package com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin;


import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ManagedTenantReplace;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.BaseTenantMapper;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncRulesData;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.StreamSimpleInfo;
import org.apache.ibatis.annotations.Param;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@ManagedTenantReplace
public interface AdminSyncPloyDetailDao extends BaseTenantMapper<SyncPloyDetailEntity, AdminSyncPloyDetailDao> {

    List<SyncPloyDetailEntity> listByTenantIdAndSourceObjs(@Param("tenantId") String tenantId, @Param("sourceObjs") List<String> sourceObjs, @Param("status") Integer status);

    List<SyncPloyDetailEntity> listByTenantIdAndStatus(@Param("tenantId") String tenantId, @Param("status") Integer status);

    SyncPloyDetailEntity getById(@Param("tenantId")String tenantId, @Param("id")String id);

    List<SyncPloyDetailEntity> listByStatus(@Param("tenantIds") List<String> tenantIds,@Param("status") Integer status);

    List<SyncPloyDetailEntity> listByTenantIdAndId(@Param("tenantId") String tenantId, @Param("streamIds") List<String> ids);

    List<SyncPloyDetailEntity> listBySource(@Param("sourceTenantId") String sourceTenantId,
                                            @Param("status") Integer status,
                                            @Param("sourceTenantType") Integer sourceTenantType,
                                            @Param("sourceObjectApiName") String sourceObjectApiName);

    List<SyncPloyDetailEntity> listBySourceTenantId(@Param("sourceTenantId") String sourceTenantId, @Param("erpFakeObjApiNames") List<String> erpFakeObjApiNames, @Param("status") Integer status, @Param("sourceObjectApiName") String sourceObjectApiName,
                                                    @Param("searchText") String searchText, @Param("offset") int offset, @Param("limit") int limit);

    List<SyncPloyDetailEntity> listByDestTenantId(@Param("destTenantId") String destTenantId, @Param("erpFakeObjApiNames") List<String> erpFakeObjApiNames, @Param("status") Integer status, @Param("destObjectApiName") String destObjectApiName,
                                                  @Param("searchText") String searchText, @Param("offset") int offset, @Param("limit") int limit);

    int countBySourceTenantId(@Param("sourceTenantId") String sourceTenantId, @Param("erpFakeObjApiNames") List<String> erpFakeObjApiNames, @Param("sourceObjectApiName") String sourceObjectApiName, @Param("status") Integer status,
                              @Param("searchText") String searchText);

    int countByDestTenantId(@Param("destTenantId") String destTenantId, @Param("erpFakeObjApiNames") List<String> erpFakeObjApiNames, @Param("destObjectApiName") String destObjectApiName, @Param("status") Integer status, @Param("searchText") String searchText);

    List<SyncPloyDetailEntity> listByPloyIdAndErpObjApiNames(@Param("tenantId") String tenantId, @Param("ployId") String ployId, @Param("erpFakeObjApiNames") List<String> erpFakeObjApiNames, @Param("offset") int offset, @Param("limit") int limit);

    int countByPloyIdAndErpObjApiNames(@Param("tenantId") String tenantId, @Param("ployId") String ployId, @Param("erpFakeObjApiNames") List<String> erpFakeObjApiNames);

    List<SyncPloyDetailEntity> listBySourceTenantIdAndDestTenantId(@Param("sourceTenantId") String sourceTenantId, @Param("destTenantId") String destTenantId);

    int updateByIdSelective(SyncPloyDetailEntity entity);

    int updateStatusById(@Param("tenantId") String tenantId, @Param("id") String id, @Param("status") Integer status, @Param("oldStatus") Integer oldStatus);

    List<String> listDistinctApiNamesByDestTenantId(@Param("tenantId") String tenantId, @Param("erpFakeObjApiNames") List<String> erpFakeObjApiNames, @Param("status") Integer status, @Param("destObjectApiName") String destObjectApiName,
                                                    @Param("offset") Integer offset, @Param("limit") Integer limit);

    List<String> listDistinctApiNamesBySourceTenantId(@Param("tenantId") String tenantId, @Param("erpFakeObjApiNames") List<String> erpFakeObjApiNames, @Param("status") Integer status, @Param("sourceObjectApiName") String sourceObjectApiName,
                                                      @Param("offset") Integer offset, @Param("limit") Integer limit);

    Integer countDistinctApiNamesByDestTenantId(@Param("tenantId") String tenantId, @Param("erpFakeObjApiNames") List<String> erpFakeObjApiNames, @Param("destObjectApiName") String destObjectApiName, @Param("status") Integer status);

    Integer countDistinctApiNamesBySourceTenantId(@Param("tenantId") String tenantId, @Param("erpFakeObjApiNames") List<String> erpFakeObjApiNames, @Param("sourceObjectApiName") String sourceObjectApiName, @Param("status") Integer status);

    Integer updateFuncApiName(@Param("id") String id, @Param("customFuncType") Integer customFuncType, @Param("customFuncApiName") String customFuncApiName, @Param("updateTime") Long updateTime);

    Integer updateValid(@Param("id") String id, @Param("isValid") Boolean isValid);

    List<SyncPloyDetailEntity> listBy(@Param("sourceObjectApiName") String sourceObjectApiName,
                                      @Param("sourceTenantType") int sourceTenantType,
                                      @Param("status") int status);

    int updateSyncRulesById(@Param("id") String id, @Param("updatedSyncRules") SyncRulesData updatedSyncRules);

    List<SyncPloyDetailEntity> listBySourceType(@Param("tenantId") String tenantId,
                                                @Param("sourceTenantType") int sourceTenantType);

    List<SyncPloyDetailEntity> listByDcIdAndObjApiName(@Param("tenantId") String tenantId, @Param("sourceDcId") String sourceDcId,
                                                       @Param("destDcId") String destDcId, @Param("sourceObjectApiName") String sourceObjectApiName,
                                                       @Param("destObjectApiName") String destObjectApiName);

    int countBySourceOrDestDcIdAndObjApiName(@Param("tenantId") String tenantId, @Param("dcId") String dcId,
                                             @Param("objApiName") String objApiName, @Param("status") Integer status,@Param("queryStr") String queryStr);

    List<SyncPloyDetailEntity> listBySourceOrDestDcIdAndObjApiName(@Param("tenantId") String tenantId, @Param("dcId") String dcId,
                                                                   @Param("objApiName") String objApiName, @Param("status") Integer status,@Param("queryStr") String queryStr,
                                                                   @Param("offset") Integer offset, @Param("limit") Integer limit);
    List<SyncPloyDetailEntity> listByDestTenantTypeAndObjApiName(@Param("tenantId") String tenantId,
                                                                 @Param("destTenantType") int destTenantType,
                                                                 @Param("destObjApiName") String destObjApiName);
    List<SyncPloyDetailEntity> listBySourceTenantTypeAndObjApiName(@Param("tenantId") String tenantId,
                                                                 @Param("sourceTenantType") int sourceTenantType,
                                                                 @Param("sourceObjApiName") String sourceObjApiName);
    List<SyncPloyDetailEntity> listBySourceTenantTypeAndObjApiNameList(@Param("tenantId") String tenantId,
                                                                   @Param("sourceTenantType") int sourceTenantType,
                                                                   @Param("sourceObjApiNameList") List<String> sourceObjApiName);
    List<SyncPloyDetailEntity> listByTenantId(@Param("tenantId") String tenantId);

    List<SyncPloyDetailEntity> listSomeFieldByTenantId(@Param("tenantId") String tenantId);

    List<SyncPloyDetailEntity> listStreamByDCIDAndObjApiName(@Param("tenantId") String tenantId,
                                                             @Param("sourceApiName") String sourceApiName, @Param("destApiName") String destApiName,@Param("sourceDataCenterId") String sourceDataCenterId,
                                                             @Param("destDataCenterId") String destDataCenterId, @Param("status")Integer status);

    void updateNameAndDcIdById(@Param("id") String id,@Param("tenantId") String tenantId,@Param("name") String name, @Param("sourceDcId") String sourceDcId, @Param("destDcId") String destDcId);

    Integer updateNameById(@Param("id") String id,@Param("tenantId") String tenantId,@Param("name") String name);
    SyncPloyDetailEntity findOne(@Param("tenantId") String tenantId,
                                 @Param("sourceObjectApiName") String sourceObjectApiName,
                                 @Param("destObjectApiName") String destObjectApiName);

    int insertList(@Param("list")List<SyncPloyDetailEntity> list);

    int deleteByTenantId(@Param("tenantId")String tenantId);

    List<SyncPloyDetailEntity> listObjMappingByTenantId(@Param("tenantId")String tenantId);

    int countByTenantId(@Param("tenantId") String tenantId);

    int countEnableByTenantId(@Param("tenantId") String tenantId);
    int countEnableByDcId(@Param("tenantId") String tenantId, @Param("dcId") String dcId);
    int countByDcId(@Param("tenantId") String tenantId, @Param("dcId") String dcId);

    List<SyncPloyDetailEntity> queryListEnableByDcIds(@Param("tenantId") String tenantId, @Param("dcIds") List<String> dcIds);

    List<SyncPloyDetailEntity> queryByDcId(@Param("tenantId") String tenantId, @Param("dcId") String dcId, @Param("status")Integer status);

    void deleteByTenantAndDcId(@Param("tenantId") String tenantId, @Param("dcId") String dcId);

    List<SyncPloyDetailEntity> queryByDcIdByCrmObjApiNames(@Param("tenantId") String tenantId, @Param("dcId") String dcId, @Param("crmObjApinames") List<String> crmObjApinames,@Param("status")Integer status);

    /**
     * 根据erp的dcId和crm的apiName统计数量
     * @return
     */
    Integer countByErpDcIdAndCrmApiName(@Param("tenantId")String tenantId,@Param("erpDcId")String erpDcId, @Param("crmObjApiName")String crmObjApiName);

    /**
     * 根据目标是CRM的某个对象取集成流企业
     * @param destObjectApiName
     * @return
     */
    List<String> queryTenantIdByDestCRMObjectApiName(@Param("destObjectApiName")String destObjectApiName);

    /**
     * 根据源是CRM的某个对象取集成流企业
     * @param sourceObjectApiName
     * @return
     */
    List<String> queryTenantIdBySourceCRMObjectApiName(@Param("sourceObjectApiName")String sourceObjectApiName);

    /**
     * 查询所有满足条件的策略明细数据，
     * @param ids 策略明细id
     * @return
     */
    List<SyncPloyDetailEntity> listByIds(@Param("ids") List<String> ids);

    /**
     * 遍历简单集成流信息
     */
    List<StreamSimpleInfo> list100SimpleByIdAfter(@Param("minId")String minId);

    /**
     * 查找Crm主对象为 objApiName, 且配置了函数的所有策略明细
     */
    List<SyncPloyDetailEntity> findCrmMasterObjFuncUsagePloy(@Param("objApiName") String objApiName, @Param("tenantIds") List<String> tenantIds, @Param("status") Integer status, @Param("offset") int offset, @Param("limit") int limit);
    /**
     * 查找Erp主对象为 objApiName, 且配置了函数的所有策略明细
     */
    List<SyncPloyDetailEntity> findErpMasterObjFuncUsagePloy(@Param("objApiName") String objApiName, @Param("tenantIds") List<String> tenantIds, @Param("status") Integer status, @Param("offset") int offset, @Param("limit") int limit);

    /**
     * 查找Erp从对象为 objApiName, 且配置了函数的所有策略明细
     */
    List<SyncPloyDetailEntity> findErpDetailObjFuncUsagePloy(@Param("objApiName") String objApiName, @Param("tenantIds") List<String> tenantIds, @Param("status") Integer status, @Param("offset") int offset, @Param("limit") int limit);
    /**
     * 查找Crm从对象为 objApiName, 且配置了函数的所有策略明细
     */
    List<SyncPloyDetailEntity> findCrmDetailObjFuncUsagePloy(@Param("objApiName") String objApiName, @Param("tenantIds") List<String> tenantIds, @Param("status") Integer status, @Param("offset") int offset, @Param("limit") int limit);

    int deleteByTenantAndId(@Param("tenantId") String tenantId, @Param("id") String id);

    /**
     * 查询前缀相同的集成流，
     */
    List<SyncPloyDetailEntity> findSyncPloyDetailByStartWithName(@Param("tenantId") String tenantId, @Param("dcId") String dcId, @Param("integrationStreamName") String integrationStreamName);

    List<String> listIdBySource(@Param("tenantId") String tenantId, @Param("sourceDcId") String sourceDcId, @Param("sourceObjectApiName") String sourceObjectApiName, @Param("status") Integer status);

    List<String> listIdByCond(@Param("tenantId") String tenantId, @Nullable @Param("erpDcId") String erpDcId, @Nullable @Param("status") Integer status);

    List<String> listTenantIdList(@Param("limit") Integer limit, @Param("minEi") String minEi);

    List<SyncPloyDetailEntity> listBySourceApiName(@Param("tenantId")String tenantId, @Param("sourceObjApiName")String sourceObjApiName);
}