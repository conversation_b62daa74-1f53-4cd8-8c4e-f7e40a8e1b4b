package com.fxiaoke.open.erpsyncdata.dbproxy.entity;

import com.fxiaoke.open.erpsyncdata.common.constant.DataReceiveTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncDataStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.DataId;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ObjApiName;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.MapListStringData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.bson.types.ObjectId;

import javax.persistence.Table;
import java.io.Serializable;

/**
 * 同步数据实例
 */
@EqualsAndHashCode(callSuper = true)
@FieldNameConstants
@Data
@DataId("sourceDataId")
@ObjApiName("sourceObjectApiName")
@Table(name = "sync_data")
public class SyncDataEntity extends BaseEntity implements Serializable {
    /** 同步数据主键id */
    private String id;
    /**
     * 企业id
     */
    private String tenantId;
    /** 源企业主键id */
    private String sourceTenantId;
    /** 企业类型 目前为CRM 1 */
    private Integer sourceTenantType;
    /** 企业类型 目前为CRM 1 */
    private Integer destTenantType;
    /** 源数据的数据事件类型 1、新增 2、修改 3、作废 {@link EventTypeEnum} */
    private Integer sourceEventType;
    /** 源企业主对象apiName */
    private String sourceObjectApiName;
    /** 源数据主键id */
    private String sourceDataId;
    /** 源数据主属性 */
    private String sourceDataName;
    /** 源主对象数据 */
    private ObjectData sourceData;
    /** 源从对象id封装 */
    private MapListStringData sourceDetailSyncDataIds = new MapListStringData();
    /** 目标企业id */
    private String destTenantId;
    /** 目标企业事件类型 1、新增 2、修改 3、作废 {@link EventTypeEnum}*/
    private Integer destEventType;
    /** 目标企业主对象apiName */
    private String destObjectApiName;
    /** 目标企数据id */
    private String destDataId;
    /** 目标主对象数据 */
    private ObjectData destData;
    /** 数据状态 {@link SyncDataStatusEnum}*/
    private Integer status;
    /** 快照id */
    private String syncPloyDetailSnapshotId;
    /** 操作人主键id */
    private String operatorId;
    /** 同步日志备注 */
    private String remark;
    /** 错误编码 */
    private String errorCode;
    /** 逻辑删除 */
    private Boolean isDeleted;
    /**
     * 需要返回的目标数据
     */
    private ObjectData needReturnDestObjectData;

    /**
     * 同步数据logId
     * @param tenantId
     */
    private String syncLogId;
    /**
     * 数据接收方式
     * @see DataReceiveTypeEnum
     */
    private Integer dataReceiveType;

    /**
     * 节点信息
     *
     */
    private NodeMsg data =new NodeMsg();

    @Data
    public static class NodeMsg{
        /**
         * 最后执行节点
         *
         */
        private String lastNodeName;
        /**
         * 最后执行节点状态(信息)
         *
         */
        private String lastNodeStatusMsg;
        /**
         * 数据体的版本（crm使用version，erp使用new_last_sync_time）
         *
         */
        private String dataVersion;
        /** 是否回写crm失败 */
        private Boolean reverseWrite2CrmFailed;
        /** 回写crm失败信息 */
        private String reverseWrite2CrmFailedRemark;
        /** 是否同步后函数执行失败 */
        private Boolean afterFuncFailed;
        /** 同步后函数执行失败信息 */
        private String afterFuncFailedRemark;
    }


    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
        this.sourceTenantId = tenantId;
        this.destTenantId = tenantId;
    }

    public void convertAndSetId(ObjectId objectId){
        this.id = objectId.toString();
    }

    @Override
    public String toString() {
        return "SyncDataEntity";
    }
}
