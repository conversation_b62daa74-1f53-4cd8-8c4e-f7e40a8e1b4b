package com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin;


import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.*;
import lombok.Data;

import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Objects;

/**
 * 同步策略对象所含详情实例
 */
@Data
@Table(name = "sync_ploy_detail")
public class SyncPloyDetailEntity extends BaseEntity implements Serializable {
    /** 策略对象所含详情id */
    private String id;
    /** 策企业id */
    @TenantID
    private String tenantId;
    /** 集成流名称 */
    private String integrationStreamName;
    /** 源数据中心 */
    private String sourceDataCenterId;
    /** 目标数据中心 */
    private String destDataCenterId;
    /**
     * 策略对象主键id
     * 已作废，仅为刷库使用
     */
    @Deprecated
    private String syncPloyId;
    /** 策略状态 1.启用 2.停用 {@link  SyncPloyDetailStatusEnum} */
    private Integer status;
    /** 企业类型 1.纷享 2.外部对接*/
    private Integer sourceTenantType;
    /**
     * 已作废，仅为刷库使用
     */
    @Deprecated
    @Transient
    private String sourceTenantId;
    /** 源企业账号ids,已作废，统一使用tenantId */
    @Deprecated
    @Transient
    private ListStringData sourceTenantIds;
    /** 源企业策略对象名称 */
    private String sourceObjectApiName;
    /** 企业类型 1.纷享 2.外部对接*/
    private Integer destTenantType;
    /**
     * 已作废，仅为刷库使用
     */
    @Deprecated
    @Transient
    private String destTenantId;
    /** 目标企业账号ids,已作废，统一使用tenantId */
    @Deprecated
    @Transient
    private ListStringData destTenantIds;
    /** 源企业策略对象名称 */
    private String destObjectApiName;
    /** 字段映射 */
    private FieldMappingsData fieldMappings;
    /** 从对象映射 */
    private DetailObjectMappingsData detailObjectMappings;
    /** 同步规则封装 */
    private SyncRulesData syncRules;
    /** 同步数据范围，即查找条件 */
    private SyncConditionsData syncConditions;
    /** 从对象数据范围，即查找条件 */
    private SyncConditionsListData detailObjectSyncConditions;
    /** 集成流节点 */
    private IntegrationStreamNodesData integrationStreamNodes;
    /** 同步前自定义函数APIName */
    private String beforeFuncApiName;
    /** 同步中自定义函数APIName */
    private String duringFuncApiName;
    /** 同步后自定义函数APIName */
    private String afterFuncApiName;
    /** 执行策略是否有效 1.有效 2.无效 */
    private Boolean isValid;
    /** 备注 */
    private String remark;

    public String getErpDataCenterId() {
        return Objects.equals(sourceTenantType, TenantTypeEnum.ERP.getType()) ? sourceDataCenterId : destDataCenterId;
    }
}
