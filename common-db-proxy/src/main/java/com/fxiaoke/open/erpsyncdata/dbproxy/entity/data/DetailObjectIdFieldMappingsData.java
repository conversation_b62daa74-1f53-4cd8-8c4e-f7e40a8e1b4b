package com.fxiaoke.open.erpsyncdata.dbproxy.entity.data;


import lombok.Data;

import java.util.ArrayList;
import java.util.List;

public class DetailObjectIdFieldMappingsData extends ArrayList<DetailObjectIdFieldMappingsData.DetailObjectIdFieldMappingData> {
    @Data
    public static class DetailObjectIdFieldMappingData {
        /** 源从对象apiName */
        private String sourceObjectApiName;
        /** 目标从对象字段apiName */
        private String sourceApiName;
        /** 中间表字段apiName */
        private String destApiName;
    }
}
