package com.fxiaoke.open.erpsyncdata.dbproxy.entity.data;

import java.util.ArrayList;

public class FieldMappingsData extends ArrayList<FieldMappingData> {

//    public static List<com.fxiaoke.open.erpsyncdata.admin.data.FieldMappingData> convert(List<FieldMappingData> sourceList){
//        if (CollectionUtils.isEmpty(sourceList)){
//            return null;
//        }
//        return sourceList.stream().map(FieldMappingsData::convert).collect(Collectors.toList());
//    }
//
//    public static com.fxiaoke.open.erpsyncdata.admin.data.FieldMappingData convert(FieldMappingData source){
//        if(source==null){
//            return null;
//        }
//        com.fxiaoke.open.erpsyncdata.admin.data.FieldMappingData result = new com.fxiaoke.open.erpsyncdata.admin.data.FieldMappingData();
//        result.setSourceApiName(source.getSourceApiName());
//        result.setSourceType(source.getSourceType());
//        result.setSourceTargetApiName(source.getSourceTargetApiName());
//        result.setSourceQuoteFieldType(source.getSourceQuoteFieldType());
//        result.setSourceQuoteRealField(source.getSourceQuoteRealField());
//        result.setSourceQuoteFieldTargetObjectApiName(source.getSourceQuoteFieldTargetObjectApiName());
//        result.setSourceQuoteFieldTargetObjectField(source.getSourceQuoteFieldTargetObjectField());
//        result.setDestApiName(source.getDestApiName());
//        result.setDestType(source.getDestType());
//        result.setDestTargetApiName(source.getDestTargetApiName());
//        result.setDestQuoteFieldType(source.getDestQuoteFieldType());
//        result.setOptionMappings(OptionMappingData.convert(source.getOptionMappings()));
//        result.setMappingType(source.getMappingType());
//        result.setFunction(source.getFunction());
//        result.setValue(source.getValue());
//        return result;
//
//    }

}
