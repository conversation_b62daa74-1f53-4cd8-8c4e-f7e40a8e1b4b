package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpFieldDataMappingDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldDataMappingEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/5/12
 */
@Component
public class ErpFieldDataMappingManager {
    @Autowired
    private ErpFieldDataMappingDao erpFieldDataMappingDao;

    @LogLevel(LogLevelEnum.DEBUG)
    @Cached(expire = 30, cacheType = CacheType.LOCAL)
    public List<ErpFieldDataMappingEntity> listNoSearch(String tenantId,
                                                 String dataCenterId,
                                                 ErpFieldTypeEnum dataType,
                                                 String fsDataId,
                                                 String erpDataId){
        return erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listNoSearch(tenantId, dataCenterId, dataType, fsDataId, erpDataId);
    }
}
