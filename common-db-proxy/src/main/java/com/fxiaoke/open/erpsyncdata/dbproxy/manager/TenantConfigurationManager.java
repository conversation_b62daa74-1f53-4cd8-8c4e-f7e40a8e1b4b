package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.fxiaoke.open.erpsyncdata.dbproxy.model.K3UltimateSignatureConfig;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.lang.Dict;
import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.notifier.support.NotifierClient;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.LogStorageRuleEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpTenantConfigurationDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpIdNumberNameKeyMapping;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.K3UltimateConfigModel;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TenantConfigHelper;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendAdminNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.config.AlertNoticeConfig;
import com.fxiaoke.open.erpsyncdata.preprocess.model.config.PloyBreakNoticeConfig;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.google.common.base.Splitter;
import com.google.common.collect.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum.BACKGROUND_DEFAULT_LANG;
import static com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager.funcEiTLocale;

/**
 * 对企业配置加一层封装方便缓存
 *
 * <AUTHOR> (^_−)☆
 * @date 2022/2/14
 */
@Component
@Slf4j
@LogLevel
public class TenantConfigurationManager {
    @Autowired
    private I18NStringManager i18NStringManager;

    @Autowired
    private ErpTenantConfigurationDao erpTenantConfigurationDao;
    /**
     * 启动服务时,会先初始化TenantConfigurationManager
     * 不加@Lazy会导致在创建Aspect之前先创建了NotificationService和内部依赖,导致aop失效
     */
    @Autowired
    @Lazy
    private NotificationService notificationService;
    @Autowired
    private ConfigCenterConfig configCenterConfig;

    /**
     * 白名单缓存
     * key ，value tenantId集合,或者其他
     */
    private static final TimedCache<String, ImmutableSet<String>> whiteSetCache = CacheUtil.newTimedCache(10000);
    /**
     * 分组的白名单缓存
     * key ，value是一个map,不可变！，比如key是企业，value是对象set
     */
    private static final TimedCache<String, ImmutableMap<String, ImmutableSet<String>>> groupWhiteSetCache = CacheUtil.newTimedCache(10000);
    /**
     * 由于tenantConfigrurationMapCache是缓存entity，每次调用多出序列化的操作，增加一个对象数据的缓存。
     * 缓存10s
     * key type，value 缓存的数据，map格式
     */
    private static final TimedCache<TenantConfigurationTypeEnum, Dict> objConfigCache = CacheUtil.newTimedCache(TimeUnit.SECONDS.toMillis(10L));

    /**
     * 因为发布环境是逐步迭代的，所以这里的先设置比较小的。等所有环境都发布了接入fast-notifer的代码，再调大时间。
     */
    private static final TimedCache<String, ConcurrentMap<String, ErpTenantConfigurationEntity>> tenantConfigrurationMapCache  =
            CacheUtil.newTimedCache(ConfigCenter.TENANT_CONFIG_MAP_CACHE_EXPIRE_TIME);

    //配置表100次读不完，说明代码有问题了。
    private static final int maxReadDBCnt = 500;
    /**为了避免频繁splitToList消耗CPU, 提取两个成员变量。*/
    private long isTenantNotChangeBigDecimalLastUpdateTime = 0;
    private List<String> isTenantNotChangeBigDecimalEiList = new ArrayList<>();

    /**
     * 清理所有缓存，主要用于测试场景
     */
    public void clearCache() {
        tenantConfigrurationMapCache.clear();
        whiteSetCache.clear();
        groupWhiteSetCache.clear();
        objConfigCache.clear();
    }

    static {
        //定时清理，不然超时后不会自动清理。10分钟
        tenantConfigrurationMapCache.schedulePrune(TimeUnit.MINUTES.toMillis(10));
    }


    @PostConstruct
    void initCache() {
        log.info("load TENANT_CONFIG_MAP_CACHE_EXPIRE_TIME:{} minute", ConfigCenter.TENANT_CONFIG_MAP_CACHE_EXPIRE_TIME / (60 * 1000L));
        //读取所有配置
        List<ErpTenantConfigurationEntity> allTenantErpTenantConfigurationEntityList = readAllConfig();
        List<String> allTenantID = allTenantErpTenantConfigurationEntityList.stream().map(s -> s.getTenantId()).collect(Collectors.toList());
        for (String ei : allTenantID) {
            //防止异步操作map出错
            tenantConfigrurationMapCache.put(ei, Maps.newConcurrentMap());
        }

        allTenantErpTenantConfigurationEntityList.forEach(item -> {
            String ei = item.getTenantId();
            ConcurrentMap<String, ErpTenantConfigurationEntity> oneTenantConfigMap = tenantConfigrurationMapCache.get(ei);
            String cacheKey = TenantConfigHelper.buildKey(item.getDataCenterId(), item.getType());
            ErpTenantConfigurationEntity oldConfig = oneTenantConfigMap.putIfAbsent(cacheKey, item);
        });

        /**注册fast-notifyer的监听器。 当别的pod修改了配置表，其它的pod都能接到通知。*/
        NotifierClient.register(ErpTenantConfigurationDao.ErpTenantConfigurationChangeNotifyRoom, (message) -> {
            log.info("listen tenant:{} configuration change ", message.getContent());
            /**message.getContent()就是配置发生变化的tenantid**/
            reloadTenantConfigurationCache(message.getContent());
        });

        setI18NStringManager();
    }

    public  void setI18NStringManager() {
        try {
            funcEiTLocale = (tenantId) -> {
                ErpTenantConfigurationEntity entity = findOne("0", "0", ErpChannelEnum.ALL.name(), BACKGROUND_DEFAULT_LANG.name());
                if (null != entity && entity.getConfiguration() != null) {
                    Map<String, String> map = JacksonUtil.fromJson(entity.getConfiguration(), Map.class);
                    if (map.containsKey(tenantId)) {
                        return map.get(tenantId);
                    }
                }
                return null;
            };
            log.info("setI18NStringManager init funcEiTLocale success");
        } catch (Exception e) {
            log.error("setI18NStringManager init funcEiTLocale error", e);
        }
    };

    private void reloadTenantConfigurationCache(String tenantId) {
        ConcurrentMap<String, ErpTenantConfigurationEntity> newMap = buildTenantMapFromDb(tenantId);
        //每次重载就延长超时时间
        ConcurrentMap<String, ErpTenantConfigurationEntity> oldMap = tenantConfigrurationMapCache.get(tenantId);
        //更新配置
        tenantConfigrurationMapCache.put(tenantId,newMap);
    }

    private List<ErpTenantConfigurationEntity> readAllConfig() {
        List<ErpTenantConfigurationEntity> allTenantErpTenantConfigurationEntityList = Lists.newArrayList();
        int offset = 0;
        int limit = 100;
        int readCnt = 0;
        while (readCnt < maxReadDBCnt) {
            readCnt++;
            List<ErpTenantConfigurationEntity> tmp = erpTenantConfigurationDao
                    .setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("0")).findOnePage(offset, limit);

            if (null == tmp || tmp.size() == 0) {
                break;
            }
            offset += tmp.size();
            allTenantErpTenantConfigurationEntityList.addAll(tmp);
        }
        if (readCnt >= maxReadDBCnt) {
            SendAdminNoticeArg sendAdminNoticeArg = SendAdminNoticeArg.builder().tenantId("0").dcId("NoUse").msgTitle(i18NStringManager.getByEi(I18NStringEnum.s3747, null))
                    .msg(i18NStringManager.getByEi(I18NStringEnum.s3748, null) + maxReadDBCnt).build();
            notificationService.sendSuperAdminNotice(sendAdminNoticeArg);
        }
        return allTenantErpTenantConfigurationEntityList;
    }

    /**
     * @param channel 实际上无用，但是调用地方比较多，不去掉
     */
    @LogLevel(LogLevelEnum.DEBUG)
    public ErpTenantConfigurationEntity findOne(String tenantId, String dataCenterId, String channel, String type) {
        String cacheKey = TenantConfigHelper.buildKey(dataCenterId, type);
        //类似于putIfAbsent，其他线程会等待.
        Map<String, ErpTenantConfigurationEntity> eiTenantConfigurationMap = tenantConfigrurationMapCache.get(
                tenantId,
                false,
                //从缓存读取不到，则从db加载，获取不到也返回空map。这样完全没有配置的企业，也不会持续读数据库。
                () -> buildTenantMapFromDb(tenantId));
        ErpTenantConfigurationEntity result = eiTenantConfigurationMap.get(cacheKey);
        log.debug("trace find configuration succ,for ei:{},dataCenterId:{}, type:{}, result:{}",
                tenantId, dataCenterId, type, result);
        return result;
    }

    public List<ErpTenantConfigurationEntity> findAll(String tenantId) {
        Map<String, ErpTenantConfigurationEntity> eiTenantConfigurationMap = tenantConfigrurationMapCache.get(tenantId,
                false,
                () -> buildTenantMapFromDb(tenantId));
        return Lists.newArrayList(eiTenantConfigurationMap.values());
    }

    public int insert(String tenantId, ErpTenantConfigurationEntity entity) {
        long now = System.currentTimeMillis();
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
        int ret = erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(entity);
        return ret;
    }


    /**
     * 尽量别使用这个更新，可以吧type和channel等信息都给更新了。请使用updateConfig
     */
    public int updateById(String tenantId, ErpTenantConfigurationEntity entity) {
        entity.setUpdateTime(System.currentTimeMillis());
        int ret = erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateById(entity);
        return ret;
    }


    public void updateGlobalConfig(String type, String configuration) {
        updateConfig("0", "0", "ALL", type, configuration);
    }

    public void updateConfig(String tenantId, String dataCenterId, String channel, String type, String configuration) {
        ErpTenantConfigurationEntity configurationEntity = erpTenantConfigurationDao.findOneNoCache(tenantId,
                dataCenterId,
                channel,
                type);
        if (configurationEntity != null) {
            configurationEntity.setConfiguration(configuration);
            updateById(tenantId, configurationEntity);
            return;
        }
        ErpTenantConfigurationEntity entity = ErpTenantConfigurationEntity.builder()
                .id(IdGenerator.get())
                .tenantId(tenantId)
                .dataCenterId(dataCenterId)
                .channel(channel)
                .type(type)
                .configuration(configuration)
                .createTime(System.currentTimeMillis())
                .updateTime(System.currentTimeMillis())
                .build();
        int count = insert(tenantId, entity);
    }


    public void upsertConfigIgnoreChannel(String tenantId, String dataCenterId, TenantConfigurationTypeEnum type, String configuration) {
        type.checkFormat(tenantId,configuration);
        //查询时，忽略channel
        ErpTenantConfigurationEntity configurationEntity = erpTenantConfigurationDao.findOneNoCache(tenantId,
                dataCenterId,
                null,
                type.name());
        if (configurationEntity != null) {
            configurationEntity.setConfiguration(configuration);
            updateById(tenantId, configurationEntity);
            return;
        }
        ErpTenantConfigurationEntity entity = ErpTenantConfigurationEntity.builder()
                .id(IdGenerator.get())
                .tenantId(tenantId)
                .dataCenterId(dataCenterId)
                .channel(ErpChannelEnum.ALL.name())
                .type(type.name())
                .configuration(configuration)
                .createTime(System.currentTimeMillis())
                .updateTime(System.currentTimeMillis())
                .build();
        int count = insert(tenantId, entity);
    }

    public int updateConfig(String tenantId, String id, String config, TenantConfigurationTypeEnum type) {
        return erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateConfig(tenantId, id, config, System.currentTimeMillis(), type);
    }

    public List<ErpTenantConfigurationEntity> queryList(String tenantId, ErpTenantConfigurationEntity entity) {
        return erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(entity);
    }

    public int deleteById(String tenantId, String id) {
        int ret = erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteById(id);
        return ret;
    }

    public int deleteByTenantIdWithId(String tenantId, String id) {
        int ret = erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteById(tenantId, id);
        return ret;
    }

    public int deleteByTenantIdWithDataCenterId(String tenantId, String dataCenterId) {
        return erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteByDataCenterId(tenantId, dataCenterId);
    }

    public int deleteByTenantIdWithDataCenterId(String tenantId, List<String> dataCenterIds) {
        return erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).batchDeleteByDataCenterId(tenantId, dataCenterIds);
    }

    public int deleteByTenantId(String destDbRoute, String tenantId) {
        int ret = erpTenantConfigurationDao.setTenantId(destDbRoute).deleteByTenantId(tenantId);
        return ret;
    }

    /**
     * 获取全局配置，解析为int，缓存120s
     *
     * @param type
     * @param defaultValue 默认值
     * @return
     */
    public Integer queryGlobalIntConfig(String type, Integer defaultValue) {
        ErpTenantConfigurationEntity configurationEntity = findOne("0", "0", ErpChannelEnum.ALL.name(), type);
        if (configurationEntity == null || configurationEntity.getConfiguration() == null) {
            return defaultValue;
        }
        try {
            int i = Integer.parseInt(configurationEntity.getConfiguration());
            return i;
        } catch (Exception e) {
            log.error("parse int config error,config:{}", configurationEntity, e);
            return defaultValue;
        }
    }



    /**
     * 获取全局配置，缓存120s
     *
     * @param type
     * @param defaultValue 默认值
     * @return
     */
    public Double queryGlobalDoubleConfig(String type, Double defaultValue) {
        ErpTenantConfigurationEntity configurationEntity = findOne("0", "0", ErpChannelEnum.ALL.name(), type);
        if (configurationEntity == null || configurationEntity.getConfiguration() == null) {
            return defaultValue;
        }
        try {
            double i = Double.parseDouble(configurationEntity.getConfiguration());
            return i;
        } catch (Exception e) {
            log.error("parse int config error,config:{}", configurationEntity, e);
            return defaultValue;
        }
    }


    /**
     * 获取全局配置
     *
     * @param type
     * @return
     */
    @LogLevel(LogLevelEnum.DEBUG)
    public ErpTenantConfigurationEntity findGlobal(String type) {
        return findOne("0", "0", ErpChannelEnum.ALL.name(), type);
    }

    public ErpTenantConfigurationEntity findById(String configID) {
        return erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("0")).
                findById(CommonConstant.GLOBAL_DISABLE_DYNAMIC_TENANTS_CONFIG_ID);
    }

    ;

    @Cached(expire = 120, cacheType = CacheType.LOCAL, localLimit = 1000)
    public Integer getIntegerValue(String tenantId, TenantConfigurationTypeEnum type) {
        ErpTenantConfigurationEntity entity = findOne(tenantId, "0", ErpChannelEnum.ALL.name(), type.name());
        if (entity != null && entity.getConfiguration() != null) {
            log.info("trace param threadnum,ei:{}, config:{}", tenantId, entity.getConfiguration());
            return Integer.valueOf(entity.getConfiguration());
        }
        return null;
    }


    /**
     * 获取全局配置
     *
     * @param tenantId
     * @param dcId
     * @param channel
     * @param type
     * @return
     */
    public ErpTenantConfigurationEntity findNoCache(String tenantId, String dcId, String channel, TenantConfigurationTypeEnum type) {
        return erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .findOneNoCache(tenantId, dcId, channel, type.name());
    }

    public ErpTenantConfigurationEntity findNoCache(String tenantId, String dcId, TenantConfigurationTypeEnum type) {
        return erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .findOneNoCache(tenantId, dcId, null, type.name());
    }

    /**
     * 获取Double配置值
     * 配置值类似于{"671268":20, "735430":5000,"732208":100}
     *
     * @param type 配置类型
     * @param keys 取key值，按顺序,比如 tenantId,channel,default
     * @return 当不存在值时，返回defaultValue。defaultValue可以为null，但是配置值不能为null
     */
    @LogLevel(LogLevelEnum.TRACE)
    public Double getDoubleConfig(TenantConfigurationTypeEnum type, Double defaultValue, String... keys) {
        if (keys == null) {
            throw new ErpSyncDataException("Key can not be null");
        }
        Double result = getDoubleConfig(type, keys);
        return result != null ? result : defaultValue;
    }

    public Double getDoubleConfig(TenantConfigurationTypeEnum type, String... keys) {
        if (keys == null) {
            throw new ErpSyncDataException("Key can not be null");
        }

        //查本地缓存
        Dict objCache = objConfigCache.get(type, false);
        if (objCache == null) {
            //从配置取出来更新到对象缓存里
            Map<String, Double> map;
            ErpTenantConfigurationEntity configurationEntity = findOne("0", "0", ErpChannelEnum.ALL.name(), type.name());
            if (configurationEntity == null || configurationEntity.getConfiguration() == null) {
                map = new HashMap<>();
            } else {
                try {
                    map = JacksonUtil.fromJson(configurationEntity.getConfiguration(), new TypeReference<Map<String, Double>>() {
                    });
                } catch (ErpSyncDataException e) {
                    log.error("parse map config error,config:{}", configurationEntity);
                    map = new HashMap<>();
                }
            }
            objCache = new Dict();
            objCache.putAll(map);
            objConfigCache.put(type, objCache);
        }
        for (String key : keys) {
            Double obj = objCache.getDouble(key);
            if (obj != null) {
                return obj;
            }
        }
        return null;
    }


    /**
     * 以后白名单的配置，请尽量走这个接口，方便统一配置缓存等。
     * 长期维持的配置适用
     * 设置本地缓存，防止当出现短时间大量调用，redis负载升高
     *
     * @param key                         tenantId  or 其他
     * @return 是否在白名单里.其实也可能是黑名单。。。
     */
    @LogLevel(LogLevelEnum.DEBUG)
    public boolean inWhiteList(String key, TenantConfigurationTypeEnum tenantConfigurationTypeEnum) {
        return getWhiteList(tenantConfigurationTypeEnum).contains(key);
    }

    /**
     * 默认以;分割
     * @param tenantConfigurationTypeEnum
     * @return
     */
    @LogLevel(LogLevelEnum.DEBUG)
    public ImmutableSet<String> getWhiteList(TenantConfigurationTypeEnum tenantConfigurationTypeEnum) {
        return getWhiteList(tenantConfigurationTypeEnum,";");
    }


    /**
     *
     * @param tenantConfigurationTypeEnum
     * @param separator 为了兼容一些非默认分割的历史数据
     * @return
     */
    @LogLevel(LogLevelEnum.DEBUG)
    @NotNull
    public ImmutableSet<String> getWhiteList(TenantConfigurationTypeEnum tenantConfigurationTypeEnum,String separator) {
        //本地缓存,不刷新访问时间，10s超时移除
        String type = tenantConfigurationTypeEnum.name();
        ImmutableSet<String> whiteSet = whiteSetCache.get(type, false);
        if (whiteSet != null) {
            return whiteSet;
        }
        ErpTenantConfigurationEntity config = findOne("0", "0", ErpChannelEnum.ALL.name(), type);
        if (config != null && config.getConfiguration() != null) {
            try {
                String configuration = config.getConfiguration();
                whiteSet = ImmutableSet.copyOf(Splitter.on(separator).split(configuration));
            } catch (Exception e) {
                log.error("split config error,config:{}", config, e);
            }
        }
        if (whiteSet == null) {
            whiteSet = ImmutableSet.of();
        }
        whiteSetCache.put(type, whiteSet);
        return whiteSet;
    }

    /**
     * 分组的白名单
     * @param groupKey 一般为tenantId
     * @param key 可以为对象apiName等
     * @return 是否在白名单里.其实也可能是黑名单。。。
     */
    @LogLevel(LogLevelEnum.DEBUG)
    public Boolean inGroupWhiteList(String groupKey, String key, TenantConfigurationTypeEnum tenantConfigurationTypeEnum) {
        ImmutableMap<String, ImmutableSet<String>> groupWhiteMap = getGroupWhiteMap(tenantConfigurationTypeEnum);
        ImmutableSet<String> keySet = groupWhiteMap.get(groupKey);
        return keySet != null && keySet.contains(key);
    }

    @LogLevel(LogLevelEnum.DEBUG)
    public ImmutableMap<String, ImmutableSet<String>> getGroupWhiteMap(TenantConfigurationTypeEnum tenantConfigurationTypeEnum) {
        //本地缓存,不刷新访问时间，10s超时移除
        String type = tenantConfigurationTypeEnum.name();
        ImmutableMap<java.lang.String, ImmutableSet<java.lang.String>> groupWhileSetMap = groupWhiteSetCache.get(type, false);
        if (groupWhileSetMap != null) {
            return groupWhileSetMap;
        }
        ErpTenantConfigurationEntity config = findOne("0", "0", ErpChannelEnum.ALL.name(), type);
        if (config != null && config.getConfiguration() != null) {
            try {
                String configuration = config.getConfiguration();
                HashMap<String, HashSet<String>> groupWhileSetMap1 = JacksonUtil.fromJson(configuration, new TypeReference<HashMap<String, HashSet<String>>>() {
                });
                HashMap<String, ImmutableSet<String>> groupWhileSetMap2 = new HashMap<>();
                groupWhileSetMap1.forEach((k, v) -> groupWhileSetMap2.put(k, ImmutableSet.copyOf(v)));
                groupWhileSetMap = ImmutableMap.copyOf(groupWhileSetMap2);
            } catch (Exception e) {
                log.error("parse group white config error,config:{}", config, e);
            }
        }
        if (groupWhileSetMap == null) {
            groupWhileSetMap = ImmutableMap.of();
        }
        groupWhiteSetCache.put(type, groupWhileSetMap);
        return groupWhileSetMap;
    }

    @LogLevel(LogLevelEnum.DEBUG)
    @Cached(expire = 120, cacheType = CacheType.LOCAL, localLimit = 300)
    public Boolean getUseViewInterFace(String tenantId, String dcId, String masterApiName) {
        //配置
        ErpTenantConfigurationEntity config = findOne(tenantId, dcId, ErpChannelEnum.ERP_K3CLOUD.name(), TenantConfigurationTypeEnum.USE_BILLQUERY_INTERFACE_TO_VIEW.name());
        List<String> objList = Lists.newArrayList();
        if (config != null && config.getConfiguration() != null) {
            objList = JSONObject.parseArray(config.getConfiguration(), String.class);
        }
        Boolean useViewInterface = false;
        if (objList.contains("*") || objList.contains(masterApiName)) {//走view接口
            useViewInterface = true;
        }
        return useViewInterface;
    }

    @LogLevel(LogLevelEnum.DEBUG)
    public Set<String> getVipTenantIds() {
        return ConfigCenter.VIP_ENVIROMENT_TENANT;
    }

    @LogLevel(LogLevelEnum.DEBUG)
    public Set<String> getGrayTenantIds() {
        return ConfigCenter.GRAY_TENANTS;
    }

    @LogLevel(LogLevelEnum.DEBUG)
    @Cached(expire = 300, cacheType = CacheType.LOCAL, localLimit = 300)
    public Set<String> getColorLogTenantIds() {
        ErpTenantConfigurationEntity configuration = findGlobal(TenantConfigurationTypeEnum.COLOR_EI_LOG_LIST.name());
        if (configuration == null || StringUtils.isEmpty(configuration.getConfiguration())) {
            return Sets.newHashSet();
        }
        return ImmutableSet.copyOf(Splitter.on(";").split(configuration.getConfiguration()));
    }

    /**
     * @return
     */
    @LogLevel(LogLevelEnum.DEBUG)
    @Cached(expire = 120, cacheType = CacheType.LOCAL, localLimit = 300)
    public Map<String, List<String>> getConfigRouteTenant() {
        //配置
        ErpTenantConfigurationEntity config = erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("all")).findOne("all", "0", ErpChannelEnum.ALL.name(), TenantConfigurationTypeEnum.CONFIG_ROUTE_TENANT.name());
        if (config == null || StringUtils.isBlank(config.getConfiguration())) {
            return new HashMap<>();
        }
        Map<String, List<String>> map;
        try {
            map = JacksonUtil.fromJson(config.getConfiguration(), new TypeReference<Map<String, List<String>>>() {
            });
        } catch (ErpSyncDataException e) {
            log.error("parse map config error,config:{}", config);
            map = new HashMap<>();
        }
        return map;
    }

    @LogLevel(LogLevelEnum.DEBUG)
    public Boolean updateConfigRouteTenant(Map<String, List<String>> value) {
        //配置
        ErpTenantConfigurationEntity config = findOne("all", "0", ErpChannelEnum.ALL.name(), TenantConfigurationTypeEnum.CONFIG_ROUTE_TENANT.name());
        if(config==null){
            ErpTenantConfigurationEntity entity = ErpTenantConfigurationEntity.builder()
                    .id(IdGenerator.get())
                    .tenantId("all")
                    .dataCenterId("0")
                    .channel(ErpChannelEnum.ALL.name())
                    .type(TenantConfigurationTypeEnum.CONFIG_ROUTE_TENANT.name())
                    .configuration(JacksonUtil.toJson(value))
                    .createTime(System.currentTimeMillis())
                    .updateTime(System.currentTimeMillis())
                    .build();
            int count = insert(DataBaseBatchIndexUtil.notTenantId, entity);
            return count==1;
        }
        config.setConfiguration(JacksonUtil.toJson(value));
        config.setUpdateTime(System.currentTimeMillis());
        int update = erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("all")).updateById(config);
        return update == 1;
    }

    /**
     * 缓存告警配置到本地，避免过多序列化
     *
     * @param tenantId
     * @param dcId
     * @return 未配置时返回null
     */
    @Cached(name = "alertNoticeConfig", cacheType = CacheType.LOCAL, localLimit = 1000, expire = 2, timeUnit = TimeUnit.MINUTES)
    @LogLevel(LogLevelEnum.TRACE)
    public AlertNoticeConfig getAlertNoticeConfig(String tenantId, String dcId) {
        return getAlertNoticeConfigNoCache(tenantId, dcId);
    }

    @LogLevel(LogLevelEnum.TRACE)
    public AlertNoticeConfig getAlertNoticeConfigNoCache(String tenantId, String dcId) {
        ErpTenantConfigurationEntity config = findNoCache(tenantId, dcId, TenantConfigurationTypeEnum.ALERT_NOTICE);
        AlertNoticeConfig noticeConfig;
        if (config != null) {
            noticeConfig = JacksonUtil.fromJson(config.getConfiguration(), AlertNoticeConfig.class);
            noticeConfig.setId(config.getId());
        } else {
            //未创建时，返回默认值，但不插入配置表
            noticeConfig = new AlertNoticeConfig();
            //默认CRM管理员
            noticeConfig.setRoles(Lists.newArrayList("00000000000000000000000000000006"));
        }
        if (noticeConfig.getPollingErpAlertThresholds() == null) {
            noticeConfig.setPollingErpAlertThresholds(ConfigCenter.POLLING_ALERT_THRESHOLDS);
        }
        if (noticeConfig.getFailedIncrementAlertThresholds() == null) {
            noticeConfig.setFailedIncrementAlertThresholds(ConfigCenter.FAILED_INCREMENT_ALERT_THRESHOLDS);

        }
        return noticeConfig;
    }


    /**
     * 缓存告警配置到本地，避免过多序列化
     *
     * @param tenantId
     * @param dcId
     * @return 未配置时返回null
     */
    @Cached(name = "ployBreakConfig", cacheType = CacheType.LOCAL, localLimit = 1000, expire = 2, timeUnit = TimeUnit.MINUTES)
    @LogLevel(LogLevelEnum.TRACE)
    public PloyBreakNoticeConfig getPloyBreakNoticeConfig(String tenantId, String dcId) {
        return getPloyBreakNoticeConfigNoCache(tenantId, dcId);
    }

    @LogLevel(LogLevelEnum.TRACE)
    public PloyBreakNoticeConfig getPloyBreakNoticeConfigNoCache(String tenantId, String dcId) {
        ErpTenantConfigurationEntity config = findNoCache(tenantId, dcId, TenantConfigurationTypeEnum.BREAK_PLOY_NOTICE);
        PloyBreakNoticeConfig noticeConfig;
        if (config != null) {
            noticeConfig = JacksonUtil.fromJson(config.getConfiguration(), PloyBreakNoticeConfig.class);
            noticeConfig.setId(config.getId());
        } else {
            //未创建时，返回默认值，但不插入配置表
            noticeConfig = new PloyBreakNoticeConfig();
            //默认CRM管理员
            noticeConfig.setRoles(Lists.newArrayList("00000000000000000000000000000006"));
        }
        if (noticeConfig.getFailedIncrementBreakThresholds() == null) {
            noticeConfig.setFailedIncrementBreakThresholds(ConfigCenter.FAILED_INCREMENT_BREAK_THRESHOLDS);
        }
        if (noticeConfig.getGetByIdsFailCount() == null) {
            Long breakCount = configCenterConfig.getGetByIdBreakCount(tenantId, dcId, null);
            if (breakCount == 0) {
                breakCount = configCenterConfig.getGetByIdBreakCount();
            }
            noticeConfig.setGetByIdsFailCount(breakCount);
        }
        return noticeConfig;
    }


    private ConcurrentMap<String, ErpTenantConfigurationEntity> buildTenantMapFromDb(String tenantId) {
        ErpTenantConfigurationEntity queryArg = new ErpTenantConfigurationEntity();
        queryArg.setTenantId(tenantId);
        List<ErpTenantConfigurationEntity> resultList = erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(queryArg);
        log.debug("trace getTenantConfigurationCache read db for ei:{}  ", tenantId);
        /**Map<cacheKey, ErpTenantConfigurationEntity>, String cacheKey = String.format(cacheKeyFormat,dataCenterId,type);**/
        ConcurrentMap<String, ErpTenantConfigurationEntity> map = resultList.stream()
                .collect(Collectors.toConcurrentMap(k ->
                        TenantConfigHelper.buildKey(k.getDataCenterId(), k.getType()), v -> v));
        return map;
    }


    @LogLevel(LogLevelEnum.TRACE)
    @Cached(expire = 600, cacheType = CacheType.LOCAL)
    public Set<String> getNotOfferGetbyidTenantAndObjAPISet(String tenantId, String dataCenterId) {
        ErpTenantConfigurationEntity result = findOne(tenantId, dataCenterId, null, TenantConfigurationTypeEnum.NOT_OFFER_GETBYID_INTERFACE_TENANTS.name());
        if ((null == result) || (null == result.getConfiguration())) {
            return Sets.newHashSet();
        }
        Set<String> tenantSet = ImmutableSet.copyOf(Splitter.on(";").split(result.getConfiguration()));
        return tenantSet;
    }

    @LogLevel(LogLevelEnum.TRACE)
    @Cached(expire = 300, cacheType = CacheType.LOCAL)
    public List<String> getSaleOrderPlanRemoveFEntryIdEiList() {
        ErpTenantConfigurationEntity result = findOne("0", "0", "ERP_K3CLOUD",
                TenantConfigurationTypeEnum.SALE_ORDER_PLAN_REMOVE_FENTRY_ID_EI_LIST.name());
        if (result == null || StringUtils.isEmpty(result.getConfiguration())) {
            return new ArrayList<>();
        }
        return Splitter.on(";").splitToList(result.getConfiguration());
    }

    @LogLevel(LogLevelEnum.TRACE)
    public Map<String, Long> getErpReSyncDataCollectionConfig() {
        //配置
        ErpTenantConfigurationEntity config = erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("0"))
                .findOne("0", "0", "0", TenantConfigurationTypeEnum.ERP_RE_SYNC_DATA_CONFIG.name());
        if (config == null || StringUtils.isBlank(config.getConfiguration())) {
            return Maps.newHashMap();
        }
        Map<String, Long> map;
        try {
            map = JacksonUtil.fromJson(config.getConfiguration(), new TypeReference<Map<String, Long>>() {
            });
        } catch (ErpSyncDataException e) {
            log.error("getErpReSyncDataCollectionConfig Exception e={}", e);
            map = Maps.newHashMap();
        }
        return map;
    }
    @LogLevel(LogLevelEnum.TRACE)
    public Map<String, Long> getReSyncDataNodeMsgCollectionConfig() {
        //配置
        ErpTenantConfigurationEntity config = erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("0"))
                .findOne("0", "0", "0", TenantConfigurationTypeEnum.RE_SYNC_DATA_NODE_MSG_CONFIG.name());
        if (config == null || StringUtils.isBlank(config.getConfiguration())) {
            return Maps.newHashMap();
        }
        Map<String, Long> map;
        try {
            map = JacksonUtil.fromJson(config.getConfiguration(), new TypeReference<Map<String, Long>>() {
            });
        } catch (ErpSyncDataException e) {
            log.error("getReSyncDataNodeMsgCollectionConfig Exception e={}", e);
            map = Maps.newHashMap();
        }
        return map;
    }


    /**
     * 是否忽略该事件
     */
    @LogLevel(LogLevelEnum.TRACE)
    public boolean isPassDataSource(String tenantId, String sourceApiName, Integer sourceEventType, String dataSource) {
        //根据配置丢弃
        Map<String, List<String>> needPassDataSourceConfig = getNeedPassDataSourceConfig(tenantId);
        return ifPassDataSource(tenantId, sourceApiName, sourceEventType, dataSource, needPassDataSourceConfig);
    }

    public Boolean ifPassDataSource(String tenantId, String sourceApiName, Integer sourceEventType, String dataSource, Map<String, List<String>> needPassDataSourceConfig) {
        String tenantObjEvent = tenantId + "_" + sourceApiName + "_" + sourceEventType;
        String tenantObj = tenantId + "_" + sourceApiName;
        if (sourceEventType!=null&&needPassDataSourceConfig.containsKey(tenantObjEvent)) {//企业对象动作
            if (needPassDataSourceConfig.get(tenantObjEvent).contains(dataSource)) {
                return true;
            }
        } else if (needPassDataSourceConfig.containsKey(tenantObj)) {//企业对象
            if (needPassDataSourceConfig.get(tenantObj).contains(dataSource)) {
                return true;
            }
        } else if (needPassDataSourceConfig.containsKey(tenantId)) {//企业
            if (needPassDataSourceConfig.get(tenantId).contains(dataSource)) {
                return true;
            }
        } else if (needPassDataSourceConfig.containsKey("*")) {//所有企业
            if (needPassDataSourceConfig.get("*").contains(dataSource)) {
                return true;
            }
        }
        return false;
    }

    @LogLevel(LogLevelEnum.TRACE)
    public Map<String, List<String>> getNeedPassDataSourceConfig(String tenantId) {//在消费paasmq的地方用到，抛异常会影响消费mq
        try {
            ErpTenantConfigurationEntity config = findOne(tenantId, CommonConstant.configUniformIdentifier, CommonConstant.configUniformIdentifier,
                    TenantConfigurationTypeEnum.TENANT_NEED_PASS_DATASOURCE.name());
            if (config != null) {//企业
                if(StringUtils.isNotBlank(config.getConfiguration())){
                    return JacksonUtil.fromJson(config.getConfiguration(), Map.class);
                }else{
                    return Maps.newHashMap();
                }
            }
            config = findOne(CommonConstant.configUniformIdentifier, CommonConstant.configUniformIdentifier, CommonConstant.configUniformIdentifier,
                    TenantConfigurationTypeEnum.TENANT_NEED_PASS_DATASOURCE.name());
            if (config == null || StringUtils.isEmpty(config.getConfiguration())) {//历史d数据
                return Maps.newHashMap();
            }
            return JacksonUtil.fromJson(config.getConfiguration(), Map.class);
        } catch (Exception e) {
            log.error("getNeedPassDataSourceConfig Exception e={}", e);
        }
        return Maps.newHashMap();
    }

    @LogLevel(LogLevelEnum.TRACE)
    public List<String> getQuickPassSpecialFieldTenantConfig() {
        try {
            ErpTenantConfigurationEntity config = findOne(CommonConstant.configUniformIdentifier, CommonConstant.configUniformIdentifier, CommonConstant.configUniformIdentifier,
                    TenantConfigurationTypeEnum.TENANT_NEED_PASS_SPECIAL_FIELD.name());
            if (config != null && !StringUtils.isEmpty(config.getConfiguration())) {
                String[] split = config.getConfiguration().split(";");
                return Lists.newArrayList(split);
            }
        } catch (Exception e) {
            log.error("getQuickPassSpecialFieldTenantConfig Exception e={}", e);
        }
        return Lists.newArrayList();
    }

    /**
     * 容器限速，byte
     * {"defaultTenant":100000,"allTenant":100000000,"81243":200000}
     * defaultTenant：默认单企业；allTenant:所有企业总限速
     * @return
     */
    @LogLevel(LogLevelEnum.TRACE)
    public Map<String, Integer> getFileUploadRateLimit() {
        try {
            ErpTenantConfigurationEntity config = findOne(CommonConstant.configUniformIdentifier, CommonConstant.configUniformIdentifier, CommonConstant.configUniformIdentifier,
                    TenantConfigurationTypeEnum.TENANT_FILE_UPLOAD_RATE_LIMIT.name());
            if (config == null || StringUtils.isEmpty(config.getConfiguration())) {
                return Maps.newHashMap();
            }
            return JacksonUtil.fromJson(config.getConfiguration(), Map.class);
        } catch (Exception e) {
            log.error("getFileUploadRateLimit Exception e={}", e);
        }
        return Maps.newHashMap();
    }

    @LogLevel(LogLevelEnum.TRACE)
    public List<String> getNeedProcessNodesTenantConfig() {
        try {
            ErpTenantConfigurationEntity config = findOne(CommonConstant.configUniformIdentifier, CommonConstant.configUniformIdentifier, CommonConstant.configUniformIdentifier,
                    TenantConfigurationTypeEnum.TENANT_NEED_SEND_NODES_MSG.name());
            if (config != null && !StringUtils.isEmpty(config.getConfiguration())) {
                String[] split = config.getConfiguration().split(";");
                return Lists.newArrayList(split);
            }
        } catch (Exception e) {
            log.error("getNeedSendNodesTenantConfig Exception e={}", e);
        }
        return Lists.newArrayList();
    }

    @LogLevel(LogLevelEnum.TRACE)
    public Map<String, Long> getDataNodesTimeOutConfig() {
        try {
            ErpTenantConfigurationEntity config = findOne(CommonConstant.configUniformIdentifier, CommonConstant.configUniformIdentifier, CommonConstant.configUniformIdentifier,
                    TenantConfigurationTypeEnum.TENANT_DATA_NODES_TIME_OUT.name());
            if (config == null || StringUtils.isEmpty(config.getConfiguration())) {
                return Maps.newHashMap();
            }
            return JacksonUtil.fromJson(config.getConfiguration(), new TypeReference<Map<String, Long>>() {
            });
        } catch (Exception e) {
            log.error("getNeedPassDataSourceConfig Exception e={}", e);
        }
        return Maps.newHashMap();
    }

    @LogLevel(LogLevelEnum.TRACE)
    public long getTenantDispatcherRemainMaximum(String tenantId) {//倍数
        try {
            ErpTenantConfigurationEntity config = findOne(CommonConstant.configUniformIdentifier, CommonConstant.configUniformIdentifier, CommonConstant.configUniformIdentifier,
                    TenantConfigurationTypeEnum.TENANT_DISPATCHER_REMAIN_MAXIMUM.name());
            if (config == null || StringUtils.isEmpty(config.getConfiguration())) {
                return 3L;
            }
            Map<String, Long> map = JacksonUtil.fromJson(config.getConfiguration(), new TypeReference<Map<String, Long>>() {
            });
            if (map != null) {
                if (map.containsKey(tenantId) && map.get(tenantId) != null) {
                    return map.get(tenantId);
                }
                if (map.containsKey("default") && map.get("default") != null) {
                    return map.get("default");
                }
            }
        } catch (Exception e) {
            log.error("getTenantDispatcherRemainMaximum Exception e={}", e);
        }
        return 3L;
    }

    /**
     * 获取需要聚合主从数据为一个队列的配置，例：{"1":["xxxMainObj"]}
     * @return
     */
    @LogLevel(LogLevelEnum.TRACE)
    public Map<String, List<String>> getMergeMasterAndDetailTenantObj() {
        try {
            ErpTenantConfigurationEntity config = findOne(CommonConstant.configUniformIdentifier, CommonConstant.configUniformIdentifier, CommonConstant.configUniformIdentifier,
                    TenantConfigurationTypeEnum.MERGE_MASTER_DETAIL_TENANT_OBJ.name());
            if (config == null || StringUtils.isEmpty(config.getConfiguration())) {
                return Maps.newHashMap();
            }
            return JacksonUtil.fromJson(config.getConfiguration(), Map.class);
        } catch (Exception e) {
            log.error("getMergeMasterAndDetailTenantObj Exception e={}", e);
        }
        return Maps.newHashMap();
    }
    @LogLevel(LogLevelEnum.TRACE)
    public long getCrmDetailObjDefaultAggregationTime() {
        long defaultTime = 2000L;
        try {
            ErpTenantConfigurationEntity config = findOne(CommonConstant.configUniformIdentifier, CommonConstant.configUniformIdentifier, CommonConstant.configUniformIdentifier,
                    TenantConfigurationTypeEnum.CRM_DETAIL_OBJ_DEFAULT_AGGREGATION_TIME.name());
            if (config == null || StringUtils.isEmpty(config.getConfiguration())) {
                return defaultTime;
            }
            return Long.valueOf(config.getConfiguration());
        } catch (Exception e) {
            log.error("getCrmDetailObjDefaultAggregationTime Exception e={}", e);
        }
        return defaultTime;

    }

    @Cached(expire = 120, cacheType = CacheType.LOCAL, localLimit = 300)
    public Set<String> getDisableK3CInventoryAuxPropEiList() {
        //配置
        ErpTenantConfigurationEntity config = findOne(CommonConstant.configUniformIdentifier,
                CommonConstant.configUniformIdentifier,
                ErpChannelEnum.ERP_K3CLOUD.name(),
                TenantConfigurationTypeEnum.DISABLE_K3C_INVENTORY_AUX_PROP_EI_LIST.name());
        Set<String> set = new HashSet<>();
        if (config != null && StringUtils.isNotBlank(config.getConfiguration())) {
            set = ImmutableSet.copyOf(Splitter.on(";").split(config.getConfiguration()));
        }
        return set;
    }
    @LogLevel(LogLevelEnum.TRACE)
    public ErpIdNumberNameKeyMapping getK3cObjIdNumberNameKeyMapping(String tenantId,String erpObjApiName) {
        try {
            ErpTenantConfigurationEntity config = findOne(CommonConstant.configUniformIdentifier,
                    CommonConstant.configUniformIdentifier,
                    ErpChannelEnum.ERP_K3CLOUD.name(),
                    TenantConfigurationTypeEnum.K3C_OBJ_ID_NUMBER_NAME_KEY_MAPPING.name());
            if (config == null || StringUtils.isEmpty(config.getConfiguration())) {
                return null;
            }
            JSONObject configMap = JSONObject.parseObject(config.getConfiguration());
            List<ErpIdNumberNameKeyMapping> list = JSONObject.parseArray(configMap.getString(tenantId),ErpIdNumberNameKeyMapping.class);
            if(CollectionUtils.isNotEmpty(list)) {
                for(ErpIdNumberNameKeyMapping mapping : list) {
                    if(StringUtils.equalsIgnoreCase(mapping.getErpObjApiName(),erpObjApiName)) return mapping;
                }
            }
            return null;
        } catch (Exception e) {
            log.error("getK3cObjIdNumberNameKeyMapping Exception e={}", e);
        }
        return null;

    }
    @LogLevel(LogLevelEnum.DEBUG)
    public Set<String> getNeedInterceptRepeatSyncTenants() {
        Set<String> set = new HashSet<>();
        set.add("*");
        try {
            //配置
            ErpTenantConfigurationEntity config = findOne(CommonConstant.configUniformIdentifier,
                    CommonConstant.configUniformIdentifier,
                    CommonConstant.configUniformIdentifier,
                    TenantConfigurationTypeEnum.NEED_INTERCEPT_REPEAT_SYNC_TENANT.name());
            if (config != null && StringUtils.isNotBlank(config.getConfiguration())) {
                set = ImmutableSet.copyOf(Splitter.on(";").split(config.getConfiguration()));
            }
        }catch (Exception e){
            log.error("getNeedInterceptRepeatSyncTenants Exception e={}", e);
        }
        return set;
    }

    @Cached(expire = 120, cacheType = CacheType.LOCAL, localLimit = 300)
    public Set<String> getNotUsePaasAddApiName(String tenantId) {
        //配置
        ErpTenantConfigurationEntity config = findOne(tenantId,
                CommonConstant.configUniformIdentifier,
                CommonConstant.configUniformIdentifier,
                TenantConfigurationTypeEnum.NOT_USE_PAAS_ADD_API_NAME.name());
        Set<String> set = new HashSet<>();
        if (config != null && StringUtils.isNotBlank(config.getConfiguration())) {
            set = ImmutableSet.copyOf(Splitter.on(";").split(config.getConfiguration()));
        }
        return set;
    }

    @Cached(expire = 120, cacheType = CacheType.LOCAL, localLimit = 300)
    public Set<String> getNotUsePaasUpdateApiName(String tenantId) {
        //配置
        ErpTenantConfigurationEntity config = findOne(tenantId,
                CommonConstant.configUniformIdentifier,
                CommonConstant.configUniformIdentifier,
                TenantConfigurationTypeEnum.NOT_USE_PAAS_UPDATE_API_NAME.name());
        Set<String> set = new HashSet<>();
        if (config != null && StringUtils.isNotBlank(config.getConfiguration())) {
            set = ImmutableSet.copyOf(Splitter.on(";").split(config.getConfiguration()));
        }
        return set;
    }

    @LogLevel(LogLevelEnum.DEBUG)
    public boolean needCompareDetail(String tenantId, String dcId, ErpChannelEnum channel, String splitObjApiName) {
        try {
            //配置
            ErpTenantConfigurationEntity config = findOne(tenantId, dcId, channel.name(), TenantConfigurationTypeEnum.NEED_COMPARE_DETAIL_OBJ.name());
            if (config != null && StringUtils.isNotBlank(config.getConfiguration())) {
                Set<String> set = ImmutableSet.copyOf(Splitter.on(";").split(config.getConfiguration()));
                if(set.contains("*")||set.contains(splitObjApiName)){
                    return true;
                }
            }
        }catch (Exception e){
            log.error("needCompareDetail Exception e={}", e);
        }
        return false;
    }

    public List<String> getNPathUrlFormat() {
        List<String> formatList = new ArrayList<>();
        try {
            ErpTenantConfigurationEntity config = findOne(CommonConstant.configUniformIdentifier,
                    CommonConstant.configUniformIdentifier,
                    CommonConstant.configUniformIdentifier,
                    TenantConfigurationTypeEnum.NPATH_URL_FORMAT.name());
            if (config != null && StringUtils.isNotBlank(config.getConfiguration())) {
                formatList = Splitter.on(",").splitToList(config.getConfiguration());
            }
        }catch (Exception e){
            log.error("getNpathUrlFormat Exception e={}", e);
        }
        return formatList;
    }

    @LogLevel(LogLevelEnum.TRACE)
    @Cached(expire = 120, cacheType = CacheType.LOCAL, localLimit = 300)
    public LogStorageRuleEnum getLogStorageRule(String tenantId) {
        return getLogStorageRuleNoCache(tenantId);
    }

    public LogStorageRuleEnum getLogStorageRuleNoCache(String tenantId) {
        try {
            ErpTenantConfigurationEntity config = findOne(tenantId,
                    CommonConstant.configUniformIdentifier,
                    CommonConstant.configUniformIdentifier,
                    TenantConfigurationTypeEnum.LOG_STORAGE_RULE.name());
            if (config == null || StringUtils.isEmpty(config.getConfiguration())) {
                return LogStorageRuleEnum.RULE_ONE;
            }
            return LogStorageRuleEnum.valueOf(config.getConfiguration());
        } catch (Exception e) {
            log.error("getLogStorageRuleNoCache Exception e={}", e);
        }
        return LogStorageRuleEnum.RULE_ONE;
    }




    public boolean isTenantNotChangeBigDecimal(String tenantId) {
        try {
            ErpTenantConfigurationEntity config = findOne(CommonConstant.configUniformIdentifier,
                    CommonConstant.configUniformIdentifier,
                    CommonConstant.configUniformIdentifier,
                    TenantConfigurationTypeEnum.NotChangeBigDecimalTenant.name());
            if(null == config) {
                return false;
            }
            if ((config.getUpdateTime() > isTenantNotChangeBigDecimalLastUpdateTime) && StringUtils.isNotBlank(config.getConfiguration())) {
                isTenantNotChangeBigDecimalLastUpdateTime = config.getUpdateTime();
                isTenantNotChangeBigDecimalEiList = Splitter.on(";").splitToList(config.getConfiguration());
            }
        }catch (Exception e){
            log.error("getNotChangeBigDecimalTenantIdList Exception e={}", e);
        }
        boolean ret = isTenantNotChangeBigDecimalEiList.contains(tenantId);
        log.debug("trace isTenantNotChangeBigDecimalEiList ei:{}, ret:{}", tenantId,ret);
        return ret;
    }

    @LogLevel(LogLevelEnum.TRACE)
    @Cached(expire = 120, cacheType = CacheType.LOCAL, localLimit = 300)
    public Map<String,Object> needBidirectionalWriting(String tenantId) {
        try {
            ErpTenantConfigurationEntity config = findOne(CommonConstant.configUniformIdentifier,
                    CommonConstant.configUniformIdentifier,
                    CommonConstant.configUniformIdentifier,
                    TenantConfigurationTypeEnum.NeedBidirectionalWriting.name());
            if (config != null && StringUtils.isNotBlank(config.getConfiguration())) {
                Map<String,Map<String,Object>> result=JacksonUtil.fromJson(config.getConfiguration(), Map.class);
                return result.get(tenantId);
            }
        }catch (Exception e){
            log.error("needBidirectionalWriting Exception e={}", e);
        }
        return Maps.newHashMap();
    }

    @LogLevel(LogLevelEnum.DEBUG)
    public Integer getPushFrequencyLimitPerMin(String tenantId) {
        int limit=500;
        try {
            ErpTenantConfigurationEntity config = findOne(CommonConstant.configUniformIdentifier,
                    CommonConstant.configUniformIdentifier,
                    CommonConstant.configUniformIdentifier,
                    TenantConfigurationTypeEnum.PushFrequencyLimitPerMin.name());
            if (config != null && StringUtils.isNotBlank(config.getConfiguration())) {
                Map<String,Integer> result=JacksonUtil.fromJson(config.getConfiguration(), Map.class);
                if(result.containsKey(tenantId)){
                    return result.get(tenantId);
                }else if(result.containsKey("*")){
                    return result.get("*");
                }
            }
        }catch (Exception e){
            log.error("getPushLimitPerMin Exception e={}", e);
        }
        return limit;
    }

    public K3UltimateConfigModel.UpdateErpObjConfig getK3UltimateUpdateErpObjConfig(String tenantId,
                                                                                    String dataCenterId,
                                                                                    String erpObjApiName) {
        try {
            ErpTenantConfigurationEntity config = findOne(tenantId,
                    dataCenterId,
                    ErpChannelEnum.ERP_K3CLOUD_ULTIMATE.name(),
                    TenantConfigurationTypeEnum.K3_ULTIMATE_UPDATE_ERP_OBJ_CONFIG.name());
            if (config == null || StringUtils.isEmpty(config.getConfiguration())) {
                return null;
            }
            Map<String,K3UltimateConfigModel.UpdateErpObjConfig> updateErpObjConfig = JacksonUtil.fromJson(config.getConfiguration(),
                    new TypeReference<Map<String, K3UltimateConfigModel.UpdateErpObjConfig>>() {});
            return updateErpObjConfig.get(erpObjApiName);

        } catch (Exception e) {
            log.error("getK3UltimateUpdateErpObjConfig Exception e={}", e);
        }
        return null;
    }

    public K3UltimateConfigModel.CreateErpObjConfig getK3UltimateCreateErpObjConfig(String tenantId,
                                                                                    String dataCenterId,
                                                                                    String erpObjApiName) {
        try {
            ErpTenantConfigurationEntity config = findOne(tenantId,
                    dataCenterId,
                    ErpChannelEnum.ERP_K3CLOUD_ULTIMATE.name(),
                    TenantConfigurationTypeEnum.K3_ULTIMATE_CREATE_ERP_OBJ_CONFIG.name());
            if (config == null || StringUtils.isEmpty(config.getConfiguration())) {
                return null;
            }
            Map<String,K3UltimateConfigModel.CreateErpObjConfig> createErpObjConfig = JacksonUtil.fromJson(config.getConfiguration(),
                    new TypeReference<Map<String, K3UltimateConfigModel.CreateErpObjConfig>>() {});
            return createErpObjConfig.get(erpObjApiName);

        } catch (Exception e) {
            log.error("getK3UltimateCreateErpObjConfig Exception e={}", e);
        }
        return null;
    }

    @LogLevel(LogLevelEnum.TRACE)
    @Cached(expire = 120, cacheType = CacheType.LOCAL, localLimit = 3000)
    public boolean getKeepNullValueConfig(String tenantId, String objectApiName) {

        try {
            ErpTenantConfigurationEntity entity = findOne(tenantId,
                    "0",
                    ErpChannelEnum.ALL.name(),
                    TenantConfigurationTypeEnum.KEEP_NULL_VALUE_IN_FIELD_VALUE_CONVERTER.name());
            if (ObjectUtils.isEmpty(entity) || StringUtils.isEmpty(entity.getConfiguration())) {
                return false;
            }
            return StringUtils.containsIgnoreCase(entity.getConfiguration(), objectApiName);
        } catch (Exception e) {
            log.error("keepNullValue Exception e={}", e);
        }
        return false;
    }

    @LogLevel(LogLevelEnum.TRACE)
    @Cached(expire = 2, cacheType = CacheType.BOTH, key = "args[0]")
    public <T> T getGlobalAndParse(TenantConfigurationTypeEnum typeEnum, TypeReference<T> tTypeReference) {
        //这个也有缓存，所有null不需要缓存
        ErpTenantConfigurationEntity global = findGlobal(typeEnum.name());
        if (global == null) {
            return null;
        }
        //不做容错，有需要上层调用做
        T t = JacksonUtil.fromJson(global.getConfiguration(), tTypeReference);
        return t;
    }

    @LogLevel(LogLevelEnum.TRACE)
    @Cached(expire = 120, cacheType = CacheType.LOCAL, localLimit = 500)
    public Boolean isUsedFMaterialId2Id(String tenantId,String dcId) {
        ErpTenantConfigurationEntity config = findOne(tenantId,
                dcId,
                ErpChannelEnum.ERP_K3CLOUD.name(),
                TenantConfigurationTypeEnum.SUPPORT_FMATERIALID_TO_ID.name());
        if (config != null) {
            return true;
        }
        return false;
    }

    public ErpTenantConfigurationEntity getReTryFromMongo() {
        ErpTenantConfigurationEntity config = findOne(CommonConstant.configUniformIdentifier,
                CommonConstant.configUniformIdentifier,
                CommonConstant.configUniformIdentifier,
                TenantConfigurationTypeEnum.RE_TRY_PAAS_MQ.name());
        return config;
    }
    @LogLevel(LogLevelEnum.TRACE)
    @Cached(expire = 120, cacheType = CacheType.LOCAL, localLimit = 300)
    public Map<String,Long> getTenantExpireTimeInterval() {
        try {
            ErpTenantConfigurationEntity config = findOne(CommonConstant.configUniformIdentifier,
                    CommonConstant.configUniformIdentifier,
                    CommonConstant.configUniformIdentifier,
                    TenantConfigurationTypeEnum.TENANT_LOG_EXPIRE_TIME_INTERVAL.name());
            if (config != null && StringUtils.isNotBlank(config.getConfiguration())) {
                Map<String,Long> result=JacksonUtil.fromJson(config.getConfiguration(), new TypeReference<Map<String, Long>>() {});
                return result;
            }
        }catch (Exception e){
            log.error("getTenantExpireTimeInterval Exception e={}", e);
        }
        return Maps.newHashMap();
    }

    @LogLevel(LogLevelEnum.TRACE)
    @Cached(expire = 120, cacheType = CacheType.LOCAL, localLimit = 300)
    public Map<String, String> getChannel2ApplicationMarketId() {
        try {
            ErpTenantConfigurationEntity config = findOne(CommonConstant.configUniformIdentifier,
                    CommonConstant.configUniformIdentifier,
                    CommonConstant.configUniformIdentifier,
                    TenantConfigurationTypeEnum.CHANNEL_APPLICATION_MARKET_ID.name());
            if (config != null && StringUtils.isNotBlank(config.getConfiguration())) {
                Map<String,String> result=JacksonUtil.fromJson(config.getConfiguration(), new TypeReference<Map<String, String>>() {});
                return result;
            }
        }catch (Exception e){
            log.error("getTenantExpireTimeInterval Exception e={}", e);
        }
        return Maps.newHashMap();
    }

    @LogLevel(LogLevelEnum.TRACE)
    public List<String> getNeedSetUpdateJsonFieldEnv() {
        try {
            ErpTenantConfigurationEntity config = findOne(CommonConstant.configUniformIdentifier,
                    CommonConstant.configUniformIdentifier,
                    CommonConstant.configUniformIdentifier,
                    TenantConfigurationTypeEnum.NEED_SET_UPDATEJSONFIELD_ENV.name());
            if (config != null && StringUtils.isNotBlank(config.getConfiguration())) {
                List<String> result=JacksonUtil.fromJson(config.getConfiguration(), List.class);
                return result;
            }
        }catch (Exception e){
            log.error("getNeedSetUpdateJsonFieldEnv Exception e={}", e);
        }
        return Lists.newArrayList();
    }
    @LogLevel(LogLevelEnum.TRACE)
    @Cached(expire = 120, cacheType = CacheType.LOCAL, localLimit = 300)
    public Map<String,Map<String,Map<String,Object>>> getK3InterfaceArg(String tenantId,String dcId) {
        try {
            ErpTenantConfigurationEntity config = findOne(tenantId,
                    dcId,
                    ErpChannelEnum.ERP_K3CLOUD.name(),
                    TenantConfigurationTypeEnum.ERP_K3_OBJ_INTERFACE_ARG_SETTING.name());
            if (config != null && StringUtils.isNotBlank(config.getConfiguration())) {
                Map<String,Map<String,Map<String,Object>>> result=JacksonUtil.fromJson(config.getConfiguration(), new TypeReference<Map<String,Map<String,Map<String,Object>>>>() {});
                return result;
            }
        }catch (Exception e){
            log.error("getK3InterfaceArg Exception e={}", e);
        }
        return Maps.newHashMap();
    }

    public K3UltimateSignatureConfig getK3UltimateSignatureConfig(String tenantId, String dcId, Boolean getFromCache) {
        try {
            ErpTenantConfigurationEntity config;
            if (getFromCache == null || getFromCache) {
                config = findOne(tenantId,
                        dcId,
                        ErpChannelEnum.ERP_K3CLOUD_ULTIMATE.name(),
                        TenantConfigurationTypeEnum.ERP_K3_ULTIMATE_SIGNATURE_CONFIG.name());
            } else {
                config = findNoCache(tenantId,
                        dcId,
                        ErpChannelEnum.ERP_K3CLOUD_ULTIMATE.name(),
                        TenantConfigurationTypeEnum.ERP_K3_ULTIMATE_SIGNATURE_CONFIG);
            }
            if (config != null && StringUtils.isNotBlank(config.getConfiguration())) {
                K3UltimateSignatureConfig result = JacksonUtil.fromJson(config.getConfiguration(), K3UltimateSignatureConfig.class);
                return result;
            }
        } catch (Exception e) {
            log.error("getK3InterfaceArg Exception e={}", e);
        }
        return null;
    }

    @LogLevel(LogLevelEnum.TRACE)
    public Set<String> getNotReportErrorNumEis() {
        Set<String> formatList = Sets.newHashSet();
        try {
            ErpTenantConfigurationEntity config = findOne(CommonConstant.configUniformIdentifier,
                    CommonConstant.configUniformIdentifier,
                    CommonConstant.configUniformIdentifier,
                    TenantConfigurationTypeEnum.NOT_REPORT_ERROR_NUM.name());
            if (config != null && StringUtils.isNotBlank(config.getConfiguration())) {
                formatList = Sets.newHashSet(Splitter.on(",").splitToList(config.getConfiguration()));
            }
        }catch (Exception e){
            log.error("getNotReportErrorNumEis Exception e={}", e);
        }
        return formatList;
    }

    @LogLevel(LogLevelEnum.TRACE)
    @Cached(expire = 120, cacheType = CacheType.LOCAL, localLimit = 300)
    public Map<String, List<String>> getErpNeedSingleSync(String tenantId, String dcId) {
        Map<String, List<String>> result = Maps.newHashMap();
        try {
            ErpTenantConfigurationEntity config = findOne(CommonConstant.configUniformIdentifier,
                    CommonConstant.configUniformIdentifier,
                    CommonConstant.configUniformIdentifier,
                    TenantConfigurationTypeEnum.ERP_NEED_SINGLE_SYNC_OBJ_DEFAULT.name());
            if (config != null && StringUtils.isNotBlank(config.getConfiguration())) {
                Map<String, List<String>> result1 = JacksonUtil.fromJson(config.getConfiguration(), new TypeReference<Map<String, List<String>>>() {
                });
                result.putAll(result1);
            }
            config = findOne(tenantId,
                    dcId,
                    CommonConstant.configUniformIdentifier,
                    TenantConfigurationTypeEnum.ERP_NEED_SINGLE_SYNC_OBJ_TENANT.name());
            if (config != null && StringUtils.isNotBlank(config.getConfiguration())) {
                Map<String, List<String>> result1 = JacksonUtil.fromJson(config.getConfiguration(), new TypeReference<Map<String, List<String>>>() {
                });
                result.putAll(result1);
            }

            return result;
        } catch (Exception e) {
            log.error("getNeedSingleSync Exception e={}", e);
        }
        return Maps.newHashMap();
    }
    @LogLevel(LogLevelEnum.TRACE)
    @Cached(expire = 120, cacheType = CacheType.LOCAL, localLimit = 300)
    public List<String> getCrmNeedSingleSync(String tenantId) {
        List<String> result = Lists.newArrayList();
        result.add(ObjectApiNameEnum.FS_PRICEBOOKPRODUCT_OBJ.getObjApiName());
        try {
            ErpTenantConfigurationEntity config = findOne(CommonConstant.configUniformIdentifier,
                    CommonConstant.configUniformIdentifier,
                    CommonConstant.configUniformIdentifier,
                    TenantConfigurationTypeEnum.CRM_NEED_SINGLE_SYNC_OBJ_DEFAULT.name());
            if (config != null && StringUtils.isNotBlank(config.getConfiguration())) {
                List<String> result1 = JacksonUtil.fromJson(config.getConfiguration(), new TypeReference<List<String>>() {});
                result.addAll(result1);
            }
            config = findOne(tenantId,
                    CommonConstant.configUniformIdentifier,
                    CommonConstant.configUniformIdentifier,
                    TenantConfigurationTypeEnum.CRM_NEED_SINGLE_SYNC_OBJ_TENANT.name());
            if (config != null && StringUtils.isNotBlank(config.getConfiguration())) {
                List<String> result1 = JacksonUtil.fromJson(config.getConfiguration(), new TypeReference<List<String>>() {});
                result.addAll(result1);
            }
            return result;
        } catch (Exception e) {
            log.error("getNeedSingleSync Exception e={}", e);
        }
        return result;
    }
}






