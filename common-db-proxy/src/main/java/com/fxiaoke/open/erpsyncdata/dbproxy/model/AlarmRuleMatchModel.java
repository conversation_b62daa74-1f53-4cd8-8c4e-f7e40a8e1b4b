package com.fxiaoke.open.erpsyncdata.dbproxy.model;

import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpAlarmRuleEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AlarmRuleMatchModel implements Serializable {
    private static final long serialVersionUID = 1L;
    private ErpAlarmRuleEntity matchEntity;
    private Integer matchThreshold;
    private Integer minThreshold;
    private Integer maxThreshold;
    /**
     * 大于最大阈值
     */
    private boolean greaterThanMaxThreshold;
}
