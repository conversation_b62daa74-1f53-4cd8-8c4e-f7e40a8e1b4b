package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.*;
import org.mongodb.morphia.utils.IndexType;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/8/22 19:44:29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity(value = "history_task_duplicate_data", noClassnameStored = true)
@Indexes({
//        @Index(fields = {@Field("tenantId"), @Field("taskNum"), @Field(value = "duplicateTime", type = IndexType.DESC)}, options = @IndexOptions(background = true)),
        @Index(fields = {@Field("tenantId"), @Field("taskNum"), @Field(value = "dataId")}, options = @IndexOptions(background = true)),
        @Index(fields = {@Field("createTime")}, options = @IndexOptions(background = true, expireAfterSeconds = 14 * 24 * 3600))
})
public class HistoryTaskDuplicateDataEntity implements Serializable {

    /**
     * 本次历史任务查询到的总数据
     */
    public static final String totalSizeDataNum = "erpdss_task_total";

    @Id
    @Property("id")
    private ObjectId id;

    @TenantID
    @Property("tenant_id")
    private String tenantId;

    @Property("task_num")
    private String taskNum;

    @Property("data_id")
    private String dataId;

    /**
     * 重复次数
     */
    @Property("time")
    private Integer duplicateTime;

    /**
     * 任务创建时间
     */
    @Property("create_time")
    private Long createTime;

    public HistoryTaskDuplicateDataEntity(String tenantId, String taskNum, String dataId, Integer duplicateTime, Long createTime) {
        this.tenantId = tenantId;
        this.taskNum = taskNum;
        this.dataId = dataId;
        this.duplicateTime = duplicateTime;
        this.createTime = createTime;
    }
}
