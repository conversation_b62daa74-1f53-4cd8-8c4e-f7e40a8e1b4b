package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.mongodb.morphia.annotations.*;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/25 15:01:28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity(value = "polling_temp_fail", noClassnameStored = true)
@Indexes({@Index(fields = {@Field("tenant_id"), @Field("status"), @Field(value = "next_time"), @Field("_id")}, options = @IndexOptions(background = true))
        , @Index(fields = {@Field("next_time")}, options = @IndexOptions(background = true, expireAfterSeconds = 7 * 24 * 3600))
})
public class PollingTempFailEntity implements Serializable {

    public static final int status_init = 1;
    public static final int status_executing = 2;
    public static final int status_success = 3;
    public static final int status_fail = 4;

    @Id
    private String id;
    @Property("tenant_id")
    private String tenantId;
    @Property("arg")
    private String timeFilterArg;
    @Property("type")
    private List<Integer> theObjOperateType;
    @Property("send_detail")
    private boolean needSendDetailEvent;
    @Property("ploy_id")
    private String syncPloyDetailId;
    @Property("log_id")
    private String traceId;
    /**
     * 1.未执行 2.执行中 3.成功 4.失败满重试次数
     */
    @Property("status")
    private Integer status;

    /**
     * 最后一次错误的错误信息
     * 成功后会不置空
     */
    @Property("err_msg")
    private String errMsg;
    /**
     * 已重试次数
     */
    @Property("time")
    private int tryTime;
    /**
     * 下次重试时间
     */
    @Property("next_time")
    private Long nextRetryTime;
    @Property("create_time")
    private Long createTime;
    @Property("update_time")
    private Long updateTime;
}
