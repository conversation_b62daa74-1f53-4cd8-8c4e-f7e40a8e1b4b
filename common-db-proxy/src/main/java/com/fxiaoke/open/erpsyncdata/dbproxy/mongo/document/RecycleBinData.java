package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import com.fxiaoke.open.erpsyncdata.dbproxy.constant.RecycleType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.bson.Document;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.types.ObjectId;

import java.util.Date;

/**
 * 回收站数据
 * <AUTHOR> (^_−)☆
 * @date 2023/5/26
 */
@Data
@FieldNameConstants
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RecycleBinData {
    @BsonId
    private ObjectId id;

    /**
     * 创建时间，储存时赋值
     */
    private Date createTime;

    private String tenantId;

    /**
     * 操作人userId，依赖traceContext获取
     */
    private String operatorId;

    /**
     * 标识同一次操作的id
     * 用于后期恢复数据之类的
     */
    private String actionId;

    /**
     * 回收资源类型
     */
    private RecycleType recycleType;

    /**
     * 回收的数据
     */
    private Document recycleData;

    /**
     * 回收的数据json
     */
    private String recycleJson;

}
