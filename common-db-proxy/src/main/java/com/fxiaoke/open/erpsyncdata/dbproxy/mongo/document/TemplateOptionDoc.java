package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/8/26
 */

@Data
@FieldNameConstants
public class TemplateOptionDoc {
    /**
     * id
     */
    @Id
    @JSONField(serializeUsing = ObjectIdSer.class,deserializeUsing = ObjectIdSer.class)
    private ObjectId id;
    /**
     * 类型
     */
    private String type;
    /**
     * id
     */
    private String value;
    /**
     * 名称
     */
    private String label;
    /**
     * 扩展信息，用于储存跳转链接等
     */
    private String extend;

    /**
     * 用于排序
     */
    private int order = 65536;
}
