package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.base.BaseExpireIndexLogMongoStore;
import com.google.common.collect.Lists;
import com.mongodb.client.FindIterable;
import com.mongodb.client.model.*;
import com.mongodb.client.result.DeleteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/2/23 10:15:55
 */
@Repository
@Slf4j
public class InterfaceMonitorMongoStore extends BaseExpireIndexLogMongoStore<Document> {

    protected final static String interfaceMonitorCollectionPrefix = "interface_monitor_";

    public long countInterfaceMonitor(String tenantId, List<Bson> filters) {
        return this.getOrCreateCollection(tenantId).countDocuments(Filters.and(filters));
    }

    public long countInterfaceMonitorLimitSize(String tenantId, List<Bson> filters, int limit) {
        return this.getOrCreateCollection(tenantId).countDocuments(Filters.and(filters), new CountOptions().limit(limit).maxTime(3, TimeUnit.SECONDS));
    }

    public List<Document> listInterfaceMonitorDocument(String tenantId, List<Bson> filters, Bson sort, int offset, Integer limit) {
        List<Document> result = Lists.newArrayList();
        FindIterable<Document> iterable;
        if (sort == null) {
            if(CollectionUtils.isEmpty(filters)){
                iterable = this.getOrCreateCollection(tenantId).find().skip(offset).limit(limit);
            }else{
                iterable = this.getOrCreateCollection(tenantId).find(Filters.and(filters)).skip(offset).limit(limit);
            }
        } else {
            if(CollectionUtils.isEmpty(filters)){
                iterable = this.getOrCreateCollection(tenantId).find().sort(sort).skip(offset).limit(limit);
            }else{
                iterable = this.getOrCreateCollection(tenantId).find(Filters.and(filters)).sort(sort).skip(offset).limit(limit);
            }
        }
        for (Document doc : iterable) {
            result.add(doc);
        }
        return result;
    }

    /**
     * mongo 的投影，过滤掉不需要的字段，避免OOM
     *
     * @param projections 投影字段
     */
    public List<Document> listInterfaceMonitorDocumentByProjection(String tenantId, List<Bson> filters, Bson projections, Bson sort, int offset, int limit) {
        List<Document> result = Lists.newArrayList();
        FindIterable<Document> iterable;
        if (sort == null) {
            iterable = this.getOrCreateCollection(tenantId).find(Filters.and(filters)).projection(projections).skip(offset).limit(limit);
        } else {
            iterable = this.getOrCreateCollection(tenantId).find(Filters.and(filters)).projection(projections).sort(sort).skip(offset).limit(limit);
        }

        for (Document doc : iterable) {
            result.add(doc);
        }
        return result;
    }

    public List<Document> listInterfaceMonitorDocumentByResult(String tenantId, List<Bson> filters, String resultStr, Bson sort, int offset, int limit) {
        List<Document> result = Lists.newArrayList();
        FindIterable<Document> iterable;
        if (sort == null) {
            iterable = this.getOrCreateCollection(tenantId).find(Filters.and(filters)).skip(0).limit(1000);//从1000条中筛选
        } else {
            iterable = this.getOrCreateCollection(tenantId).find(Filters.and(filters)).sort(sort).skip(0).limit(1000);//从1000条中筛选
        }
        int num = 0;
        for (Document doc : iterable) {
            if (doc.getString("result") == null || !doc.getString("result").contains(resultStr)) {
                continue;
            }
            num++;
            if (num <= offset) {//过滤掉前面offset条
                continue;
            }
            doc.remove("result");
            result.add(doc);
            if (result.size() == limit) {
                break;
            }
        }
        return result;
    }

    public DeleteResult deleteInterfaceMonitorDocument(String tenantId, Bson filter) {
        return this.getOrCreateCollection(tenantId).deleteMany(filter);
    }

    @Override
    public String getCollName(String tenantId) {
        return interfaceMonitorCollectionPrefix + tenantId;
    }

    @Override
    public List<IndexModel> buildIndexes(String tenantId) {
        List<IndexModel> toBeCreate = Lists.newArrayList();
        String key = "index_ei_obj_type_desc_id";
        Bson index_ei_obj_type_id = Indexes.compoundIndex(Indexes.ascending("tenant_id"), Indexes.ascending("obj_api_name"), Indexes.ascending("type"),
                Indexes.descending("_id"));
        toBeCreate.add(new IndexModel(index_ei_obj_type_id, new IndexOptions().name(key).background(true)));

        key = "index_ei_obj_type_call_time";
        Bson index_ei_obj_type_call_time = Indexes.compoundIndex(Indexes.ascending("tenant_id"), Indexes.ascending("obj_api_name"), Indexes.ascending("type"),
                Indexes.descending("call_time"));
        toBeCreate.add(new IndexModel(index_ei_obj_type_call_time, new IndexOptions().name(key).background(true)));

        key = "index_expire_time";//过期时间，新的集合有效,如果要旧的集合有效，需要使用修改ttl索引的过期时间
        Bson index_expire_time = expireIndex();
        toBeCreate.add(new IndexModel(index_expire_time, new IndexOptions().name(key).expireAfter(getExpireDay(tenantId), TimeUnit.DAYS).background(true)));

        key = "index_sync_data_id";
        Bson index_sync_data_id = Indexes.ascending("sync_data_id");
        toBeCreate.add(new IndexModel(index_sync_data_id, new IndexOptions().name(key).background(true)));

        key = "index_start_time";
        Bson index_start_time = Indexes.ascending("time_filter_arg.startTime");
        toBeCreate.add(new IndexModel(index_start_time, new IndexOptions().name(key).background(true)));

        key = "index_end_time";
        Bson index_end_time = Indexes.ascending("time_filter_arg.endTime");
        toBeCreate.add(new IndexModel(index_end_time, new IndexOptions().name(key).background(true)));

        //基于tenant_id创建索引
        key = "tenant_id";
        Bson tenant_id = Indexes.ascending("tenant_id");
        toBeCreate.add(new IndexModel(tenant_id, new IndexOptions().name(key).background(true)));

        return toBeCreate;
    }

    @Override
    public Bson expireIndex() {
        return Indexes.ascending("expire_time");
    }
}
