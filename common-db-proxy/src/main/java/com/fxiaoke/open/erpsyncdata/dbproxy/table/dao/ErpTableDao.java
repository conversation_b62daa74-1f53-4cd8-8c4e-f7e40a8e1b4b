package com.fxiaoke.open.erpsyncdata.dbproxy.table.dao;

import cn.hutool.core.lang.Dict;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpBaseDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldExtendEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.PgClassInfo;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/11/4
 */
@Repository
public interface ErpTableDao extends ErpBaseDao<ErpFieldExtendEntity>, ITenant<ErpTableDao> {

    List<Map<String,Object>> superQuerySql(@Param(value = "sqlStr") String sqlStr, @Param(value = "tenantId") String tenantId);
    List<Dict> superQuerySql2(@Param(value = "sqlStr") String sqlStr, @Param(value = "tenantId") String tenantId);

    int superUpdateSql(@Param(value = "sqlStr") String sqlStr);

    /**
     * 查找所有左匹配的表
     *
     * @param prefix
     * @return
     */
    List<String> listAllTableLeftMatching(@Param(value = "prefix")String prefix);
    /**
     * 查找存在的表
     *
     * @param prefix
     * @return
     */
    List<String> listTables(@Param(value = "tables") Collection<String> tables);

    List<PgClassInfo> queryReltuplesFromPgClass();
}