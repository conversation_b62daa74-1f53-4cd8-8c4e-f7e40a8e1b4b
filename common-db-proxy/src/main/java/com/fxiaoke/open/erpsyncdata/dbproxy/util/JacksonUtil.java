package com.fxiaoke.open.erpsyncdata.dbproxy.util;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.codehaus.jackson.Version;

import java.util.HashMap;
import java.util.Map;
import java.io.IOException;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.TimeZone;

/**
 * Jackson工具类
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/11/12
 */
public class JacksonUtil {
    private static final ObjectMapper MAPPER = new ObjectMapper();
    private static final ObjectMapper CUSTOMMAPPER = new ObjectMapper();

    static {
        SimpleModule simpleModule=new SimpleModule();
        //BigDecimal序列化为字符串
        simpleModule.addSerializer(BigDecimal.class, new JsonSerializer<BigDecimal>() {
            @Override
            public void serialize(BigDecimal bigDecimal, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
                if(bigDecimal!=null){
                    jsonGenerator.writeString(bigDecimal.toPlainString());
                }
            }
        });
        CUSTOMMAPPER.registerModule(simpleModule);

        MAPPER.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        MAPPER.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        MAPPER.setTimeZone(TimeZone.getDefault());
//        MAPPER.configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES,true);
    }

    public static ObjectMapper get() {
        return MAPPER;
    }

    /**
     * 该方法会序列化null值
     * @param data
     * @return
     */
    public static String toJson(Object data) {
        if (data == null) {
            return "";
        }
        if (data instanceof String) {
            return (String) data;
        }
        try {
            String result = MAPPER.writeValueAsString(data);
            return result;
        } catch (JsonProcessingException e) {
            throw new ErpSyncDataException(e, ResultCodeEnum.JSON_CONVERT_EXCEPTION,null);
        }
    }
    public static String customToJson(Object data) {
        if (data == null) {
            return "";
        }
        if (data instanceof String) {
            return (String) data;
        }
        try {
            String result = CUSTOMMAPPER.writeValueAsString(data);
            return result;
        } catch (JsonProcessingException e) {
            throw new ErpSyncDataException(e, ResultCodeEnum.JSON_CONVERT_EXCEPTION,null);
        }
    }

    public static <T> T fromJson(String jsonData, TypeReference<T> beanType) {
        try {
            T result = MAPPER.readValue(jsonData, beanType);
            return result;
        } catch (Exception e) {
            throw new ErpSyncDataException(e, ResultCodeEnum.JSON_CONVERT_EXCEPTION,null);
        }
    }

    public static <T> T fromJson(String jsonData, Class<T> tClass) {
        try {
            T result = MAPPER.readValue(jsonData, tClass);
            return result;
        } catch (Exception e) {
            throw new ErpSyncDataException(e, ResultCodeEnum.JSON_CONVERT_EXCEPTION,null);
        }
    }

    public static String toXMLString(Object object) {
        try {
            XmlMapper xmlMapper = XmlMapper.builder()
                    .configure(SerializationFeature.INDENT_OUTPUT, true)
                    .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                    .enable(MapperFeature.USE_STD_BEAN_NAMING)
                    .build();
            String xmlString = xmlMapper.writeValueAsString(object);
            return xmlString;
        } catch (Exception e) {
            throw new ErpSyncDataException(e, ResultCodeEnum.JSON_2_XML_CONVERT_EXCEPTION,null);
        }
    }

    /**
     * 比较对象的字段，
     * return true if oldVal="" and newVal=null
     * @param oldVal
     * @param newVal
     * @return
     */
    public static boolean equalObjField(Object oldVal, Object newVal) {
        if (oldVal == newVal) {
            return true;
        }
        return toJson(oldVal).equals(toJson(newVal));
    }


    public static void main(String[] args) {
        String jsonString = "{\"name\":\"John\", \"age\":20, \"address\":{\"street\":\"Wall Street\", \"city\":\"New York\"}}";
        Map<String,String> map = new HashMap<>();
        map.put("name","wubb");
        map.put("sex","male");
        Result result = Result.newSuccess(map);
        toXMLString(result);
    }

}
