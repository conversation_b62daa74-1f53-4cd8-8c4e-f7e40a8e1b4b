package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.LogStorageRuleEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpTenantConfigurationDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.K3UltimateSignatureConfig;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.google.common.collect.Lists;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.annotation.Lazy;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TenantConfigurationManagerTest {

    @Mock
    private ErpTenantConfigurationDao erpTenantConfigurationDao;

    @Mock
    private I18NStringManager i18NStringManager;

    @Mock
    @Lazy
    private NotificationService notificationService;

    @Mock
    private ConfigCenterConfig configCenterConfig;

    @InjectMocks
    private TenantConfigurationManager tenantConfigurationManager;

    private String tenantId = "99999";
    private String dataCenterId = "0";

    @BeforeEach
    void setUp() {
        // 设置基本的mock行为
        when(erpTenantConfigurationDao.setTenantId(any())).thenReturn(erpTenantConfigurationDao);
        
        // 清理缓存
        tenantConfigurationManager.clearCache();
    }

    private ErpTenantConfigurationEntity buildConfig(TenantConfigurationTypeEnum type,String config) {
        ErpTenantConfigurationEntity mockConfigEntity;
        mockConfigEntity = new ErpTenantConfigurationEntity();
        mockConfigEntity.setTenantId(tenantId);
        mockConfigEntity.setDataCenterId(dataCenterId);
        mockConfigEntity.setType(type.name());
        mockConfigEntity.setConfiguration(config);
        return mockConfigEntity;
    }

    @Test
    void testGetLogStorageRule() {
        //模拟返回的配置
        when(erpTenantConfigurationDao.queryList(any())).thenReturn(Lists.newArrayList(
            buildConfig(TenantConfigurationTypeEnum.LOG_STORAGE_RULE,"RULE_TWO")
        ));

        // 执行测试
        LogStorageRuleEnum result = tenantConfigurationManager.getLogStorageRule(tenantId);

        // 验证结果
        assertEquals(LogStorageRuleEnum.RULE_TWO, result);
        verify(erpTenantConfigurationDao).queryList(any());
    }

    @Test
    void testGetLogStorageRuleWithNullConfig() {
        //模拟返回的配置
        when(erpTenantConfigurationDao.queryList(any())).thenReturn(Lists.newArrayList(
            buildConfig(TenantConfigurationTypeEnum.LOG_STORAGE_RULE,"")
        ));

        // 执行测试
        LogStorageRuleEnum result = tenantConfigurationManager.getLogStorageRule(tenantId);

        // 验证结果
        assertEquals(LogStorageRuleEnum.RULE_ONE, result);
    }

    @Test
    void testGetDoubleConfig() {
        // 测试场景1: 正常获取配置值
        when(erpTenantConfigurationDao.queryList(any())).thenReturn(Lists.newArrayList(
            buildConfig(TenantConfigurationTypeEnum.TENANT_DISPATCHER_REMAIN_MAXIMUM, "{\"key1\":100.0,\"key2\":200.0}")
        ));
        Double result1 = tenantConfigurationManager.getDoubleConfig(TenantConfigurationTypeEnum.TENANT_DISPATCHER_REMAIN_MAXIMUM, 0.0, "key1");
        assertEquals(100.0, result1);

        // 清理缓存
        tenantConfigurationManager.clearCache();

        // 测试场景2: 配置为空时返回默认值
        when(erpTenantConfigurationDao.queryList(any())).thenReturn(Lists.newArrayList(
            buildConfig(TenantConfigurationTypeEnum.TENANT_DISPATCHER_REMAIN_MAXIMUM, null)
        ));
        Double result2 = tenantConfigurationManager.getDoubleConfig(TenantConfigurationTypeEnum.TENANT_DISPATCHER_REMAIN_MAXIMUM, 0.0, "key1");
        assertEquals(0.0, result2);

        // 清理缓存
        tenantConfigurationManager.clearCache();

        // 测试场景3: 配置格式错误时返回默认值
        when(erpTenantConfigurationDao.queryList(any())).thenReturn(Lists.newArrayList(
            buildConfig(TenantConfigurationTypeEnum.TENANT_DISPATCHER_REMAIN_MAXIMUM, "invalid json")
        ));
        Double result3 = tenantConfigurationManager.getDoubleConfig(TenantConfigurationTypeEnum.TENANT_DISPATCHER_REMAIN_MAXIMUM, 0.0, "key1");
        assertEquals(0.0, result3);

        // 清理缓存
        tenantConfigurationManager.clearCache();

        // 测试场景4: 多个key按顺序查找
        when(erpTenantConfigurationDao.queryList(any())).thenReturn(Lists.newArrayList(
            buildConfig(TenantConfigurationTypeEnum.TENANT_DISPATCHER_REMAIN_MAXIMUM, "{\"key1\":100.0,\"key2\":200.0}")
        ));
        Double result4 = tenantConfigurationManager.getDoubleConfig(TenantConfigurationTypeEnum.TENANT_DISPATCHER_REMAIN_MAXIMUM, 0.0, "key3", "key1", "key2");
        assertEquals(100.0, result4);

        // 清理缓存
        tenantConfigurationManager.clearCache();

        // 测试场景5: 参数为null时抛出异常
        assertThrows(ErpSyncDataException.class, () -> {
            tenantConfigurationManager.getDoubleConfig(TenantConfigurationTypeEnum.TENANT_DISPATCHER_REMAIN_MAXIMUM, 0.0, (String[])null);
        });
    }
} 