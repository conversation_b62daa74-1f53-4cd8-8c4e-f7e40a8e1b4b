package com.fxiaoke.open.erpsyncdata.apiproxy.constant;

import com.fxiaoke.open.erpsyncdata.preprocess.arg.BIFieldDescArg;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/3/29 15:52
 * 大屏看板的信息，线上线下统一
 * @desc
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum BIDashboardEnum {

    COUNT_CRM_READ_WRITE("BI_65f2a9acb04b200001768dd1","读写CRM接口次数统计"),
    COUNT_CRM_BUSINESS_SYNC("BI_65f2a91eb04b200001768d6f","CRM数据同步量"),
    ANALYZE_BUSINESS_FLOW("BI_65f2a76eb04b200001768c6f","业务流向分析"),
    ANY_SYSTEM_INFO("BI_65f2a79bb04b200001768c8c","各系统对接情况")
    ;
    private String filterId;
    private String dashboardName;

    public static  List<BIFieldDescArg.DashboardFilterEnum> getConditionFilterValues(BIDashboardEnum biDashboardEnum){
        List<BIFieldDescArg.DashboardFilterEnum> filterEnums= null;
        switch (biDashboardEnum){
            case COUNT_CRM_BUSINESS_SYNC:
                return Lists.newArrayList(
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2a920b04b200001768d74", "Date", "执行时间", "23","executeTime"),
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2a920b04b200001768d75", "select_one", "外部系统", "26","dataCenterId"),
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2a920b04b200001768d77", "select_one", "CRM对象", "26","crmObjApiName"),
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2a920b04b200001768d78", "select_one", "执行状态", "26","operateStatus"),
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2a9983be357000145697b", "select_one", "是否是刷数据", "26","historyDataType"),
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2ae203be3570001456a73", "select_one", "操作", "26","operationType"),
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2ae203be3570001456a74", "select_one", "操作类型", "26","operationTypeDetail"),
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2ae203be3570001456a75", "select_one", "外部对象", "26","outSideObjApiName"),
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2ae203be3570001456a76", "select_one", "源系统", "26","sourceSystemType")
                );
            case COUNT_CRM_READ_WRITE:
                return Lists.newArrayList(
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2a9adb04b200001768dd7", "Date", "执行时间", "23","executeTime"),
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2a9adb04b200001768dd8", "select_one", "外部系统", "26","dataCenterId"),
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2a9adb04b200001768dd9", "select_one", "CRM对象", "26","crmObjApiName"),
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2a9adb04b200001768dda", "select_one", "执行状态", "26","operateStatus"),
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2a9adb04b200001768ddb", "select_one", "是否是刷数据", "26","historyDataType"),
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2adbab04b200001768f97", "select_one", "操作", "26","operationType"),
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2adbab04b200001768f98", "select_one", "操作类型", "26","operationTypeDetail"),
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2adbab04b200001768f9a", "select_one", "外部对象", "26","outSideObjApiName"),
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2adbab04b200001768f9b", "select_one", "源系统", "26","sourceSystemType")
                );
            case ANALYZE_BUSINESS_FLOW:
                return Lists.newArrayList(
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2a76fb04b200001768c72", "Date", "执行时间", "23","executeTime"),
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2a903b04b200001768d24", "select_one", "外部系统", "26","dataCenterId"),
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2a903b04b200001768d25", "select_one", "CRM对象", "26","crmObjApiName"),
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2a903b04b200001768d26", "select_one", "执行状态", "26","operateStatus"),
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2ad71b04b200001768f3f", "select_one", "操作", "26","operationType"),
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2ad71b04b200001768f40", "select_one", "操作类型", "26","operationTypeDetail"),
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2ad71b04b200001768f41", "select_one", "是否是刷数据", "26","historyDataType"),
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2ad71b04b200001768f42", "select_one", "外部对象", "26","outSideObjApiName"),
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2ad71b04b200001768f43", "select_one", "源系统", "26","sourceSystemType")
                );
            case ANY_SYSTEM_INFO:
                return Lists.newArrayList(
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2a79db04b200001768c90", "Date", "执行时间", "23","executeTime"),
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2a845b04b200001768cc0", "select_one", "外部系统", "26","dataCenterId"),
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2a845b04b200001768cc1", "select_one", "外部对象", "26","outSideObjApiName"),
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2a845b04b200001768cc2", "select_one", "CRM对象", "26","crmObjApiName"),
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2a845b04b200001768cc3", "select_one", "执行状态", "26","operateStatus"),
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2ad20b04b200001768ee7", "select_one", "操作", "26","operationType"),
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2ad20b04b200001768ee6", "select_one", "操作类型", "26","operationTypeDetail"),
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2ad20b04b200001768ee8", "select_one", "是否是刷数据", "26","historyDataType"),
                        new BIFieldDescArg.DashboardFilterEnum("BI_65f2ad20b04b200001768ee9", "select_one", "源系统", "26","sourceSystemType")
                );
            default:
                break;

        }
        return null;

    }
}
