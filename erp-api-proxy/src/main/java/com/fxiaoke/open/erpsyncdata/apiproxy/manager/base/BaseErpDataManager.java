package com.fxiaoke.open.erpsyncdata.apiproxy.manager.base;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager.InterfaceMonitorManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/24
 */
@Slf4j
@Component
public abstract class BaseErpDataManager implements ConnectorDataHandler {

    @Getter
    private static InterfaceMonitorManager interfaceMonitorManager;

    @Autowired
    public void setInterfaceMonitorManager(InterfaceMonitorManager interfaceMonitorManager) {
        BaseErpDataManager.interfaceMonitorManager = interfaceMonitorManager;
    }

    /* 元数据相关接口 end */


    public static void saveErpInterfaceMonitor(String tenantId,
                                               String dcId,
                                               String objApiName,
                                               String type,
                                               Object arg,
                                               String result,
                                               Integer status,
                                               Long callTime,
                                               Long returnTime,
                                               String remark,
                                               String traceId,
                                               Long costTime,
                                               TimeFilterArg timeFilterArg) {
        if (interfaceMonitorManager==null){
            //方便单元测试
            return;
        }
        interfaceMonitorManager.saveErpInterfaceMonitor(tenantId, dcId, objApiName, type, arg, result, status, callTime, returnTime, remark, traceId, costTime, timeFilterArg);
    }

    public static void saveErpInterfaceMonitor(InterfaceMonitorData data) {
        saveErpInterfaceMonitor(data.getTenantId(),
                data.getDcId(),
                data.getObjApiName(),
                data.getType(),
                data.getArg(),
                data.getResult(),
                data.getStatus(),
                data.getCallTime(),
                data.getReturnTime(),
                data.getRemark(),
                data.getTraceId(),
                data.getCostTime(),
                data.getTimeFilterArg());
    }

    public static void saveWaitingInterfaceMonitor(boolean save) {
        interfaceMonitorManager.saveAndRemoveWaitingData(save);
    }

}
