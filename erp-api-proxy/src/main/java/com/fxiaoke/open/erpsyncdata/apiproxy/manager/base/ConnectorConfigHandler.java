package com.fxiaoke.open.erpsyncdata.apiproxy.manager.base;

import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.CheckStreamEnableArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.GetConnectorIntroArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorAuthType;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ErpObjTreeNode;
import com.fxiaoke.open.erpsyncdata.preprocess.model.SystemParams;
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.ConnectorIntro;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/24
 */
public interface ConnectorConfigHandler {

    Result<List<ConnectorAuthType>> getAuthTypeList(ErpConnectInfoEntity connectInfo);

    Result<ConnectorIntro> getConnectorIntro(ErpConnectInfoEntity connectInfo, GetConnectorIntroArg arg);

    Result<SystemParams> processUserInputSystemParams(ErpConnectInfoEntity connectInfo, SystemParams systemParams);

    Result<String> getOAuth2AuthUrl(ErpConnectInfoEntity connectInfo, SystemParams systemParams);

    Result<Void> checkAuthStatus(ErpConnectInfoEntity connectInfo);

    /**
     * 启程了启用的检查，配置+实现了才有效
     */
    default Result<Void> checkEnableStream(ErpConnectInfoEntity connectInfo, CheckStreamEnableArg arg){
        return Result.newSuccess();
    }

    default Result<List<ErpObjTreeNode>> getObjsNeedPreset(ErpConnectInfoEntity connectInfo) {
        return Result.newSuccess(new ArrayList<>());
    }

    default Result<String> getStreamInfoNeedPreset(ErpConnectInfoEntity connectInfo) {
        return Result.newSuccess(null);
    }
}
