package com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.header;


import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/8/30 15:22:57
 */
public interface HeaderPlaceholderService extends HeaderReplaceService {
    String getPlaceholder();

    String replace(String tenantId);

    @Override
    default boolean isAccept(String value) {
        return value.contains(getPlaceholder());
    }

    @Override
    default String replace(String tenantId, String value) {
        final String replace = replace(tenantId);
        if (StringUtils.isNotBlank(replace)) {
            return value.replace(getPlaceholder(), replace);
        }
        return value;
    }
}
