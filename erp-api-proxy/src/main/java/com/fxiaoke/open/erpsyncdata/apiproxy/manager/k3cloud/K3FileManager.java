package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud;

import com.facishare.converter.EIEAConverter;
import com.facishare.restful.client.exception.FRestClientException;
import com.facishare.stone.sdk.StoneProxyApi;
import com.facishare.stone.sdk.request.StoneFileDownloadRequest;
import com.facishare.stone.sdk.request.StoneFileGetMetaDataRequest;
import com.facishare.stone.sdk.request.StoneFileUploadRequest;
import com.facishare.stone.sdk.response.StoneFileGetFileMetaResponse;
import com.facishare.stone.sdk.response.StoneFileUploadResponse;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.FileSpeedLimiter;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3DownFileResult;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Date 2021/7/23 14:41
 * @Version 1.0
 */
@Component
@Slf4j
public class K3FileManager {

    @Autowired
    private StoneProxyApi stoneProxyApi;

    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private FileSpeedLimiter fileSpeedLimiter;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    static String regexPattern = "filename=[\\'\\\"]?([^\\'\\\"\\;\\n]+)[\\'\\\"]?";
    private static final Pattern FILENAME_PATTERN = Pattern.compile(regexPattern);


    //crm上传文件
    public Result<StoneFileUploadResponse> crmUploadFile(InputStream inputStream, String fileName, String tenantId, Integer size, String extendName) {
        //限速
        fileSpeedLimiter.fileRateLimiter(tenantId, size);
        String ea = eieaConverter.enterpriseIdToAccount(Integer.valueOf(tenantId));
        StoneFileUploadRequest request = new StoneFileUploadRequest();
        request.setEa(ea);
        request.setEmployeeId(-10000);
        request.setBusiness(CommonConstant.BUSINESS_SYMBOL);
        request.setFileSize(size);
        request.setExtensionName(extendName);
        request.setOriginName(fileName);
        StoneFileUploadResponse fileUploadResponse = null;
        try {
            fileUploadResponse = stoneProxyApi.uploadByStream("n", request, inputStream);
            log.info("upload file by stream ea,request:{},response:{}", ea, request, fileUploadResponse);
        } catch (FRestClientException e) {
            log.info("crmDownFile path :{},ea:{},message:{}", fileName, ea, e.getMessage());
            return Result.newError(ResultCodeEnum.FILE_UPLOAD_FAILED);
        }
        return Result.newSuccess(fileUploadResponse);
    }

    public Result<StoneFileUploadResponse> crmUploadFile(String tenantId,Response response,StringBuilder builder) {
        InputStream inputStream=null;
        try {
            if (response.isSuccessful()) {
                ResponseBody body = response.body();
                if (body == null) {
                    log.warn("downLoadFile error: response body is empty");
                    return null;
                }
                // 获取文件大小
                long size = body.contentLength();
                // 获取文件名和类型

                String disposition = response.header("Content-Disposition");
                if (disposition != null) {
                    String fileNameResult = URLDecoder.decode(disposition, "UTF-8");
                    String fileName = extractFileName(fileNameResult);
                    builder.append(fileName);
                    String suffix = fileNameResult.substring(fileNameResult.lastIndexOf(".") + 1);
                    // 获取文件输入流
                    inputStream = body.byteStream();
                    //限速
                    fileSpeedLimiter.fileRateLimiter(tenantId, (int) size);
                    String ea = eieaConverter.enterpriseIdToAccount(Integer.valueOf(tenantId));
                    StoneFileUploadRequest request = new StoneFileUploadRequest();
                    request.setEa(ea);
                    request.setEmployeeId(-10000);
                    request.setBusiness(CommonConstant.BUSINESS_SYMBOL);
                    request.setFileSize((int) size);
                    request.setExtensionName(suffix);
                    request.setOriginName(fileName);
                    StoneFileUploadResponse fileUploadResponse = null;
                    fileUploadResponse = stoneProxyApi.uploadByStream("n", request, inputStream);
                    log.info("upload file by stream ea,request:{},response:{}", ea, request, fileUploadResponse);
                    return Result.newSuccess(fileUploadResponse);
                }
            }
        } catch (Exception e) {
            log.info("upload ex message:{}",e.getMessage());
        }
        return Result.newError(ResultCodeEnum.CALL_PAAS_FAILED);
    }
    private String extractFileName(String contentDisposition) {
        String fileName = null;
        if (contentDisposition != null) {
            Matcher matcher = FILENAME_PATTERN.matcher(contentDisposition);
            if (matcher.find()) {
                fileName = matcher.group(1);
            }
        }
        return fileName;
    }

    //crm下载文件
    public Result<InputStream> crmDownFile(String filepath, String extendType, String tenantId, Long size) {
        //限速
        fileSpeedLimiter.fileRateLimiter(tenantId, size==null?null:Integer.valueOf(size.toString()));
        String ea = eieaConverter.enterpriseIdToAccount(Integer.valueOf(tenantId));
        StoneFileDownloadRequest stoneFileDownloadRequest = new StoneFileDownloadRequest();
        stoneFileDownloadRequest.setFileType(extendType);
        stoneFileDownloadRequest.setEa(ea);
        stoneFileDownloadRequest.setBusiness(CommonConstant.BUSINESS_SYMBOL);
        stoneFileDownloadRequest.setEmployeeId(-10000);
        stoneFileDownloadRequest.setPath(filepath);
        try {
            InputStream inputStream = stoneProxyApi.downloadStream(stoneFileDownloadRequest);
            return Result.newSuccess(inputStream);
        } catch (FRestClientException e) {
            log.info("crmDownFile path :{},ea:{},message:{}", filepath, ea, e.getMessage());
            e.printStackTrace();
        }
        return Result.newError(ResultCodeEnum.FILE_UPLOAD_FAILED);
    }


    //K3上传文件
    public Result<Map<String, Object>> k3uploadFile(String filepath, String extendType, String tenantId, InputStream inputStream, String fileName, Long fileSize, K3CloudApiClient client) {
        String ea = eieaConverter.enterpriseIdToAccount(Integer.valueOf(tenantId));
        StoneFileGetMetaDataRequest stoneFileGetMetaDataRequest = new StoneFileGetMetaDataRequest();
        stoneFileGetMetaDataRequest.setEa(ea);
        stoneFileGetMetaDataRequest.setPath(filepath);
        stoneFileGetMetaDataRequest.setBusiness(CommonConstant.BUSINESS_SYMBOL);
        stoneFileGetMetaDataRequest.setEmployeeId(-10000);
        Long tureSize = 0L;
        try {
            StoneFileGetFileMetaResponse fileMetaData = stoneProxyApi.getFileMetaData(stoneFileGetMetaDataRequest);
            tureSize = fileMetaData.getSize();
        } catch (FRestClientException e) {
            e.printStackTrace();
        }

        final ErpTenantConfigurationEntity erpTenantConfigurationEntity = tenantConfigurationManager.findOne(tenantId, "0", "ALL", TenantConfigurationTypeEnum.NEW_FILE_UPLOAD_INTERFACE.name());
        boolean needNewFileInterface = Boolean.FALSE;
        if (ObjectUtils.isNotEmpty(erpTenantConfigurationEntity)) {
            needNewFileInterface = Boolean.TRUE;
        }

        Result<Map<String, Object>> mapResult = client.uploadFile(fileName, "", false, inputStream, tureSize, needNewFileInterface);
        log.info("tureSize:{},fileSize:{}", tureSize, fileSize);
        return mapResult;
    }

//    public static void main(String[] args) {
//        String input = "attachment;filename=86002-650.jpg;filename*=utf-8''86002-650.jpg";
//        String input2 = "attachment;filename=通辽市北晟通达冷冻食品批发有限责任公司.pdf";
//        String input3 = "attachment;filename=通辽市北晟通达冷冻食品批发有限责任公司.pdf";
//        String input4 = "attachment;filename=通辽市北晟通达冷冻食品批发有限责任公司.pdf;with;semicolons.pdf";
//
//
//        // 正则表达式匹配 filename 后跟等号，然后是任意非分号字符序列，直到点号出现
//        String regex = "filename=[\\'\\\"]?([^\\'\\\"\\;\\n]+)[\\'\\\"]?";
//
//        // 创建 Pattern 对象
//        Pattern pattern = Pattern.compile(regex);
//
//        // 创建 matcher 对象
//        Matcher matcher = pattern.matcher(input2);
//
//        while (matcher.find()) {
//            // matcher.group(1) 返回第一个括号中匹配的内容，即文件名（不包括扩展名）
//            System.out.println("Filename without extension: " + matcher.group(1));
//        }
//    }

    //K3下载文件
    public Result<StoneFileUploadResponse> k3downAndUploadFile(String field, K3CloudApiClient k3CloudApiClient,StringBuilder builder) {
        Function<Response,Result<StoneFileUploadResponse>> uploadFunction=new Function<Response, Result<StoneFileUploadResponse>>() {
            @Override
            public Result<StoneFileUploadResponse> apply(Response response) {
                Result<StoneFileUploadResponse> fileUpload = crmUploadFile(k3CloudApiClient.getTenantId(),response,builder);
                if(fileUpload.isSuccess()){
                    return fileUpload;
                }
                return null;
            }
        };
        Result<StoneFileUploadResponse> stoneFileUploadResponseResult = k3CloudApiClient.downLoadAndUploadFile(field, uploadFunction);
        return stoneFileUploadResponseResult;
    }

}
