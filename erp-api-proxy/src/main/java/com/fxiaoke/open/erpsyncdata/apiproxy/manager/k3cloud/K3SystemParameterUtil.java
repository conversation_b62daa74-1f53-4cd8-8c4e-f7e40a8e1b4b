package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.cosntant.K3SOChangeTypeEnum;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3Model;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.XmlToJsonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.model.K3CloudConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.DocumentException;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/1/8
 */
@Slf4j
public class K3SystemParameterUtil {
    private static final String FACCTBOOKID = "FACCTBOOKID";
    private static final String FORGID = "FORGID";
    private static final String FPARAMETEROBJID = "FPARAMETEROBJID";
    private static final String FPARAMETERS = "FPARAMETERS";
    private static final String SAL_SYSTEM_PARAMETER = "SAL_SystemParameter";
    private static final String SO_CHANGE_TYPE = "SOChangeType";
    private static final String SYSTEM_PLAN_PARAMETER = "MFG_PLNParam";//计划管理参数
    private static final String IS_ENABLE_RESERVE = "IsEnableReserve";//是否启动预留


    /**
     * 获取K3销售订单变更方式参数
     * 已测试6.2.935.2以上版本都支持，更低版本需要测试
     *
     * @param apiClient
     * @param orgNumber     组织编码
     * @return
     */
    public static K3SOChangeTypeEnum getK3SoChangeType(K3CloudApiClient apiClient, String orgNumber) {
        QueryArg queryArg = new QueryArg();
        queryArg.setFormId(K3CloudForm.BOS_SystemParameter);
        queryArg.setFieldKeysByList(ImmutableList.of(FACCTBOOKID, FORGID, FPARAMETEROBJID, FPARAMETERS));
        queryArg.appendEqualFilter(FPARAMETEROBJID, SAL_SYSTEM_PARAMETER);

        String orgId = null;
        if(StringUtils.isNotEmpty(orgNumber)) {
            orgId = getOrgId(apiClient,orgNumber);
        }

        if (StringUtils.isNotEmpty(orgId)) {
            queryArg.appendEqualFilter(FORGID, orgId);
        }

        Result<List<K3Model>> result = apiClient.queryAll(queryArg);
        if (!result.isSuccess() || result.getData().isEmpty()) {
            //查询结果为空，走直接订单变更
            return K3SOChangeTypeEnum.DIRECT_CHANGE_SO;
        }
        //只取第一条
        String xmlParameter = result.getData().get(0).getString(FPARAMETERS);
        JSONObject jsonParameter;
        try {
            jsonParameter = XmlToJsonUtil.xml2jsonObj(xmlParameter);
        } catch (DocumentException e) {
            log.error("parse sal parameter failed,xml:{}", xmlParameter);
            //查询结果为空，走直接订单变更
            return K3SOChangeTypeEnum.DIRECT_CHANGE_SO;
        }
        String soChangeType = jsonParameter.getString(K3SystemParameterUtil.SO_CHANGE_TYPE);
        K3SOChangeTypeEnum soChangeTypeEnum = K3SOChangeTypeEnum.getByType(soChangeType);
        log.info("K3SystemParameterUtil.getK3SoChangeType,orgNumber={},soChangeTypeEnum={}",orgNumber,soChangeTypeEnum);
        return soChangeTypeEnum;
    }

    /**
     * 根据组织编码查询组织ID
     * @param apiClient
     * @param orgNumber
     * @return
     */
    public static String getOrgId(K3CloudApiClient apiClient,String orgNumber) {
        QueryArg queryArg = new QueryArg();
        queryArg.setFormId(K3CloudForm.ORG_Organizations);
        queryArg.setFieldKeysByList(ImmutableList.of("FOrgID","FNumber","FName"));
        queryArg.appendEqualFilter("FNumber",orgNumber);
        Result<List<K3Model>> result = apiClient.queryAll(queryArg);
        log.info("K3SystemParameterUtil.getFirstOrgId,result={}",result);
        return result.getData().get(0).getString("FOrgID");
    }


    /**
     * 获取计划管理参数，主要用在是否启动库存预留设置
     * 已测试6.2.935.2以上版本都支持，更低版本需要测试
     *
     * @param apiClient
     * @param orgId     组织id，传空则取第一条
     * @return
     */
    public static Boolean getIsEnableReserve(K3CloudApiClient apiClient, String orgId) {
        QueryArg queryArg = new QueryArg();
        queryArg.setFormId(K3CloudForm.BOS_SystemParameter);
        queryArg.setFieldKeysByList(ImmutableList.of(FACCTBOOKID, FORGID, FPARAMETEROBJID, FPARAMETERS));
        queryArg.appendEqualFilter(FPARAMETEROBJID, SYSTEM_PLAN_PARAMETER);
        if (orgId != null) {
            queryArg.appendEqualFilter(FORGID, orgId);
        }
        Result<List<K3Model>> result = apiClient.queryAll(queryArg);
        if (!result.isSuccess() || result.getData().isEmpty()) {
            return null;
        }
        //只取第一条
        String xmlParameter = result.getData().get(0).getString(FPARAMETERS);
        JSONObject jsonParameter;
        try {
            jsonParameter = XmlToJsonUtil.xml2jsonObj(xmlParameter);
        } catch (DocumentException e) {
            log.error("parse sal parameter failed,xml:{}", xmlParameter);
            return null;
        }
        String IsEnableReserve = jsonParameter.getString(K3SystemParameterUtil.IS_ENABLE_RESERVE);

        return Boolean.valueOf(IsEnableReserve);
    }


//    public static void main(String[] args) {
//        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
//                "https://klsm.ik3cloud.com/K3cloud",
//                "20211123111129956", "管理员", "KLSM@888");
//        K3CloudApiClient apiClient = K3CloudApiClient.newInstance("84801", connectParam, "771281743387623424");
//        K3SOChangeTypeEnum k3SoChangeType = getK3SoChangeType(apiClient, "KL");
//        System.out.println(k3SoChangeType);
//    }

}
