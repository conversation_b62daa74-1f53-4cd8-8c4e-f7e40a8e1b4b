package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.factory;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.SpecialBusiness;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.specialBusinessImpl.BaseSpecialBusiness;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class SpecialBusinessFactory {
    @Autowired
    private Map<String, SpecialBusiness> specialBusinessMap;

    public SpecialBusiness getHandlerByName(String name) {
        SpecialBusiness specialBusiness = specialBusinessMap.get(name);
        return specialBusiness == null ? new BaseSpecialBusiness() : specialBusiness;
    }
}
