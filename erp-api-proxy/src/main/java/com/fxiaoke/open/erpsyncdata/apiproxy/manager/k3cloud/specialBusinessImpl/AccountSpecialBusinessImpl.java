package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.specialBusinessImpl;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.CommonBusinessManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.K3DataConverter;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.SpecialBusiness;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.IdSaveExtend;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3Model;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.SaveArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.ViewArg;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldExtendEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpOrganizationObjManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpOrganizationObj;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/11/24
 */
@Slf4j
@Component("BD_Customer")
public class AccountSpecialBusinessImpl implements SpecialBusiness {
    @Autowired
    private ErpFieldManager erpFieldManager;
    @Autowired
    private CommonBusinessManager commonBusinessManager;
    @Autowired
    private ErpOrganizationObjManager erpOrganizationObjManager;

    /**
     * 如果客户的id是编码，则查询其id用于调用查看接口
     *
     * @param viewArg
     * @param erpIdArg
     * @return
     */
    @Override
    public void beforeRunView(ViewArg viewArg, ErpIdArg erpIdArg, K3CloudApiClient apiClient) {
        ErpFieldExtendEntity idField = erpFieldManager.queryIdField(erpIdArg.getTenantId(),apiClient.getDataCenterId(), erpIdArg.getObjAPIName()).get(0);
        if ("Number".equalsIgnoreCase(idField.getViewCode())) {
            String id = commonBusinessManager.getIdByNumber(erpIdArg.getObjAPIName(),
                    erpIdArg.getDataId(),
                    "FCUSTID",
                    "FNumber",
                    apiClient);
            viewArg.setId(id);
        }
    }

    /**
     * 更新erp数据前置动作
     *  @param saveArg
     * @param apiClient
     * @param saveExtend
     */
    @Override
    public void beforeRunUpdate(SaveArg saveArg, StandardData standardData, K3DataConverter k3DataConverter, K3CloudApiClient apiClient, IdSaveExtend saveExtend) {
        K3Model model = saveArg.getModel();
        //移除编码，不改变ERP编码
        model.remove("FNumber");
        String id = model.getString("FCUSTID");
        if (StringUtils.isBlank(id)) {
            String erpNumber=standardData.getMasterFieldVal().getId();
            Map<String,String> eqFilter= Maps.newHashMap();
            if(model.containsKey("FCreateOrgId")){
                Object fCreateOrgId = model.get("FCreateOrgId");
                if(fCreateOrgId instanceof Map){
                    Map<String,String> fCreateOrgIdMap=(Map)fCreateOrgId;
                    String org=fCreateOrgIdMap.get("FNumber");
                    if(StringUtils.isNotBlank(org)){
                        eqFilter.put("FCreateOrgId.FNumber",org);
                    }
                }
            }
            if(model.containsKey("FUseOrgId")){
                Object fUseOrgId = model.get("FUseOrgId");
                if(fUseOrgId instanceof Map){
                    Map<String,String> fUseOrgIdMap=(Map)fUseOrgId;
                    String org=fUseOrgIdMap.get("FNumber");
                    if(StringUtils.isNotBlank(org)){
                        eqFilter.put("FUseOrgId.FNumber",org);
                    }
                }
            }
            //id为空，编码不为空，用编码获取id
            id = commonBusinessManager.getIdByNumberAndFilter("BD_Customer", erpNumber, "FCUSTID",
                    "FNumber", apiClient, eqFilter);
            //id字段放在最后同样有效
            model.put("FCUSTID", id);
        }
    }

    private void setUseOrgFilter(String tenantId, String dataCenterId, QueryArg queryArg) {
        List<ErpOrganizationObj> erpOrganizationList = erpOrganizationObjManager.queryDcErpOrganizationObj(tenantId,
                dataCenterId);
        String orgNumber = erpOrganizationList.stream()
                .filter(ErpOrganizationObj::getNeedSyncCustomer)
                .map(s -> "'" + s.getOrgNumber() + "'")        //拼接''
                .collect(Collectors.joining(","));
        if (StringUtils.isBlank(orgNumber)) {
            throw new ErpSyncDataException(ResultCodeEnum.SYNC_CUSTOMER_ORG_NOT_FOUND,tenantId);
        }
        // 设置参数
        queryArg.appendFilterString(String.format("FUseOrgId.FNumber in ( %s ) ", orgNumber));
        log.info("AccountSpecialBusinessImpl.setUseOrgFilter,queryArg={}",queryArg);
    }
    @Override
    public void beforeGetDataByBillQuery(String tenantId, QueryArg queryArg, K3CloudApiClient k3CloudApiClient){
        //设置使用组织筛选条件
        setUseOrgFilter(tenantId,k3CloudApiClient.getDataCenterId(),queryArg);
    }

    @Override
    public void beforeRunBillQuery(QueryArg queryArg, TimeFilterArg timeFilterArg, K3CloudApiClient k3CloudApiClient) {
        //设置使用组织筛选条件
        setUseOrgFilter(timeFilterArg.getTenantId(),k3CloudApiClient.getDataCenterId(),queryArg);
    }
}
