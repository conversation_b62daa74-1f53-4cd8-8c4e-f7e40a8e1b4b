package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.template;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.K3UltimateObjApiName;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import org.springframework.stereotype.Component;

@Component
public class XSaleOrderApiTemplate extends K3UltimateBaseApiTemplate {
    @Override
    public String getObjApiName() {
        return K3UltimateObjApiName.sm_xssalorder;
    }

    @Override
    public String getBatchQueryApi() {
        return "/kapi/v2/sm/sm_xssalorder/query";
    }

    @Override
    public String getBatchAddApi() {
        return null;
    }

    @Override
    public String getBatchUpdateApi() {
        return "/kapi/v2/sm/sm_xssalorder/batchUpdate";
    }

    @Override
    public String getBatchSubmitApi() {
        return "/kapi/v2/sm/sm_xssalorder/batchSubmit";
    }

    @Override
    public String getBatchUnSubmitApi() {
        return "/kapi/v2/sm/sm_xssalorder/batchUnSubmit";
    }

    @Override
    public String getBatchAuditApi() {
        return "/kapi/v2/sm/sm_xssalorder/batchAudit";
    }

    @Override
    public String getBatchUnAuditApi() {
        return "/kapi/v2/sm/sm_xssalorder/batchUnAudit";
    }

    @Override
    public String getBatchEnableApi() {
        return null;
    }

    @Override
    public String getBatchDisableApi() {
        return null;
    }

    @Override
    public String getBatchDeleteApi() {
        return "/kapi/v2/sm/sm_xssalorder/batchDelete";
    }

    @Override
    public String getBatchValidApi() {
        return "/kapi/v2/sm/sm_xssalorder/batchBizValid";
    }
}
