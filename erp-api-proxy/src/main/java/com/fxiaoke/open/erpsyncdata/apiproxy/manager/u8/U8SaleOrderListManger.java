package com.fxiaoke.open.erpsyncdata.apiproxy.manager.u8;


import com.fxiaoke.open.erpsyncdata.apiproxy.manager.u8.constant.U8ObjIdFieldConfig;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
@Service("saleorder")
public class U8SaleOrderListManger extends U8DefaultMananger{

    @Override
    protected void generatorId(StandardData standardData){

        //获取主对象数据主键
        String apiName=standardData.getObjAPIName();
        String masterCodes= U8ObjIdFieldConfig.getApiName(apiName);
        String masterId=null;
        String[] masterIdCodes = masterCodes.split(",");
        for (String idCode : masterIdCodes) {
            if (masterId==null){
                masterId=standardData.getMasterFieldVal().getString(idCode);
            }else {
                masterId=masterId+"#"+standardData.getMasterFieldVal().get(idCode);
            }
        }
        standardData.getMasterFieldVal().put("masterId",masterId);

        List<ObjectData> orderEntry = standardData.getDetailFieldVals().get("entry");
        if (!CollectionUtils.isEmpty(orderEntry)){
            for (ObjectData objectData : orderEntry) {
                Object iid= objectData.get("iid");
                Object inventorycode= objectData.get("inventorycode");
                if (iid==null){
                    iid=objectData.get("rowno");
                }
                objectData.put("detailId",masterId+"#"+inventorycode+"#"+iid);
            }
        }

    }
}
