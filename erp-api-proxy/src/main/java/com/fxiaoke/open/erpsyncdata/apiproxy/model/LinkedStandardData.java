package com.fxiaoke.open.erpsyncdata.apiproxy.model;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 标准格式，支持主从字段对象字段列表顺序排序
 * <AUTHOR>
 * @date 2021/07/06
 */
@Data
@ApiModel(value = "标准格式数据",description = "erp数据平台标准格式数据,主从字段有序")
public class LinkedStandardData implements Serializable {
    private static final long serialVersionUID = -1031712456801253532L;

    @ApiModelProperty("主对象apiName")
    private String objAPIName;
    @ApiModelProperty("主对象数据")
    private LinkedHashMap<String,Object> masterFieldVal;
    @ApiModelProperty("从对象数据;key:从对象apiName,value:从对象数据")
    private Map<String, List<LinkedHashMap<String,Object>>> detailFieldVals = new LinkedHashMap<>(0);

    public void removeId(){
        this.masterFieldVal.remove("_id");
        if (detailFieldVals!=null){
            for (List<LinkedHashMap<String,Object>> value : detailFieldVals.values()) {
                for (LinkedHashMap<String,Object> objectData : value) {
                    objectData.remove("_id");
                }
            }
        }
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("objAPIName", objAPIName)
                .append("masterFieldVal", safe2Str(masterFieldVal))
                .append("detailFieldVals", safe2Str(detailFieldVals))
                .toString();
    }

    private static String safe2Str(Object obj){
        String s = JSON.toJSONString(obj);
        if (s.length()>5000){
            s = s.substring(0,5000)+"skip...";
        }
        return s;
    }
}
