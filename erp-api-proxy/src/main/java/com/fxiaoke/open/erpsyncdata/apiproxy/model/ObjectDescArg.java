package com.fxiaoke.open.erpsyncdata.apiproxy.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.LinkedHashSet;


@Data
public class ObjectDescArg {
    /**
     * 真实对象apiName
     */
    private String realObjectApiName;


    @ToString(callSuper = true)
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class ParseObjTree extends ObjectDescArg {
        /**
         * 是否检查已存在，当为true，且已存在时报错
         */
        private boolean checkExist;
    }

    @ToString(callSuper = true)
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class ParseObjField extends ObjectDescArg {
        /**
         * 主对象是否已经存在，用于判断是否需要预置主对象。
         */
        private boolean exist = false;
        /**
         * 需要解析的明细对象（一些系统无法从主知道明细，需要传输明细对象ApiName）
         */
        private LinkedHashSet<String> realChildObjectApiName = new LinkedHashSet<>();
    }


    @Data
    public static class ParseObjFieldBySplit {
        /**
         * 中间对象apiName
         */
        private String splitObjectApiName;

        /**
         * 字段apiName，不指定查所有
         */
        private String fieldApiName;
    }
}
