package com.fxiaoke.open.erpsyncdata.apiproxy.model.crm;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/4/4
 */
@Accessors(chain = true)
public class CrmConfig {

    @Data
    public static class BatchArg {
        private Boolean isAllConfig = false;
        private List<String> keys = new ArrayList<>();
    }


    @Data
    public static class Arg {
        private String key;
    }

    @Data
    public static class Result {
        private String value;
    }

    public static class BatchResult {
        private List<Value> values;
    }

    @Data
    public static class Value {
        private String key;
        private String value;
    }
}
