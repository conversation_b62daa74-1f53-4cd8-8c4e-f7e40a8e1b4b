package com.fxiaoke.open.erpsyncdata.apiproxy.model.dbproxy;

import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 */
public class CompositeExecuteResult extends ArrayList<ExecuteResult> {
    private static final long serialVersionUID = 1725321813971053547L;

    public Map<String, ExecuteResult> toResultMap() {
        return this.stream().collect(Collectors.toMap(v -> v.getReferenceId(), v -> v, (v1, v2) -> v1));
    }
}
