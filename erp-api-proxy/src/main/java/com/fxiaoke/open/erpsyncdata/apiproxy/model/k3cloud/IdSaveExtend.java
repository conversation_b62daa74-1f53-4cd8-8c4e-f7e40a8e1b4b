package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud;

import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.cosntant.K3DocumentStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * id字段扩展保存配置
 * <AUTHOR> (^_−)☆
 * @date 2020/12/7
 */
@Data
@Slf4j
public class IdSaveExtend {
    /**
     * 是否需要自动提交并审核，默认为true
     */
    private Boolean isAutoSubmitAndAudit = true;

    /**
     * 使用暂存接口不直接保存，默认为false
     */
    private Boolean useDraft = false;

    /**
     * 只提交不审核，默认为false
     */
    private Boolean isAutoSubmitWithoutAudit = false;

    /**
     * 表单所在的子系统内码，默认null
     */
    private String subSystemId = null;

    /**
     * 是否批量填充分录
     */
    private Boolean isEntryBatchFill = null;

    /**
     * 是否验证所有的基础资料有效性，布尔类，默认false（非必录）
     */
    private Boolean isVerifyBaseDataField = null;

    /**
     * 销售订单是否在K3C开启CPQ，CRM不开启CPQ
     */
    private Boolean isDifferentCPQ = false;

    /**
     * 修改后的单据状态
     * 销售订单不支持修改单据状态
     * @see K3DocumentStatusEnum#status
     */
     private String modifyStatus;

    public static IdSaveExtend of(String objApiName, String str){
        if (StringUtils.isNotBlank(str)){
            try {
                IdSaveExtend idSaveExtend = JacksonUtil.fromJson(str,IdSaveExtend.class);
                return idSaveExtend;
            }catch (Exception e){
                log.error("save extend error,IdSaveExtend:{}",str,e);
            }
        } else if (K3CloudForm.BD_CommonContact.equals(objApiName)) {
//            联系人默认为保存状态
            IdSaveExtend idSaveExtend = new IdSaveExtend();
            idSaveExtend.setIsAutoSubmitAndAudit(false);
            return idSaveExtend;
        }
        return new IdSaveExtend();
    }

    /**
     * 获取新增后状态
     * @
     */
    public String getAddStatus() {
        if (Boolean.TRUE.equals(useDraft)) {
            return K3DocumentStatusEnum.DRAFT.getStatus();
        }
        if (Boolean.TRUE.equals(isAutoSubmitAndAudit)) {
            return K3DocumentStatusEnum.AUDITED.getStatus();
        }

        if (Boolean.TRUE.equals(isAutoSubmitWithoutAudit)) {
            return K3DocumentStatusEnum.AUDITING.getStatus();
        }

        return K3DocumentStatusEnum.ADD.getStatus();
    }
}
