package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.StrUtil2;
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * K3查询类
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class QueryArg {
    @SerializedName("FormId")
    @JsonProperty("FormId")
    private String formId;

    @SerializedName("FieldKeys")
    @JsonProperty("FieldKeys")
    private String fieldKeys;

    @SerializedName("FilterString")
    @JsonProperty("FilterString")
    private String filterString;

    @SerializedName("OrderString")
    @JsonProperty("OrderString")
    private String orderString;

    /**
     * 接口返回限制
     */
    @SerializedName("TopRowCount")
    @JsonProperty("TopRowCount")
    private String rowCount;

    @SerializedName("StartRow")
    @JsonProperty("StartRow")
    private Integer startRow = 0;

    /**
     * 数据库查询限制
     */
    @SerializedName("Limit")
    @JsonProperty("Limit")
    private Integer limit = 100;

    /**
     * K3C版本7.7支持此参数
     * 模块/子系统ID
     */
    @SerializedName("SubSystemId")
    @JsonProperty("SubSystemId")
    private String subSystemId;

    /**
     * 移除重复的字段
     * 因为有一些企业，不能传输重复的key。现在发现的是使用oracle数据的企业
     */
    public void removeDuplicateFieldKeys() {
        if (fieldKeys==null){
            return;
        }
        List<String> fieldKeyList = getFieldKeyList();
        Set<String> fieldSet = new HashSet<>();
        List<String> newFieldKeyLists = new ArrayList<>();
        for (String s : fieldKeyList) {
            if (fieldSet.add(s.toLowerCase())) {
                newFieldKeyLists.add(s);
            }
        }
        setFieldKeysByList(newFieldKeyLists);
    }

    public void setFieldKeysByList(List<String> fieldKeys) {
        String keyStr = Joiner.on(",").join(fieldKeys);
        this.fieldKeys = keyStr;
    }

    @JsonIgnore
    public List<String> getFieldKeyList() {
        //noinspection UnstableApiUsage
        return Splitter.on(",").splitToList(this.fieldKeys);
    }

    public void addAndFilters(List<FilterData> filterDatas) {
        if (CollectionUtils.isEmpty(filterDatas)) {
            return;
        }
        String addString = filterDatas.stream()
                .map(K3FilterStringBuilder::transFilter).filter(filter->StringUtils.isNotBlank(filter))
                .collect(Collectors.joining(" and "));
        addString = StringUtils.defaultIfBlank(addString, "");
        appendFilterString(addString);
    }

    public void addOrFilters(List<FilterData> filterDatas) {
        if (CollectionUtils.isEmpty(filterDatas)) {
            return;
        }
        String addString = filterDatas.stream()
                .map(K3FilterStringBuilder::transFilter).filter(filter->StringUtils.isNotBlank(filter))
                .collect(Collectors.joining(" or "));
        addString = StringUtils.defaultIfBlank(addString, "");
        appendFilterString(addString);
    }

    /**
     * 因为老是写漏单引号，写个方法调用
     * 并且支持连续调用
     * @param fieldKey
     * @param value
     */
    public QueryArg appendEqualFilter(String fieldKey,String value){
        appendFilterString(StrUtil2.convertSpecSql(fieldKey,value));
        return this;
    }

    /**
     *
     * @param fieldKey
     * @param values
     * @return
     */
    public QueryArg appendInFilter(String fieldKey, Collection<String> values){
        appendFilterString(String.format("%s in ('%s')", fieldKey,Joiner.on("','").skipNulls().join(values)));
        return this;
    }

    public void appendFilterString(String filterString){//添加与条件，一步步连续添加，括号会比较多
        if (StringUtils.isBlank(this.filterString)) {
            this.filterString = filterString;
        } else if (StringUtils.isNotBlank(filterString)) {
            if (this.filterString.contains(" or ")) {
                this.filterString = "(" + this.filterString + ")";
            }
            if (filterString.contains(" or ")) {
                filterString = "(" + filterString + ")";
            }
            this.filterString = this.filterString + " and " + filterString;
        }
    }

    public void appendOrFilterString(String filterString){
        if (StringUtils.isBlank(this.filterString)) {
            this.filterString = filterString;
        } else if (StringUtils.isNotBlank(filterString)) {//加括号
            this.filterString = "(" + this.filterString + ") or (" + filterString+")";
        }
    }

}
