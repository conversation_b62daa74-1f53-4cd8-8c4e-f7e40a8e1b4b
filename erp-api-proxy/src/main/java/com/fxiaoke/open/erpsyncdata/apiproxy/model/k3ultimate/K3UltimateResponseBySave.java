package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate;

import lombok.Data;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;

@Data
public class K3UltimateResponseBySave implements Serializable {
    private DataModel data;
    private String errorCode;
    private String message;
    private boolean status;

    @Data
    public static class DataModel implements Serializable {
        private int failCount;
        private int successCount;
        private int totalCount;
        private List<LinkedHashMap<String,Object>> result;
    }
}