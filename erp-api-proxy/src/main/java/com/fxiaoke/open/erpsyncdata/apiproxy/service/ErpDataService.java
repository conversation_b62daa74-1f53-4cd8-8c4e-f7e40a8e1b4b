package com.fxiaoke.open.erpsyncdata.apiproxy.service;

import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardDetailId;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardInvalidData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardRecoverData;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.DataMonitorScreen;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.model.IdFieldKey;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ListErpObjDataResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;

/**
 * erp数据接口代理服务
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/8/24
 */
public interface ErpDataService {

    /**
     * 根据时间获取erp对象数据
     *
     * @param timeFilterArg
     * @return
     */
    @DataMonitorScreen(tenantId = "#timeFilterArg.tenantId", dataCenterId = "#dataCenterId", outSideObjApiName = "#timeFilterArg.objAPIName", outDataCount = "#result?.getData()?.dataList?.size()?:0",sourceSystemType = "2",operationType = CommonConstant.READ_OPERATE_TYPE,operateStatus="'s106240000'.equals(#result?.getErrCode())?1:2")
    Result<StandardListData> listStandardErpObjDataByTime(TimeFilterArg timeFilterArg,String dataCenterId);

    /**
     * 新建Erp对象数据
     *
     * @param doWriteMqData
     * @return
     */
    Result<ErpIdResult> createErpObjData(SyncDataContextEvent doWriteMqData,String dataCenterId);

    /**
     * 单据新建Erp对象明细数据
     *
     * @param doWriteMqData
     * @return
     */
    Result<StandardDetailId> createErpObjDetailData(SyncDataContextEvent doWriteMqData,String dataCenterId);

    /**
     * 更新Erp对象数据
     *
     * @param doWriteMqData
     * @return
     */
    Result<ErpIdResult> updateErpObjData(SyncDataContextEvent doWriteMqData,String dataCenterId);


    /**
     * 单独更新Erp对象明细数据
     *
     * @param doWriteMqData
     * @return
     */
    Result<StandardDetailId> updateErpObjDetailData(SyncDataContextEvent doWriteMqData,String dataCenterId);

    /**
     * 从ERP获取数据，可以指定是ERP获取或者默认获取
     * @param erpIdArg
     * @return
     */
    @DataMonitorScreen(tenantId = "#erpIdArg.tenantId", dataCenterId = "#dataCenterId", outSideObjApiName = "#erpIdArg.objAPIName", outDataCount = "1",outSideObjId = "#erpIdArg.dataId",sourceSystemType = "2",operationType = CommonConstant.READ_OPERATE_TYPE,operationTypeDetail =  ErpObjInterfaceUrlEnum.queryMasterById , operateStatus="'s106240000'.equals(#result?.getErrCode())?1:2")
    Result<StandardData> getStandardErpObjDataById(ErpIdArg erpIdArg,String dataCenterId, IdFieldKey idFieldKey);


    /**
     * 读取单条Erp对象数据接口
     * 注：只可以查询主对象。查找从对象数据也只可以通过查找子对象数据查找
     *
     * @param erpIdArg
     * @return
     */
    Result<SyncDataContextEvent> getErpObjDataById(ErpIdArg erpIdArg, String dataCenterId, IdFieldKey idFieldKey);

    /**
     * 读取单条Erp对象数据接口
     * 注：只可以查询主对象。查找从对象数据也只可以通过查找主对象数据查找
     *
     * @param erpIdArg
     * @return
     */
    Result<SyncDataContextEvent> getErpObjDataFromMongoIfExist(ErpIdArg erpIdArg, String dataCenterId, IdFieldKey idFieldKey);

    /**
     * 通过id获取数据，返回list,适配一条数据被扩展为多条
     *
     * @param erpIdArg
     * @return
     */
    @DataMonitorScreen(tenantId = "#erpIdArg.tenantId", dataCenterId = "#dataCenterId", outSideObjApiName = "#erpIdArg.objAPIName", outDataCount = "#result?.getData()?.size()?:0",outSideObjId = "#erpIdArg.dataId",sourceSystemType = "2",operationType = CommonConstant.READ_OPERATE_TYPE,operateStatus="'s106240000'.equals(#result?.getErrCode())?1:2")
    Result<List<SyncDataContextEvent>> getReSyncObjDataById(ErpIdArg erpIdArg,String dataCenterId, IdFieldKey idFieldKey);

    /**
     * 作废Erp对象数据
     */
    Result<String> invalidErpObjData(StandardInvalidData standardInvalidData, String tenantId, String dataCenterId, final String syncPloyDetailSnapshotId);

    /**
     * 作废Erp对象明细数据
     */
    Result<String> invalidErpObjDetailData(StandardInvalidData standardInvalidData, String tenantId, String dataCenterId, final String syncPloyDetailSnapshotId);

    Result<String> recoverErpObjData(StandardRecoverData standardRecoverData, String tenantId, String dataCenterId, final String syncPloyDetailSnapshotId);
    /**
     * 删除Erp对象数据
     */
    Result<String> deleteErpObjData(StandardInvalidData standardInvalidData, String tenantId, String dataCenterId, final String syncPloyDetailSnapshotId);
}
