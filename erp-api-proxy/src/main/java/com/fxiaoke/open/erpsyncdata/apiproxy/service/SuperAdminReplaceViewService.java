package com.fxiaoke.open.erpsyncdata.apiproxy.service;


import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.K3DataConverter;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.ViewArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.ViewResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ListErpObjDataResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;

public interface SuperAdminReplaceViewService {

    Result<String> useView(String tenantId);

    Result<String> brushQueryCode(String tenantId,String dcId, String formId);

    List<String> getAllErpRealObj(String tenantId, String dcId);

    Result<ViewResult> getByExecuteBillQuery(String tenantId, String dcId, String formId, String dataId);

    Result<Object> compareViewAndBillQueryResult(String tenantId, String dcId, String formId, String dataId);

    Result<Object> compareAndSendMessage(String tenantId,String formId, K3CloudApiClient k3ApiClient, ViewArg viewArg, K3DataConverter converter);

    Result<Object> compareResult(String tenantId,String dataCenterId, String formId, K3DataConverter converter, Result<ViewResult> billResult, Result<ViewResult> viewResult);

    Result<String> brushAllQueryCode(String ignoreTenantIds);

    Result<String> deleteConfig(String tenantId,String type);

    Result<List<Result<ListErpObjDataResult>>> executeBillQuery(String tenantId);
}
