package com.fxiaoke.open.erpsyncdata.apiproxy.service;

import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 10:02 2020/11/6
 * @Desc:
 */
public interface SyncCpqService {
    void listErpProductByTime(List<StandardData> standardDataList, TimeFilterArg timeFilterArg,String dataCenterId);
    void getErpProductById(List<StandardData> standardDataList, ErpIdArg erpIdArg, String dataCenterId);
    List<StandardData> handleSapCpq(String tenantId, String dataCenterId, StandardData standardData, SyncPloyDetailEntity productPloyDetailEntity, SyncPloyDetailEntity productGroupPloyDetailEntity, SyncPloyDetailEntity bomPloyDetailEntity);
    SyncDataContextEvent addBomPathField(SyncDataContextEvent message);
    Boolean isNeedHandleSapCpq(String tenantId,String dcId);
    SyncPloyDetailEntity getPloyDetailByTypeAndTenantIdAndObjApiName(Integer type, String tenantId, String objApiName, String dataCenterId);
    List<SyncPloyDetailEntity> getAllSapErp2CrmCpqPloyDetails(String tenantId);
}
