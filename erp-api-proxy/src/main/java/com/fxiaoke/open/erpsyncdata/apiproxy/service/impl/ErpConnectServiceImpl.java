package com.fxiaoke.open.erpsyncdata.apiproxy.service.impl;

import cn.hutool.core.comparator.VersionComparator;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.DBProxyManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ProxyHttpClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorConfigHandler;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorHandlerFactory;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.BaseObjResult;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3Model;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate.data.GetAccessTokenData;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.ErpConnectService;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.K3UltimateApiService;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.PushIdentifyManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.i18n.I18nUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.GetConnectorIntroArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorAuthType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.K3CloudAuthType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.*;
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.ConnectorIntro;
import com.fxiaoke.open.erpsyncdata.preprocess.model.proxyservice.DbProxyServiceInfo;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/11/9
 */
@Service
@Slf4j
public class ErpConnectServiceImpl implements ErpConnectService {
    @Autowired
    private ErpConnectInfoManager connectInfoManager;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private ProxyHttpClient proxyHttpClient;
    @Autowired
    private K3UltimateApiService k3UltimateApiService;
    @Autowired
    private PushIdentifyManager pushIdentifyManager;

    private final static String redirectUriKey = "redirectUri";
    private final static String webhookUriKey = "webhookUri";

    /**
     * 获取K3CLoud数据中心列表
     *
     * @param baseUrl
     * @return
     */
    @Override
    public Result<List<DbInfo>> getK3CloudDbInfos(String tenantId, String dcId, String baseUrl) {
        K3CloudApiClient apiClient = new K3CloudApiClient(baseUrl);
        Result<List<BaseObjResult>> queryRes;
        if ("https://api.kingdee.com/galaxyapi/".equals(StringUtils.appendIfMissing(baseUrl, "/"))) {
            //公有云网关访问的查一下是否已经有连接信息
            ErpConnectInfoEntity connectInfo = connectInfoManager.getByIdAndTenantId(tenantId, dcId);
            if (connectInfo != null && connectInfo.getConnectParams() != null) {
                K3CloudApiClient k3CloudApiClient = K3CloudApiClient.newInstance(tenantId, connectInfo.getConnectParams(), dcId);
                //尝试用连接信息获取
                queryRes = k3CloudApiClient.queryDataCenterList();
                if (!queryRes.isSuccess()) {
                    return Result.copy(queryRes);
                }
            } else {
                //未连接时返回空数组
                return Result.newSuccess(new ArrayList<>());
            }
        } else {
            queryRes = apiClient.queryDataCenterList();
        }
        if (!queryRes.isSuccess()) {
            return Result.copy(queryRes);
        }
        List<DbInfo> list = new ArrayList<>();
        for (BaseObjResult objResult : queryRes.getData()) {
            DbInfo dbInfo = new DbInfo(objResult.getId(), objResult.getName());
            list.add(dbInfo);
        }
        return new Result<>(list);
    }

    /**
     * 检查连接参数
     *
     * @return
     */
    @Override
    public Result<Void> checkK3CloudParam(K3CloudConnectParam connectParam) {
        K3CloudApiClient apiClient = new K3CloudApiClient(connectParam.getBaseUrl());
        boolean login;
        apiClient.setConnectParam(connectParam);
        if (connectParam.newConfigIfNull().isUseAppToken()) {
            //调用查询物料接口验证
            QueryArg queryArg = new QueryArg();
            queryArg.setFormId(K3CloudForm.BD_MATERIAL);
            queryArg.setFieldKeys("FNumber");
            queryArg.setLimit(1);
            Result<List<K3Model>> listResult = apiClient.queryReturnMap(queryArg);
            if (!listResult.isSuccess()) {
                return Result.newError(ResultCodeEnum.THIRD_SYSTEM_AUTH_FAILED);
            }
        } else {
            if (connectParam.getAuthType().equals(K3CloudAuthType.USER)) {
                login = apiClient.loginK3();
            } else {
                login = apiClient.appLogin();
            }
            if (!login) {
                return Result.newError(ResultCodeEnum.THIRD_SYSTEM_AUTH_FAILED);
            }
        }
        return new Result<>();
    }

    @Override
    public Result<Void> checkK3CloudUltimateParam(K3UltimateConnectParam connectParam) {

        Result<GetAccessTokenData> result = k3UltimateApiService.getAccessTokenEx(connectParam);
//        Result<K3UltimateResponseByQuery> result1 =
//                k3UltimateApiService.batchQuery(connectParam.getTenantId(), null, "", null, false);
        if (result.isSuccess()) {
            return Result.newSuccess();
        }
        return Result.newError(ResultCodeEnum.THIRD_SYSTEM_AUTH_FAILED);
    }

    @Override
    public Result<DbProxyServiceInfo> getDbProxyServiceInfo(DBProxyConnectParam connectParam, String tenantId) {
        if (StrUtil.isBlank(connectParam.getBaseUrl())) {
            return Result.newError("baseUrl can not be empty");
        }
        DBProxyManager erpDataManager = (DBProxyManager) ConnectorHandlerFactory.getDataHandler(ErpChannelEnum.ERP_DB_PROXY.name());
        Result<DbProxyServiceInfo> proxyServiceInfoResult = erpDataManager.getProxyServiceInfo(connectParam);
        if (proxyServiceInfoResult.isSuccess()) {
            return proxyServiceInfoResult;
        } else {
            String version = connectParam.getVersion();
            //是否允许跳过检查,version为null或者小于1.1版本
            boolean canSkipCheck = VersionComparator.INSTANCE.compare(version, "1.1") < 0;
            if (canSkipCheck) {
                return Result.newSuccess(new DbProxyServiceInfo());
            }
            proxyServiceInfoResult.setErrMsg(i18NStringManager.get2(I18NStringEnum.s46.getI18nKey(),
                    I18nUtil.getLocaleFromTrace(),
                    tenantId,
                    String.format(I18NStringEnum.s46.getI18nValue(), proxyServiceInfoResult.getErrMsg()),
                    Lists.newArrayList(proxyServiceInfoResult.getErrMsg())));
            return proxyServiceInfoResult;
        }
    }

    @Override
    public Result<List<ConnectorAuthType>> getConnectorAuthTypeList(String tenantId, String dcId) {
        ErpConnectInfoEntity connectInfo = connectInfoManager.getByIdAndTenantId(tenantId, dcId);
        ErpChannelEnum channel = connectInfo.getChannel();
        ConnectorConfigHandler configHandler = ConnectorHandlerFactory.getConfigHandlerNotNull(channel, connectInfo.getConnectParams());
        Result<List<ConnectorAuthType>> authTypeList = configHandler.getAuthTypeList(connectInfo);
        return authTypeList;
    }

    @Override
    public Result<ConnectorIntro> getConnectorIntro(String tenantId, String dcId, GetConnectorIntroArg arg) {
        ErpConnectInfoEntity connectInfo = connectInfoManager.getByIdAndTenantId(tenantId, dcId);
        ErpChannelEnum channel = connectInfo.getChannel();
        ConnectorConfigHandler configHandler = ConnectorHandlerFactory.getConfigHandlerNotNull(channel, connectInfo.getConnectParams());
        Result<ConnectorIntro> connectorIntroRes = configHandler.getConnectorIntro(connectInfo, arg);
        //赋值平台级别默认值
        if (connectorIntroRes.isSuccess()) {
            ConnectorIntro connectorIntro = connectorIntroRes.getData();
            ConnectorAuthType authType = arg.getAuthType();
            if (connectorIntro.getParamSchemas() == null) {
                connectorIntro.setParamSchemas(new ArrayList<>());
            }
            //对webhook地址赋默认值
            Optional<ConnectorIntro.ParamSchema> webhookParam = connectorIntro.getParamSchemas().stream()
                    .filter(v -> webhookUriKey.equals(v.getKey())).findAny();
            if (webhookParam.isPresent()) {
                //存在时，赋默认值,是否显示由连接器决定
                ConnectorIntro.ParamSchema schema = webhookParam.get();
                schema.setUserEditable(false);
                schema.setDefaultValue(pushIdentifyManager.buildWebhookUrl(tenantId, dcId));
            }
            if (ConnectorAuthType.OAUTH2.equals(authType)) {
                //Oauth2对重定向地址赋默认值
                Optional<ConnectorIntro.ParamSchema> schemaOpt = connectorIntro.getParamSchemas().stream().filter(v -> redirectUriKey.equals(v.getKey())).findAny();
                ConnectorIntro.ParamSchema schema;
                if (schemaOpt.isPresent()) {
                    schema = schemaOpt.get();
                } else {
                    schema = new ConnectorIntro.ParamSchema();
                    schema.setLabel(I18NStringEnum.s3754.getText());
                    schema.setKey(redirectUriKey);
                    connectorIntro.getParamSchemas().add(schema);
                }
                schema.setUserEditable(false);
                schema.setShow(true);
                schema.setTooltip(I18NStringEnum.s3306.getNameByTraceLocale());
                schema.setDefaultValue(ConfigCenter.ERP_DOMAIN_URL + "/erp/syncdata/noAuth/standardConnector/callback");
            }
        }
        return connectorIntroRes;
    }

    @Override
    public Result<SystemParams> processUserInputSystemParams(String tenantId, String dcId, SystemParams systemParams) {
        ErpConnectInfoEntity connectInfo = connectInfoManager.getByIdAndTenantId(tenantId, dcId);
        ErpChannelEnum channel = connectInfo.getChannel();
        ConnectorConfigHandler configHandler = ConnectorHandlerFactory.getConfigHandlerNotNull(channel, connectInfo.getConnectParams());
        Result<SystemParams> result = configHandler.processUserInputSystemParams(connectInfo, systemParams);
        return result;
    }

    @Override
    public Result<String> getOAuth2AuthUrl(String tenantId, String dcId, SystemParams systemParams) {
        ErpConnectInfoEntity connectInfo = connectInfoManager.getByIdAndTenantId(tenantId, dcId);
        ErpChannelEnum channel = connectInfo.getChannel();
        //填充重定向地址固定值
        if (!systemParams.containsKey(redirectUriKey)) {
            return Result.newErrorExtra(ResultCodeEnum.MISSING_PARAM, redirectUriKey);
        }
        systemParams.putIfAbsent(redirectUriKey, ConfigCenter.ERP_DOMAIN_URL + "/erp/syncdata/noAuth/standardConnector/callback");
        ConnectorConfigHandler configHandler = ConnectorHandlerFactory.getConfigHandlerNotNull(channel, connectInfo.getConnectParams());
        return configHandler.getOAuth2AuthUrl(connectInfo, systemParams);
    }

    @Override
    public Result<Void> checkAuthStatus(String tenantId, String dcId) {
        ErpConnectInfoEntity connectInfo = connectInfoManager.getByIdAndTenantId(tenantId, dcId);
        ErpChannelEnum channel = connectInfo.getChannel();
        ConnectorConfigHandler configHandler = ConnectorHandlerFactory.getConfigHandlerNotNull(channel, connectInfo.getConnectParams());
        return configHandler.checkAuthStatus(connectInfo);
    }

    @Override
    public Result<Void> actuatorSAPProxyStatus(String serviceUrl) {
        String statusURL=new StringBuilder().append(serviceUrl).append("/proxy/sapProxy/connectFunction").toString();
        Result urlResult = proxyHttpClient.getUrl(statusURL, Maps.newHashMap(), new TypeReference<Result>() {
        });
        return urlResult;
    }
}
