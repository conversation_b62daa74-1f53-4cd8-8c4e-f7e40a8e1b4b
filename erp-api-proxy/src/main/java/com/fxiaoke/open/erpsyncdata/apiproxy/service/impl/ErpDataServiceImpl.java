package com.fxiaoke.open.erpsyncdata.apiproxy.service.impl;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ErpTempDataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.SpecialWayDataService;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.BaseErpDataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorDataHandler;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorHandlerFactory;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.*;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.ErpCustomInterfaceService;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.ErpDataService;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.ConnectParamUtil;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.FormatConvertUtil;
import com.fxiaoke.open.erpsyncdata.common.constant.DataReceiveTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpCustomInterfaceDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjGroovyDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectFieldDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.*;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.model.IdFieldKey;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/24
 */
@Service("erpDataService")
@Slf4j
public class ErpDataServiceImpl implements ErpDataService {
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private ErpObjGroovyDao erpObjGroovyDao;
    @Autowired
    private SpecialWayDataService specialWayDataService;
    @Autowired
    private ErpTempDataManager erpTempDataManager;
    @Autowired
    private ErpObjectFieldDao erpObjectFieldDao;
    @Autowired
    private SyncDataFixDao adminSyncDataDao;
    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;
    @Autowired
    private GetByIdBreakManager getByIdBreakManager;
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    private ErpCustomInterfaceDao erpCustomInterfaceDao;
    @Autowired
    private ErpCustomInterfaceService erpCustomInterfaceService;
    @Autowired
    private ErpObjInterfaceCheckedManager erpObjInterfaceCheckedManager;

    @Override
    public Result<StandardListData> listStandardErpObjDataByTime(TimeFilterArg timeFilterArg,String dataCenterId) {
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(timeFilterArg.getTenantId(),dataCenterId);
        ErpChannelEnum channel = connectInfo.getChannel();
        ConnectorDataHandler erpDataManager = ConnectorHandlerFactory.getDataHandler(channel,connectInfo.getConnectParams());
        ErpObjInterfaceUrlEnum interfaceUrlEnum = EventTypeEnum.INVALID.match(timeFilterArg.getOperationType()) ? ErpObjInterfaceUrlEnum.queryInvalid : ErpObjInterfaceUrlEnum.queryMasterBatch;
        ErpObjGroovyEntity erpObjGroovyEntity = erpObjGroovyDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(timeFilterArg.getTenantId())).getByTenantIdAndApiNameAndUrl(timeFilterArg.getTenantId(),dataCenterId,
                timeFilterArg.getObjAPIName(), interfaceUrlEnum.name());

        ErpObjInterfaceCheckedEntity checkedEntity = erpObjInterfaceCheckedManager.findOne(timeFilterArg.getTenantId(),
                dataCenterId,
                timeFilterArg.getObjAPIName(),
                interfaceUrlEnum);

        //获取数据
        Result<StandardListData> apiFormat = null;

        List<String> pushDataObjApiNameList = ConnectParamUtil.getPushDataObjApiNameList(connectInfo);
        log.info("ErpDataServiceImpl.listStandardErpObjDataByTime,ei={},pushDataObjApiNameList={}", timeFilterArg.getTenantId(), pushDataObjApiNameList);
        if (checkedEntity != null) {
            ErpCustomInterfaceEntity customInterfaceEntity = erpCustomInterfaceDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(timeFilterArg.getTenantId()))
                    .findData(timeFilterArg.getTenantId(), dataCenterId, timeFilterArg.getObjAPIName(), interfaceUrlEnum);

            // 如果策略是推送，即pushDataObjApiNameList存在对应的ERP对象，则优先查询DB，与同步规则保持一致（虽然推送数据已经不存推送表）
            if (pushDataObjApiNameList != null && pushDataObjApiNameList.contains(timeFilterArg.getObjAPIName())) {//调用db
                apiFormat = specialWayDataService.listErpObjDataByTimeWithDB(timeFilterArg, dataCenterId);
            } else if (checkedEntity.getCheckedInterfaceType() == ErpObjInterfaceTypeEnum.CUSTOM_FUNC && erpObjGroovyEntity != null) {//直接调用groovy
                apiFormat = specialWayDataService.listErpObjDataByTimeWithGroovy(timeFilterArg, erpObjGroovyEntity, dataCenterId);
            } else if (checkedEntity.getCheckedInterfaceType() == ErpObjInterfaceTypeEnum.CUSTOM_API && customInterfaceEntity != null) {
                apiFormat = erpCustomInterfaceService.listErpObjDataByTime(timeFilterArg, customInterfaceEntity);
            } else {
                //#################################调用连接器接口的逻辑在这里##############################################
                apiFormat = erpDataManager.listErpObjDataByTime(timeFilterArg, connectInfo);
            }
        } else {
            // 如果策略是推送，即pushDataObjApiNameList存在对应的ERP对象，则优先查询DB，与同步规则保持一致（虽然推送数据已经不存推送表）
            if (pushDataObjApiNameList != null && pushDataObjApiNameList.contains(timeFilterArg.getObjAPIName())) {//调用db
                apiFormat = specialWayDataService.listErpObjDataByTimeWithDB(timeFilterArg, dataCenterId);
            } else if (erpObjGroovyEntity != null) {//直接调用groovy
                apiFormat = specialWayDataService.listErpObjDataByTimeWithGroovy(timeFilterArg, erpObjGroovyEntity, dataCenterId);
            } else {
                //#################################调用连接器接口的逻辑在这里##############################################
                apiFormat = erpDataManager.listErpObjDataByTime(timeFilterArg, connectInfo);
            }
        }

        //解析结果后判断是否保存结果
        BaseErpDataManager.saveWaitingInterfaceMonitor(!apiFormat.isSuccess());
        if(apiFormat.isSuccess() && apiFormat.getData()!=null && CollectionUtils.isNotEmpty(apiFormat.getData().getDataList())) {
            List<ErpObjectFieldEntity> erpObjectFieldEntities = getErpObjectFieldEntities(timeFilterArg.getTenantId(), timeFilterArg.getObjAPIName(), connectInfo.getChannel(), dataCenterId);
            for(StandardData standardData : apiFormat.getData().getDataList()) {
                convertSelectOneFieldValue2String(
                  standardData, erpObjectFieldEntities);
            }
        }

        return apiFormat;
    }

    /**
     * 创建Erp对象数据
     * 创建对象不做id字段转换，创建后回传id，预处理模块会把映射关系的id转换成回传的id。
     *
     * @param doWriteMqData
     * @return
     */
    @Override
    public Result<ErpIdResult> createErpObjData(SyncDataContextEvent doWriteMqData, String dataCenterId) {
        ErpObjInterfaceUrlEnum interfaceUrlEnum = ErpObjInterfaceUrlEnum.create;
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(doWriteMqData.getDestTenantId(), dataCenterId);
        ErpChannelEnum channel = connectInfo.getChannel();
        ConnectorDataHandler erpDataManager = ConnectorHandlerFactory.getDataHandler(channel,connectInfo.getConnectParams());
        ErpObjGroovyEntity erpObjGroovyEntity = erpObjGroovyDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(doWriteMqData.getDestTenantId()))
                .getByTenantIdAndApiNameAndUrl(doWriteMqData.getDestTenantId(), dataCenterId, doWriteMqData.getDestObjectApiName(), interfaceUrlEnum.name());

        StandardData standardData = FormatConvertUtil.crm2StdErp(doWriteMqData);
        StandardData linkedStandardData = StandardData.convert(standardData,
                erpObjectFieldDao,
                adminSyncDataDao,
                doWriteMqData.getTenantId(),
                dataCenterId);

        ErpObjInterfaceCheckedEntity checkedEntity = erpObjInterfaceCheckedManager.findOne(doWriteMqData.getDestTenantId(),
                dataCenterId,
                linkedStandardData.getObjAPIName(),
                interfaceUrlEnum);

        Result<ErpIdResult> erpIdResult = null;
        if(checkedEntity!=null) {
            ErpCustomInterfaceEntity customInterfaceEntity = erpCustomInterfaceDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(doWriteMqData.getDestTenantId()))
                    .findData(doWriteMqData.getDestTenantId(), dataCenterId, linkedStandardData.getObjAPIName(), interfaceUrlEnum);

            if (checkedEntity.getCheckedInterfaceType() == ErpObjInterfaceTypeEnum.CUSTOM_FUNC && erpObjGroovyEntity != null) {
                erpIdResult = specialWayDataService.createErpObjDataWithGroovy(doWriteMqData, linkedStandardData, erpObjGroovyEntity, dataCenterId);
            } else if (checkedEntity.getCheckedInterfaceType() == ErpObjInterfaceTypeEnum.CUSTOM_API && customInterfaceEntity != null) {
                erpIdResult = erpCustomInterfaceService.createErpObjData(linkedStandardData,customInterfaceEntity);
            } else {
                erpIdResult = erpDataManager.createErpObjData(linkedStandardData, connectInfo);
            }
        } else {
            if (erpObjGroovyEntity != null) {
                erpIdResult = specialWayDataService.createErpObjDataWithGroovy(doWriteMqData, linkedStandardData, erpObjGroovyEntity, dataCenterId);
            } else {
                erpIdResult = erpDataManager.createErpObjData(linkedStandardData, connectInfo);
            }
        }

        //解析结果后判断是否保存结果
        BaseErpDataManager.saveWaitingInterfaceMonitor(!erpIdResult.isSuccess());
        return erpIdResult;
    }

    /**
     * 单据新建Erp对象明细数据
     *
     * @param doWriteMqData
     * @return
     */
    @Override
    public Result<StandardDetailId> createErpObjDetailData(SyncDataContextEvent doWriteMqData, String dataCenterId) {
        ErpObjInterfaceUrlEnum interfaceUrlEnum = ErpObjInterfaceUrlEnum.create;
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(doWriteMqData.getDestTenantId(), dataCenterId);
        ErpChannelEnum channel = connectInfo.getChannel();
        ConnectorDataHandler erpDataManager = ConnectorHandlerFactory.getDataHandler(channel,connectInfo.getConnectParams());
        ErpObjGroovyEntity erpObjGroovyEntity = erpObjGroovyDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(doWriteMqData.getDestTenantId()))
                .getByTenantIdAndApiNameAndUrl(doWriteMqData.getDestTenantId(), dataCenterId, doWriteMqData.getDestObjectApiName(), interfaceUrlEnum.name());

        StandardDetailData standardDetailData = FormatConvertUtil.crm2StdDetailErp(doWriteMqData);
        StandardDetailData linkedStandardDetailData = StandardDetailData.convert(standardDetailData,
                erpObjectFieldDao,
                adminSyncDataDao,
                doWriteMqData.getDestTenantId(),
                connectInfo.getId());

        ErpObjInterfaceCheckedEntity checkedEntity = erpObjInterfaceCheckedManager.findOne(doWriteMqData.getDestTenantId(),
                dataCenterId,
                linkedStandardDetailData.getObjAPIName(),
                interfaceUrlEnum);

        Result<StandardDetailId> erpIdResult = null;
        if(checkedEntity!=null) {
            ErpCustomInterfaceEntity customInterfaceEntity = erpCustomInterfaceDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(doWriteMqData.getDestTenantId()))
                    .findData(doWriteMqData.getDestTenantId(), dataCenterId, linkedStandardDetailData.getObjAPIName(), interfaceUrlEnum);

            if (checkedEntity.getCheckedInterfaceType() == ErpObjInterfaceTypeEnum.CUSTOM_FUNC && erpObjGroovyEntity != null) {
                erpIdResult = specialWayDataService.createErpObjDetailDataWithGroovy(doWriteMqData, linkedStandardDetailData, erpObjGroovyEntity, dataCenterId);
            } else if(checkedEntity.getCheckedInterfaceType() == ErpObjInterfaceTypeEnum.CUSTOM_API && customInterfaceEntity!=null) {
                erpIdResult = erpCustomInterfaceService.createErpObjDetailData(doWriteMqData,linkedStandardDetailData,customInterfaceEntity);
            } else {
                erpIdResult = erpDataManager.createErpObjDetailData(doWriteMqData, linkedStandardDetailData, connectInfo);
            }
        } else {
            if (erpObjGroovyEntity != null) {
                erpIdResult = specialWayDataService.createErpObjDetailDataWithGroovy(doWriteMqData, linkedStandardDetailData, erpObjGroovyEntity, dataCenterId);
            } else {
                erpIdResult = erpDataManager.createErpObjDetailData(doWriteMqData, linkedStandardDetailData, connectInfo);
            }
        }

        //解析结果后判断是否保存结果
        BaseErpDataManager.saveWaitingInterfaceMonitor(!erpIdResult.isSuccess());
        return erpIdResult;
    }

    /**
     * 更新erp对象数据
     *
     * @param doWriteMqData
     * @return
     */
    @Override
    public Result<ErpIdResult> updateErpObjData(SyncDataContextEvent doWriteMqData, String dataCenterId) {
        ErpObjInterfaceUrlEnum interfaceUrlEnum = ErpObjInterfaceUrlEnum.update;
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(doWriteMqData.getDestTenantId(), dataCenterId);
        ErpChannelEnum channel = connectInfo.getChannel();
        ConnectorDataHandler erpDataManager = ConnectorHandlerFactory.getDataHandler(channel,connectInfo.getConnectParams());
        ErpObjGroovyEntity erpObjGroovyEntity = erpObjGroovyDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(doWriteMqData.getDestTenantId()))
                .getByTenantIdAndApiNameAndUrl(doWriteMqData.getDestTenantId(), dataCenterId, doWriteMqData.getDestObjectApiName(), interfaceUrlEnum.name());

        StandardData standardData = FormatConvertUtil.crm2StdErp(doWriteMqData);
        StandardData linkedStandardData = StandardData.convert(standardData,
                erpObjectFieldDao,
                adminSyncDataDao,
                doWriteMqData.getTenantId(),
                dataCenterId);

        ErpObjInterfaceCheckedEntity checkedEntity = erpObjInterfaceCheckedManager.findOne(doWriteMqData.getDestTenantId(),
                dataCenterId,
                linkedStandardData.getObjAPIName(),
                interfaceUrlEnum);

        Result<ErpIdResult> erpIdResult = null;
        if(checkedEntity!=null) {
            ErpCustomInterfaceEntity customInterfaceEntity = erpCustomInterfaceDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(doWriteMqData.getDestTenantId()))
                    .findData(doWriteMqData.getDestTenantId(), dataCenterId, linkedStandardData.getObjAPIName(), interfaceUrlEnum);

            if (checkedEntity.getCheckedInterfaceType() == ErpObjInterfaceTypeEnum.CUSTOM_FUNC && erpObjGroovyEntity != null) {
                erpIdResult = specialWayDataService.updateErpObjDataWithGroovy(doWriteMqData, linkedStandardData, erpObjGroovyEntity, dataCenterId);
            } else if(checkedEntity.getCheckedInterfaceType() == ErpObjInterfaceTypeEnum.CUSTOM_API && customInterfaceEntity!=null) {
                erpIdResult = erpCustomInterfaceService.updateErpObjData(linkedStandardData,customInterfaceEntity);
            } else {
                erpIdResult = erpDataManager.updateErpObjData(linkedStandardData, connectInfo);
            }
        } else {
            if (erpObjGroovyEntity != null) {
                erpIdResult = specialWayDataService.updateErpObjDataWithGroovy(doWriteMqData, linkedStandardData, erpObjGroovyEntity, dataCenterId);
            } else {
                erpIdResult = erpDataManager.updateErpObjData(linkedStandardData, connectInfo);
            }
        }

        //解析结果后判断是否保存结果
        BaseErpDataManager.saveWaitingInterfaceMonitor(!erpIdResult.isSuccess());
        return erpIdResult;
    }

    /**
     * 单独更新Erp对象明细数据
     *
     * @param doWriteMqData
     * @return
     */
    @Override
    public Result<StandardDetailId> updateErpObjDetailData(SyncDataContextEvent doWriteMqData, String dataCenterId) {
        ErpObjInterfaceUrlEnum interfaceUrlEnum = ErpObjInterfaceUrlEnum.update;
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(doWriteMqData.getDestTenantId(), dataCenterId);
        ErpChannelEnum channel = connectInfo.getChannel();
        ErpObjGroovyEntity erpObjGroovyEntity = erpObjGroovyDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(doWriteMqData.getDestTenantId()))
                .getByTenantIdAndApiNameAndUrl(doWriteMqData.getDestTenantId(), dataCenterId, doWriteMqData.getDestObjectApiName(), interfaceUrlEnum.name());

        StandardDetailData standardDetailData = FormatConvertUtil.crm2StdDetailErp(doWriteMqData);
        StandardDetailData linkedStandardDetailData = StandardDetailData.convert(standardDetailData,
                erpObjectFieldDao,
                adminSyncDataDao,
                doWriteMqData.getDestTenantId(),
                connectInfo.getId());

        ErpObjInterfaceCheckedEntity checkedEntity = erpObjInterfaceCheckedManager.findOne(doWriteMqData.getDestTenantId(),
                dataCenterId,
                linkedStandardDetailData.getObjAPIName(),
                interfaceUrlEnum);

        Result<StandardDetailId> erpIdResult = null;
        if(checkedEntity!=null) {
            ErpCustomInterfaceEntity customInterfaceEntity = erpCustomInterfaceDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(doWriteMqData.getDestTenantId()))
                    .findData(doWriteMqData.getDestTenantId(), dataCenterId, linkedStandardDetailData.getObjAPIName(), interfaceUrlEnum);

            if (checkedEntity.getCheckedInterfaceType() == ErpObjInterfaceTypeEnum.CUSTOM_FUNC && erpObjGroovyEntity != null) {
                erpIdResult = specialWayDataService.updateErpObjDetailDataWithGroovy(doWriteMqData, linkedStandardDetailData, erpObjGroovyEntity);
            } else if(checkedEntity.getCheckedInterfaceType() == ErpObjInterfaceTypeEnum.CUSTOM_API && customInterfaceEntity!=null) {
                erpIdResult = erpCustomInterfaceService.updateErpObjDetailData(doWriteMqData, linkedStandardDetailData, customInterfaceEntity);
            } else {
                ConnectorDataHandler erpDataManager = ConnectorHandlerFactory.getDataHandler(channel,connectInfo.getConnectParams());
                erpIdResult = erpDataManager.updateErpObjDetailData(doWriteMqData, linkedStandardDetailData, connectInfo);
            }
        } else {
            if (erpObjGroovyEntity != null) {
                erpIdResult = specialWayDataService.updateErpObjDetailDataWithGroovy(doWriteMqData, linkedStandardDetailData, erpObjGroovyEntity);
            } else {
                ConnectorDataHandler erpDataManager = ConnectorHandlerFactory.getDataHandler(channel,connectInfo.getConnectParams());
                erpIdResult = erpDataManager.updateErpObjDetailData(doWriteMqData, linkedStandardDetailData, connectInfo);
            }
        }

        //解析结果后判断是否保存结果
        BaseErpDataManager.saveWaitingInterfaceMonitor(!erpIdResult.isSuccess());
        return erpIdResult;
    }



    @Override
    public Result<StandardData> getStandardErpObjDataById(ErpIdArg oriErpIdArg,String dataCenterId, IdFieldKey idFieldKey) {
        //new idArg
        ErpIdArg erpIdArg = new ErpIdArg();
        BeanUtils.copyProperties(oriErpIdArg, erpIdArg);
        //转换复合Id参数
        erpIdArg.setDataId(idFieldKey.getCompositeIdExtend().getMasterId(erpIdArg.getDataId()));
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(erpIdArg.getTenantId(),dataCenterId);
        ErpChannelEnum channel = connectInfo.getChannel();

        boolean isBreak = getByIdBreakManager.isBreak(erpIdArg.getTenantId(),dataCenterId,channel,erpIdArg.getObjAPIName());
        if(isBreak) {
            LogIdUtil.clear();
            return Result.newError(ResultCodeEnum.GET_BY_ID_BREAK);
        }

        ErpObjInterfaceUrlEnum interfaceUrlEnum = ErpObjInterfaceUrlEnum.queryMasterById;

        ConnectorDataHandler erpDataManager = ConnectorHandlerFactory.getDataHandler(channel,connectInfo.getConnectParams());
        ErpObjGroovyEntity erpObjGroovyEntity = erpObjGroovyDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(erpIdArg.getTenantId()))
                .getByTenantIdAndApiNameAndUrl(erpIdArg.getTenantId(),dataCenterId, erpIdArg.getObjAPIName(), interfaceUrlEnum.name());
        List<String> pushDataObjApiNameList = ConnectParamUtil.getPushDataObjApiNameList(connectInfo);

        ErpObjInterfaceCheckedEntity checkedEntity = erpObjInterfaceCheckedManager.findOne(erpIdArg.getTenantId(),
                dataCenterId,
                erpIdArg.getObjAPIName(),
                interfaceUrlEnum);

        //获取数据
        Result<StandardData> standardDataResult =null;

        if(checkedEntity!=null) {
            ErpCustomInterfaceEntity customInterfaceEntity = erpCustomInterfaceDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(erpIdArg.getTenantId()))
                    .findData(erpIdArg.getTenantId(), dataCenterId, erpIdArg.getObjAPIName(), interfaceUrlEnum);

            if (checkedEntity.getCheckedInterfaceType() == ErpObjInterfaceTypeEnum.CUSTOM_FUNC && erpObjGroovyEntity != null) {//直接调用groovy
                standardDataResult = specialWayDataService.getErpObjDataByIdWithGroovy(erpIdArg, erpObjGroovyEntity);
            } else if (pushDataObjApiNameList != null && pushDataObjApiNameList.contains(erpIdArg.getObjAPIName())) {//调用mongo
                standardDataResult = specialWayDataService.getErpObjDataByIdWithDB(erpIdArg, dataCenterId);
            } else if(checkedEntity.getCheckedInterfaceType() == ErpObjInterfaceTypeEnum.CUSTOM_API && customInterfaceEntity!=null) {
                standardDataResult = erpCustomInterfaceService.getErpObjData(erpIdArg,customInterfaceEntity);
            } else {
                /**如果客户没有提供getbyid接口实现，不调用erp接口了。*/
                isBreak = getByIdBreakManager.isTenantConfigBreak(erpIdArg.getTenantId(),dataCenterId,erpIdArg.getObjAPIName());
                if(isBreak) {
                    LogIdUtil.clear();
                    return Result.newError(ResultCodeEnum.GET_BY_ID_BREAK);
                }
                standardDataResult = erpDataManager.getErpObjData(erpIdArg, connectInfo);
            }
        } else {
            if (erpObjGroovyEntity != null) {//直接调用groovy
                standardDataResult = specialWayDataService.getErpObjDataByIdWithGroovy(erpIdArg, erpObjGroovyEntity);
            } else if (pushDataObjApiNameList != null && pushDataObjApiNameList.contains(erpIdArg.getObjAPIName())) {//调用mongo
                standardDataResult = specialWayDataService.getErpObjDataByIdWithDB(erpIdArg, dataCenterId);
            } else {
                /**如果客户没有提供getbyid接口实现，不调用erp接口了。*/
                isBreak = getByIdBreakManager.isTenantConfigBreak(erpIdArg.getTenantId(),dataCenterId,erpIdArg.getObjAPIName());
                if(isBreak) {
                    LogIdUtil.clear();
                    return Result.newError(ResultCodeEnum.GET_BY_ID_BREAK);
                }
                standardDataResult = erpDataManager.getErpObjData(erpIdArg, connectInfo);
            }
        }

        //解析结果后判断是否保存结果
        BaseErpDataManager.saveWaitingInterfaceMonitor(!standardDataResult.isSuccess());
        List<ErpObjectFieldEntity> erpObjectFieldEntities = getErpObjectFieldEntities(erpIdArg.getTenantId(), erpIdArg.getObjAPIName(), connectInfo.getChannel(), dataCenterId);
        convertSelectOneFieldValue2String(standardDataResult.getData(), erpObjectFieldEntities);
        //入库
        erpTempDataManager.singleInsertErpData(erpIdArg,dataCenterId,standardDataResult,idFieldKey);

        if (!standardDataResult.isSuccess()) {
            if (!StringUtils.equalsIgnoreCase(standardDataResult.getErrCode(), ResultCodeEnum.GET_BY_ID_BREAK.getErrCode())) {
                getByIdBreakManager.increaseFailedCount(erpIdArg.getTenantId(),
                        dataCenterId,
                        connectInfo.getChannel(),
                        erpIdArg.getObjAPIName(),
                        standardDataResult.getErrMsg());
            }
        }
        return standardDataResult;
    }

    @Override
    public Result<SyncDataContextEvent> getErpObjDataById(ErpIdArg erpIdArg, String dataCenterId, IdFieldKey idFieldKey) {
        //获取数据
        Result<StandardData> standardDataResult =getStandardErpObjDataById(erpIdArg,dataCenterId,idFieldKey);

        Result<SyncDataContextEvent> targetResult = FormatConvertUtil.resultStdErp2Crm(erpIdArg.getTenantId(),erpIdArg.getObjAPIName(), standardDataResult);
        return targetResult;
    }

    @Override
    public Result<SyncDataContextEvent> getErpObjDataFromMongoIfExist(ErpIdArg erpIdArg, String dataCenterId, IdFieldKey idFieldKey) {
        Result<StandardData> standardDataResult = erpTempDataManager.getErpObjDataFromMongo(erpIdArg, dataCenterId);
        if (standardDataResult == null || !standardDataResult.isSuccess() || standardDataResult.getData() == null) {
            getStandardErpObjDataById(erpIdArg,dataCenterId,idFieldKey);
            //再次从库取数据
            standardDataResult = erpTempDataManager.getErpObjDataFromMongo(erpIdArg, dataCenterId);
        }

        return FormatConvertUtil.resultStdErp2Crm(erpIdArg.getTenantId(),erpIdArg.getObjAPIName(), standardDataResult);
    }

    @Override
    public Result<List<SyncDataContextEvent>> getReSyncObjDataById(ErpIdArg oriErpIdArg,String dataCenterId,IdFieldKey idFieldKey) {
        log.info("trace getReSyncObjDataById erpIdArg{}",oriErpIdArg);
        //new idArg
        ErpIdArg erpIdArg = new ErpIdArg();
        BeanUtils.copyProperties(oriErpIdArg, erpIdArg);
        //转换复合Id参数
        erpIdArg.setDataId(idFieldKey.getCompositeIdExtend().getMasterId(erpIdArg.getDataId()));
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(erpIdArg.getTenantId(),dataCenterId);
        ErpChannelEnum channel = connectInfo.getChannel();

        boolean isBreak = getByIdBreakManager.isBreak(erpIdArg.getTenantId(),dataCenterId,channel,erpIdArg.getObjAPIName());
        if(isBreak) {
            return Result.newError(ResultCodeEnum.GET_BY_ID_BREAK);
        }

        ErpObjInterfaceUrlEnum interfaceUrlEnum = ErpObjInterfaceUrlEnum.queryMasterById;

        ConnectorDataHandler erpDataManager = ConnectorHandlerFactory.getDataHandler(channel,connectInfo.getConnectParams());
        ErpObjGroovyEntity erpObjGroovyEntity = erpObjGroovyDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(erpIdArg.getTenantId()))
                .getByTenantIdAndApiNameAndUrl(erpIdArg.getTenantId(),dataCenterId, erpIdArg.getObjAPIName(), interfaceUrlEnum.name());
        List<String> pushDataObjApiNameList = ConnectParamUtil.getPushDataObjApiNameList(connectInfo);

        ErpObjInterfaceCheckedEntity checkedEntity = erpObjInterfaceCheckedManager.findOne(erpIdArg.getTenantId(),
                dataCenterId,
                erpIdArg.getObjAPIName(),
                interfaceUrlEnum);

        //获取数据
        Result<List<StandardData>> targetResult =null;

        if(checkedEntity!=null) {
            ErpCustomInterfaceEntity customInterfaceEntity = erpCustomInterfaceDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(erpIdArg.getTenantId()))
                    .findData(erpIdArg.getTenantId(), dataCenterId, erpIdArg.getObjAPIName(), interfaceUrlEnum);

            if (checkedEntity.getCheckedInterfaceType() == ErpObjInterfaceTypeEnum.CUSTOM_FUNC && erpObjGroovyEntity != null) {//直接调用groovy
                targetResult = specialWayDataService.getReSyncObjDataByIdWithGroovy(erpIdArg, erpObjGroovyEntity, dataCenterId);
            } else if (pushDataObjApiNameList != null && pushDataObjApiNameList.contains(erpIdArg.getObjAPIName())) {
                //调用db 临时库 需要使用原始的id。目前只有K3的订单，会在前面因为复合id更换id值
                ErpIdArg idArgQueryMongo = BeanUtil.copy(erpIdArg, ErpIdArg.class);
                idArgQueryMongo.setDataId(oriErpIdArg.getDataId());
                targetResult = specialWayDataService.getReSyncObjDataByIdWithDB(idArgQueryMongo, dataCenterId);
            } else if(checkedEntity.getCheckedInterfaceType() == ErpObjInterfaceTypeEnum.CUSTOM_API && customInterfaceEntity!=null) {
                targetResult = erpCustomInterfaceService.getReSyncObjDataById(erpIdArg, customInterfaceEntity, connectInfo);
            } else {
                targetResult = erpDataManager.getReSyncObjDataById(erpIdArg, connectInfo);
            }
        } else {
            if (erpObjGroovyEntity != null) {//直接调用groovy
                targetResult = specialWayDataService.getReSyncObjDataByIdWithGroovy(erpIdArg, erpObjGroovyEntity, dataCenterId);
            } else if (pushDataObjApiNameList != null && pushDataObjApiNameList.contains(erpIdArg.getObjAPIName())) {
                //调用db 临时库 需要使用原始的id。目前只有K3的订单，会在前面因为复合id更换id值
                ErpIdArg idArgQueryMongo = BeanUtil.copy(erpIdArg, ErpIdArg.class);
                idArgQueryMongo.setDataId(oriErpIdArg.getDataId());
                targetResult = specialWayDataService.getReSyncObjDataByIdWithDB(idArgQueryMongo, dataCenterId);
            } else {
                targetResult = erpDataManager.getReSyncObjDataById(erpIdArg, connectInfo);
            }
        }

        //解析结果后判断是否保存结果
        BaseErpDataManager.saveWaitingInterfaceMonitor(targetResult == null || !targetResult.isSuccess());
        if (targetResult == null) {
            return null;
        } else if (!targetResult.isSuccess()) {
            Result<List<SyncDataContextEvent>> allDataResult = new Result<>();
            allDataResult.setErrMsg(targetResult.getErrMsg());
            allDataResult.setErrCode(targetResult.getErrCode());
            return allDataResult;
        } else {
            List<SyncDataContextEvent> allData = Lists.newArrayList();
            List<ErpObjectFieldEntity> erpObjectFieldEntities = getErpObjectFieldEntities(erpIdArg.getTenantId(), erpIdArg.getObjAPIName(), connectInfo.getChannel(), dataCenterId);
            for (StandardData standardData : targetResult.getData()) {
                convertSelectOneFieldValue2String(
                        standardData, erpObjectFieldEntities);
                //入库
                //前置判断来源数据类型type
                Integer dataType=ObjectUtils.isNotEmpty(TaskUtil.getTaskNumInfo())?DataReceiveTypeEnum.FROM_ERP_HISTORY_TASK.getType():DataReceiveTypeEnum.AUTO_POLLING_ERP.getType();
                String mongoId = erpTempDataManager.singleInsertErpData(erpIdArg, dataCenterId, Result.newSuccess(standardData), idFieldKey);
                if (mongoId != null && standardData.getMasterFieldVal() != null) {
                    standardData.getMasterFieldVal().put("mongo_id", mongoId);
                }
                SyncDataContextEvent erpObjDataResult = FormatConvertUtil.stdErp2Crm(erpIdArg.getTenantId(),erpIdArg.getObjAPIName(), standardData);
                if(erpObjDataResult!=null){
                    erpObjDataResult.setDataVersion(System.currentTimeMillis());//取当前时间为版本
                }
                erpObjDataResult.setDataReceiveType(dataType);
                allData.add(erpObjDataResult);
            }
            return Result.newSuccess(allData);
        }
    }

    private void convertSelectOneFieldValue2String(StandardData standardData, List<ErpObjectFieldEntity> selectOneFieldList) {
        if (standardData == null) {
            return;
        }
        updateSelectOneValue2String(standardData.getMasterFieldVal(), selectOneFieldList);
        if (standardData.getDetailFieldVals() != null && !standardData.getDetailFieldVals().isEmpty()) {
            for (String key : standardData.getDetailFieldVals().keySet()) {
                List<ObjectData> childDataList = standardData.getDetailFieldVals().get(key);

                if (childDataList == null) {
                    log.debug("key:{} is null,error data is {},selectOneFieldList is {}", key, JacksonUtil.toJson(standardData), JacksonUtil.toJson(selectOneFieldList));
                    continue;
                }

                for (ObjectData childData : childDataList) {
                    updateSelectOneValue2String(childData, selectOneFieldList);
                }
            }
        }
    }

    private List<ErpObjectFieldEntity> getErpObjectFieldEntities(String tenantId,
                                                                 String objectApiName,
                                                                 ErpChannelEnum channel,
                                                                 String dataCenterId) {
        ErpObjectRelationshipEntity relationshipEntity = new ErpObjectRelationshipEntity();
        relationshipEntity.setTenantId(tenantId);
        relationshipEntity.setChannel(channel);
        relationshipEntity.setDataCenterId(dataCenterId);
        relationshipEntity.setErpRealObjectApiname(objectApiName);
        List<ErpObjectRelationshipEntity> list = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(relationshipEntity);
        List<String> objectApiNameList = list.stream().map(ErpObjectRelationshipEntity::getErpSplitObjectApiname)
                .collect(Collectors.toList());
        List<ErpObjectFieldEntity> selectOneFieldList = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findSelectOneFieldList(tenantId,
                dataCenterId, objectApiNameList);
        return selectOneFieldList;
    }

    private void updateSelectOneValue2String(ObjectData objectData,
                                             List<ErpObjectFieldEntity> selectOneFieldList) {

        if (objectData == null || objectData.isEmpty()) return;
        if (CollectionUtils.isEmpty(selectOneFieldList)) return;

        for (String fieldApiName : objectData.keySet()) {
            Optional<ErpObjectFieldEntity> fieldEntity = selectOneFieldList.stream()
                    .filter((entity) -> StringUtils.equalsIgnoreCase(entity.getFieldApiName(), fieldApiName))
                    .findFirst();
            if (fieldEntity.isPresent()) {
                //把单选字段的值改成字符串类型
                Object value = objectData.get(fieldApiName);
                if (value == null) continue;
                if (value instanceof Number) {
                    log.debug("ErpDataServiceImpl.updateSelectOneValue2String,fieldApiName={},value={}", fieldApiName, value);
                    String fieldValue = objectData.getString(fieldApiName);
                    objectData.put(fieldApiName, fieldValue);
                    log.debug("ErpDataServiceImpl.updateSelectOneValue2String,fieldApiName={},new value={}", fieldApiName,
                            objectData.get(fieldApiName));
                }
            }
        }
    }


    @Override
    public Result<String> invalidErpObjData(StandardInvalidData standardInvalidData, String tenantId, String dataCenterId, final String syncPloyDetailSnapshotId) {
        ErpObjInterfaceUrlEnum interfaceUrlEnum = ErpObjInterfaceUrlEnum.invalid;

        ErpObjGroovyEntity erpObjGroovyEntity = erpObjGroovyDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .getByTenantIdAndApiNameAndUrl(tenantId, dataCenterId, standardInvalidData.getObjAPIName(), interfaceUrlEnum.name());
        if (erpObjGroovyEntity != null) {
            return specialWayDataService.invalidErpObjDataWithGroovy(standardInvalidData, erpObjGroovyEntity, syncPloyDetailSnapshotId);
        }
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dataCenterId);
        ErpChannelEnum channel = connectInfo.getChannel();
        boolean supportInvalidErpData = channel.equals(ErpChannelEnum.ERP_K3CLOUD)
                || channel.equals(ErpChannelEnum.ERP_SAP)
                || channel.equals(ErpChannelEnum.STANDARD_CHANNEL)
                || channel.equals(ErpChannelEnum.ERP_DB_PROXY);
        if (!supportInvalidErpData) {
            throw new ErpSyncDataException(I18NStringEnum.s133,tenantId);
        }

        ErpObjInterfaceCheckedEntity checkedEntity = erpObjInterfaceCheckedManager.findOne(tenantId,
                dataCenterId,
                standardInvalidData.getObjAPIName(),
                interfaceUrlEnum);

        Result<String> result = null;
        if(checkedEntity!=null) {
            ErpCustomInterfaceEntity customInterfaceEntity = erpCustomInterfaceDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .findData(tenantId, dataCenterId, standardInvalidData.getObjAPIName(), interfaceUrlEnum);

            if(checkedEntity.getCheckedInterfaceType() == ErpObjInterfaceTypeEnum.CUSTOM_API && customInterfaceEntity!=null) {
                result = erpCustomInterfaceService.invalidErpObjData(standardInvalidData,customInterfaceEntity);
            } else {
                ConnectorDataHandler erpDataManager = ConnectorHandlerFactory.getDataHandler(channel,connectInfo.getConnectParams());
                result = erpDataManager.invalidErpObjData(standardInvalidData, connectInfo);
            }
        } else {
            ConnectorDataHandler erpDataManager = ConnectorHandlerFactory.getDataHandler(channel,connectInfo.getConnectParams());
            result = erpDataManager.invalidErpObjData(standardInvalidData, connectInfo);
        }

        //解析结果后判断是否保存结果
        BaseErpDataManager.saveWaitingInterfaceMonitor(!result.isSuccess());
        return result;
    }

    @Override
    public Result<String> invalidErpObjDetailData(StandardInvalidData standardInvalidData, String tenantId, String dataCenterId, final String syncPloyDetailSnapshotId) {
        ErpObjInterfaceUrlEnum interfaceUrlEnum = ErpObjInterfaceUrlEnum.invalidDetail;

        ErpObjGroovyEntity erpObjGroovyEntity = erpObjGroovyDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .getByTenantIdAndApiNameAndUrl(tenantId, dataCenterId, standardInvalidData.getObjAPIName(), interfaceUrlEnum.name());
        if (erpObjGroovyEntity != null) {
            return specialWayDataService.invalidErpObjDetailDataWithGroovy(standardInvalidData, erpObjGroovyEntity, syncPloyDetailSnapshotId);
        }
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dataCenterId);
        ErpChannelEnum channel = connectInfo.getChannel();
        boolean supportInvalidErpData = channel.equals(ErpChannelEnum.ERP_K3CLOUD)
                || channel.equals(ErpChannelEnum.ERP_SAP)
                || channel.equals(ErpChannelEnum.STANDARD_CHANNEL)
                || channel.equals(ErpChannelEnum.ERP_DB_PROXY);
        if (!supportInvalidErpData) {
            throw new ErpSyncDataException(I18NStringEnum.s134,tenantId);
        }

        ErpObjInterfaceCheckedEntity checkedEntity = erpObjInterfaceCheckedManager.findOne(tenantId,
                dataCenterId,
                standardInvalidData.getObjAPIName(),
                interfaceUrlEnum);

        Result<String> result = null;
        if(checkedEntity!=null) {
            ErpCustomInterfaceEntity customInterfaceEntity = erpCustomInterfaceDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .findData(tenantId, dataCenterId, standardInvalidData.getObjAPIName(), interfaceUrlEnum);

            if(checkedEntity.getCheckedInterfaceType() == ErpObjInterfaceTypeEnum.CUSTOM_API && customInterfaceEntity!=null) {
                result = erpCustomInterfaceService.invalidErpObjData(standardInvalidData,customInterfaceEntity);
            } else {
                ConnectorDataHandler erpDataManager = ConnectorHandlerFactory.getDataHandler(channel,connectInfo.getConnectParams());
                result = erpDataManager.invalidErpObjDetailData(standardInvalidData, connectInfo);
            }
        } else {
            ConnectorDataHandler erpDataManager = ConnectorHandlerFactory.getDataHandler(channel,connectInfo.getConnectParams());
            result = erpDataManager.invalidErpObjDetailData(standardInvalidData, connectInfo);
        }


        //解析结果后判断是否保存结果
        BaseErpDataManager.saveWaitingInterfaceMonitor(!result.isSuccess());
        return result;
    }
    @Override
    public Result<String> deleteErpObjData(StandardInvalidData standardDeleteData, String tenantId, String dataCenterId, final String syncPloyDetailSnapshotId) {
        ErpObjInterfaceUrlEnum interfaceUrlEnum = ErpObjInterfaceUrlEnum.delete;
        ErpObjGroovyEntity erpObjGroovyEntity = erpObjGroovyDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .getByTenantIdAndApiNameAndUrl(tenantId, dataCenterId, standardDeleteData.getObjAPIName(), interfaceUrlEnum.name());
        if (erpObjGroovyEntity != null) {
            return specialWayDataService.deleteErpObjDataWithGroovy(standardDeleteData, erpObjGroovyEntity,syncPloyDetailSnapshotId);
        }
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dataCenterId);
        ErpChannelEnum channel = connectInfo.getChannel();

        ErpObjInterfaceCheckedEntity checkedEntity = erpObjInterfaceCheckedManager.findOne(tenantId,
                dataCenterId,
                standardDeleteData.getObjAPIName(),
                interfaceUrlEnum);

        Result<String> result = null;
        if(checkedEntity!=null) {
            ErpCustomInterfaceEntity customInterfaceEntity = erpCustomInterfaceDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .findData(tenantId, dataCenterId, standardDeleteData.getObjAPIName(), interfaceUrlEnum);

            if(checkedEntity.getCheckedInterfaceType() == ErpObjInterfaceTypeEnum.CUSTOM_API && customInterfaceEntity!=null) {
                result = erpCustomInterfaceService.deleteErpObjData(standardDeleteData,customInterfaceEntity);
            } else {
                ConnectorDataHandler erpDataManager = ConnectorHandlerFactory.getDataHandler(channel,connectInfo.getConnectParams());
                result = erpDataManager.deleteErpObjData(standardDeleteData, connectInfo);
            }
        } else {
            ConnectorDataHandler erpDataManager = ConnectorHandlerFactory.getDataHandler(channel,connectInfo.getConnectParams());
            result = erpDataManager.deleteErpObjData(standardDeleteData, connectInfo);
        }

        //解析结果后判断是否保存结果
        BaseErpDataManager.saveWaitingInterfaceMonitor(!result.isSuccess());
        return result;
    }

    @Override
    public Result<String> recoverErpObjData(StandardRecoverData standardRecoverData, String tenantId, String dataCenterId, final String syncPloyDetailSnapshotId) {
        ErpObjGroovyEntity erpObjGroovyEntity = erpObjGroovyDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .getByTenantIdAndApiNameAndUrl(tenantId, dataCenterId, standardRecoverData.getObjAPIName(), ErpObjInterfaceUrlEnum.recover.name());
        if (erpObjGroovyEntity != null) {
            return specialWayDataService.recoverErpObjDataWithGroovy(standardRecoverData, erpObjGroovyEntity, syncPloyDetailSnapshotId);
        }
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dataCenterId);
        ErpChannelEnum channel = connectInfo.getChannel();
        boolean supportRecoverErpData = channel.equals(ErpChannelEnum.ERP_K3CLOUD);//目前只支持k3
        if (!supportRecoverErpData) {
            throw new ErpSyncDataException(I18NStringEnum.s135,tenantId);
        }
        ConnectorDataHandler erpDataManager = ConnectorHandlerFactory.getDataHandler(channel,connectInfo.getConnectParams());
        Result<String> result = erpDataManager.recoverErpObjData(standardRecoverData, connectInfo);
        //解析结果后判断是否保存结果
        BaseErpDataManager.saveWaitingInterfaceMonitor(!result.isSuccess());
        return result;
    }
}
