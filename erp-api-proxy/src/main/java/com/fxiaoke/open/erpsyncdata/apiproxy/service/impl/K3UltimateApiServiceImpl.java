package com.fxiaoke.open.erpsyncdata.apiproxy.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ProxyHttpClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.BaseErpDataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.K3UltimateApiTemplateManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.template.K3UltimateBaseApiTemplate;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.ProxyRequest;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate.*;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate.arg.CommonIdQueryArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate.arg.GetAccessTokenRequestArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate.arg.GetAppTokenRequestArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate.data.GetAccessTokenData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate.data.GetAppTokenData;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.K3UltimateApiService;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.ConnectParamUtil;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.HttpRspLimitLenUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.K3UltimateConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.SocketTimeoutException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 云星空旗舰版open api接口服务
 * <AUTHOR>
 * @date 2023-09-06
 */

@Service
@Slf4j
public class K3UltimateApiServiceImpl implements K3UltimateApiService {
    @Autowired
    private ProxyHttpClient proxyHttpClient;
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private K3UltimateApiTemplateManager k3UltimateApiTemplateManager;
    @Autowired
    private I18NStringManager i18NStringManager;


    private Result<GetAppTokenData> getAppToken(K3UltimateConnectParam k3UltimateConnectParam) {
        String url = k3UltimateConnectParam.getBaseUrl() + "/api/getAppToken.do";

        GetAppTokenRequestArg arg = new GetAppTokenRequestArg();
        arg.setTenantId(k3UltimateConnectParam.getTenantId());
        arg.setAccountId(k3UltimateConnectParam.getDbId());
        arg.setAppId(k3UltimateConnectParam.getAppId());
        arg.setAppSecret(k3UltimateConnectParam.getAppSecret());
        arg.setLanguage(k3UltimateConnectParam.getLanguage());

        ProxyHttpClient.SimpleHttpResult result = proxyHttpClient.postUrlResult(url, arg, new HashMap<>());
        log.info("K3UltimateApiServiceImpl.getAppToken,result={}",result);

        K3UltimateResponse<GetAppTokenData> response = JSONObject.parseObject(result.getData(),
                new TypeReference<K3UltimateResponse<GetAppTokenData>>(){});
        if(response.isStatus() && response.getData().isSuccess()) {
            return Result.newSuccess(response.getData());
        }
        return Result.newError(response.getData().getErrorCode(),response.getData().getErrorDesc());
    }

    private Result<GetAccessTokenData> getAccessToken(String appToken,K3UltimateConnectParam k3UltimateConnectParam) {
        String url = k3UltimateConnectParam.getBaseUrl() + "/api/login.do";

        GetAccessTokenRequestArg arg = new GetAccessTokenRequestArg();
        arg.setApptoken(appToken);
        arg.setTenantId(k3UltimateConnectParam.getTenantId());
        arg.setAccountId(k3UltimateConnectParam.getDbId());
        arg.setUser(k3UltimateConnectParam.getUser());
        arg.setUsertype(k3UltimateConnectParam.getUserType());
        arg.setLanguage(k3UltimateConnectParam.getLanguage());

        ProxyHttpClient.SimpleHttpResult result = proxyHttpClient.postUrlResult(url, arg, new HashMap<>());
        log.info("K3UltimateApiServiceImpl.getAccessToken,result={}",result);

        K3UltimateResponse<GetAccessTokenData> response = JSONObject.parseObject(result.getData(),
                new TypeReference<K3UltimateResponse<GetAccessTokenData>>(){});
        if(response.isStatus() && response.getData().isSuccess()) {
            return Result.newSuccess(response.getData());
        }
        return Result.newError(response.getData().getErrorCode(),response.getData().getErrorDesc());
    }

    @Override
    public Result<GetAccessTokenData> getAccessTokenEx(K3UltimateConnectParam k3UltimateConnectParam) {
        String key = "k3_ultimate_access_token_" + k3UltimateConnectParam.getTenantId() + "_" + k3UltimateConnectParam.getDbId() + "_" + k3UltimateConnectParam.getUser();
        String jsonValue = redisDataSource.get(K3UltimateApiService.class.getSimpleName()).get(key);
        if (StringUtils.isNotEmpty(jsonValue)) {
            GetAccessTokenData data = JSONObject.parseObject(jsonValue, GetAccessTokenData.class);
            if (data != null && StringUtils.isNotEmpty(data.getAccessToken())) {
                return Result.newSuccess(data);
            }
        }

        Result<GetAppTokenData> appTokenResult = getAppToken(k3UltimateConnectParam);
        if (appTokenResult.isSuccess()) {
            Result<GetAccessTokenData> accessTokenResult = getAccessToken(appTokenResult.getData().getAppToken(),k3UltimateConnectParam);
            if (accessTokenResult.isSuccess()
                    && accessTokenResult.getData() != null
                    && StringUtils.isNotEmpty(accessTokenResult.getData().getAccessToken())) {

                long expireTimeInMs = accessTokenResult.getData().getExpireTime() - System.currentTimeMillis();
                String set = redisDataSource.get(K3UltimateApiService.class.getSimpleName()).psetex(key,
                        expireTimeInMs,
                        JSONObject.toJSONString(accessTokenResult.getData()));
                log.info("K3UltimateApiServiceImpl.getAccessTokenEx,set={}", set);
                return Result.newSuccess(accessTokenResult.getData());
            }
        }
        return Result.newError(appTokenResult.getErrCode(), appTokenResult.getErrMsg());
    }

    @Override
    public Result<K3UltimateResponseByQuery> batchQuery(String tenantId,
                                                        String dataCenterId,
                                                        String objApiName,
                                                        K3UltimateRequestByQuery arg,
                                                        boolean queryInvalid,
                                                        K3UltimateConnectParam k3UltimateConnectParam) {
        String batchQueryApi=k3UltimateApiTemplateManager.getApiTemplate(tenantId,dataCenterId,objApiName).getBatchQueryApi();
        if(StringUtils.isBlank(batchQueryApi)||"/".equals(batchQueryApi)){
            return Result.newError(I18NStringEnum.s5109);
        }
        String url = k3UltimateConnectParam.getBaseUrl() + batchQueryApi;
        if(queryInvalid) {
            arg.getData().setEnable("0");//查询禁用状态的数据
        }
        return postUrl(url, arg,k3UltimateConnectParam);
    }

    @Override
    public Result<K3UltimateResponseBySave2> batchAdd(String tenantId,
                                                      String dataCenterId,
                                                      String objApiName,
                                                      List<Object> dataList,
                                                      K3UltimateConnectParam k3UltimateConnectParam) {
        K3UltimateRequestBySave requestBySave = K3UltimateRequestBySave.buildSaveRequest(tenantId,
                dataCenterId,
                objApiName,
                ErpObjInterfaceUrlEnum.create,
                dataList);
        String batchAddApi = k3UltimateApiTemplateManager.getApiTemplate(tenantId, dataCenterId, objApiName).getBatchAddApi();
        if (StringUtils.isBlank(batchAddApi) || "/".equals(batchAddApi)) {
            return Result.newError(I18NStringEnum.s5109);
        }
        String url = k3UltimateConnectParam.getBaseUrl() + batchAddApi;
        return postUrl2(url, requestBySave, k3UltimateConnectParam);
    }

    @Override
    public Result<K3UltimateResponseBySave2> batchUpdate(String tenantId,
                                                         String dataCenterId,
                                                         String objApiName,
                                                         List<Object> dataList,
                                                         K3UltimateConnectParam k3UltimateConnectParam) {
        K3UltimateRequestBySave requestBySave = K3UltimateRequestBySave.buildSaveRequest(tenantId,
                dataCenterId,
                objApiName,
                ErpObjInterfaceUrlEnum.update,
                dataList);
        String batchUpdateApi=k3UltimateApiTemplateManager.getApiTemplate(tenantId,dataCenterId,objApiName).getBatchUpdateApi();
        if(StringUtils.isBlank(batchUpdateApi)||"/".equals(batchUpdateApi)){
            return Result.newError(I18NStringEnum.s5109);
        }
        String url = k3UltimateConnectParam.getBaseUrl() + batchUpdateApi;
        return postUrl2(url, requestBySave,k3UltimateConnectParam);
    }

    @Override
    public Result<K3UltimateResponseBySave2> bizChange(String tenantId,
                                                       String dataCenterId,
                                                       String objApiName,
                                                       String id,
                                                       ErpObjInterfaceUrlEnum interfaceUrl,
                                                       K3UltimateConnectParam k3UltimateConnectParam) {
        K3UltimateRequest<CommonIdQueryArg> request = K3UltimateRequest.buildCommonIdRequest(tenantId,
                dataCenterId,
                objApiName,
                interfaceUrl,
                Lists.newArrayList(id),
                1,
                1);
        String bizChangeApi=k3UltimateApiTemplateManager.getApiTemplate(tenantId,dataCenterId,objApiName).getBizChangeApi(interfaceUrl);
        if(StringUtils.isBlank(bizChangeApi)||"/".equals(bizChangeApi)){
            return Result.newError(I18NStringEnum.s5109);
        }
        String url = k3UltimateConnectParam.getBaseUrl() + bizChangeApi;
        return postUrl2(url, request,k3UltimateConnectParam);
    }

    @Override
    public Result<K3UltimateResponseBySave2> batchSubmit(String tenantId,
                                                         String dataCenterId,
                                                         String objApiName,
                                                         List<String> idList,
                                                         K3UltimateConnectParam k3UltimateConnectParam) {
        K3UltimateRequest<CommonIdQueryArg> request = K3UltimateRequest.buildCommonIdRequest(tenantId,
                dataCenterId,
                objApiName,
                ErpObjInterfaceUrlEnum.submit,
                idList,
                1,
                idList.size());
        String batchSubmitApi=k3UltimateApiTemplateManager.getApiTemplate(tenantId,dataCenterId,objApiName).getBatchSubmitApi();
        if(StringUtils.isBlank(batchSubmitApi)||"/".equals(batchSubmitApi)){
            return Result.newError(I18NStringEnum.s5109);
        }
        String url = k3UltimateConnectParam.getBaseUrl() + batchSubmitApi;
        return postUrl2(url, request,k3UltimateConnectParam);
    }

    @Override
    public Result<K3UltimateResponseBySave2> batchUnSubmit(String tenantId,
                                                           String dataCenterId,
                                                           String objApiName,
                                                           List<String> idList,
                                                           K3UltimateConnectParam k3UltimateConnectParam) {
        K3UltimateRequest<CommonIdQueryArg> request = K3UltimateRequest.buildCommonIdRequest(tenantId,
                dataCenterId,
                objApiName,
                ErpObjInterfaceUrlEnum.unSubmit,
                idList,
                1,
                idList.size());
        String batchUnSubmitApi=k3UltimateApiTemplateManager.getApiTemplate(tenantId,dataCenterId,objApiName).getBatchUnSubmitApi();
        if(StringUtils.isBlank(batchUnSubmitApi)||"/".equals(batchUnSubmitApi)){
            return Result.newError(I18NStringEnum.s5109);
        }
        String url = k3UltimateConnectParam.getBaseUrl() + batchUnSubmitApi;
        return postUrl2(url, request,k3UltimateConnectParam);
    }

    @Override
    public Result<K3UltimateResponseBySave2> batchAudit(String tenantId,
                                                        String dataCenterId,
                                                        String objApiName,
                                                        List<String> idList,
                                                        K3UltimateConnectParam k3UltimateConnectParam) {
        K3UltimateRequest<CommonIdQueryArg> request = K3UltimateRequest.buildCommonIdRequest(tenantId,
                dataCenterId,
                objApiName,
                ErpObjInterfaceUrlEnum.audit,
                idList,
                1,
                idList.size());
        String batchAuditApi=k3UltimateApiTemplateManager.getApiTemplate(tenantId,dataCenterId,objApiName).getBatchAuditApi();
        if(StringUtils.isBlank(batchAuditApi)||"/".equals(batchAuditApi)){
            return Result.newError(I18NStringEnum.s5109);
        }
        String url = k3UltimateConnectParam.getBaseUrl() + batchAuditApi;
        return postUrl2(url, request,k3UltimateConnectParam);
    }

    @Override
    public Result<K3UltimateResponseBySave2> batchUnAudit(String tenantId,
                                                          String dataCenterId,
                                                          String objApiName,
                                                          List<String> idList,
                                                          K3UltimateConnectParam k3UltimateConnectParam) {
        K3UltimateRequest<CommonIdQueryArg> request = K3UltimateRequest.buildCommonIdRequest(tenantId,
                dataCenterId,
                objApiName,
                ErpObjInterfaceUrlEnum.unAudit,
                idList,
                1,
                idList.size());
        String batchUnAuditApi=k3UltimateApiTemplateManager.getApiTemplate(tenantId,dataCenterId,objApiName).getBatchUnAuditApi();
        if(StringUtils.isBlank(batchUnAuditApi)||"/".equals(batchUnAuditApi)){
            return Result.newError(I18NStringEnum.s5109);
        }
        String url = k3UltimateConnectParam.getBaseUrl() + batchUnAuditApi;
        return postUrl2(url, request,k3UltimateConnectParam);
    }

    @Override
    public Result<K3UltimateResponseBySave2> batchEnable(String tenantId,
                                                         String dataCenterId,
                                                         String objApiName,
                                                         List<String> idList,
                                                         K3UltimateConnectParam k3UltimateConnectParam) {
        K3UltimateRequest<CommonIdQueryArg> request = K3UltimateRequest.buildCommonIdRequest(tenantId,
                dataCenterId,
                objApiName,
                ErpObjInterfaceUrlEnum.enable,
                idList,
                1,
                idList.size());
        String batchEnableApi=k3UltimateApiTemplateManager.getApiTemplate(tenantId,dataCenterId,objApiName).getBatchEnableApi();
        if(StringUtils.isBlank(batchEnableApi)||"/".equals(batchEnableApi)){
            return Result.newError(I18NStringEnum.s5109);
        }
        String url = k3UltimateConnectParam.getBaseUrl() + batchEnableApi;
        return postUrl2(url, request,k3UltimateConnectParam);
    }

    @Override
    public Result<K3UltimateResponseBySave2> batchDisable(String tenantId,
                                                          String dataCenterId,
                                                          String objApiName,
                                                          List<String> idList,
                                                          K3UltimateConnectParam k3UltimateConnectParam) {
        K3UltimateRequest<CommonIdQueryArg> request = K3UltimateRequest.buildCommonIdRequest(tenantId,
                dataCenterId,
                objApiName,
                ErpObjInterfaceUrlEnum.disable,
                idList,
                1,
                idList.size());
        String batchDisableApi=k3UltimateApiTemplateManager.getApiTemplate(tenantId,dataCenterId,objApiName).getBatchDisableApi();
        if(StringUtils.isBlank(batchDisableApi)||"/".equals(batchDisableApi)){
            return Result.newError(I18NStringEnum.s5109);
        }
        String url = k3UltimateConnectParam.getBaseUrl() + batchDisableApi;
        return postUrl2(url, request,k3UltimateConnectParam);
    }

    @Override
    public Result<K3UltimateResponseBySave2> batchDelete(String tenantId,
                                                         String dataCenterId,
                                                         String objApiName,
                                                         List<String> idList,
                                                         K3UltimateConnectParam k3UltimateConnectParam) {
        K3UltimateRequest<CommonIdQueryArg> request = K3UltimateRequest.buildCommonIdRequest(tenantId,
                dataCenterId,
                objApiName,
                ErpObjInterfaceUrlEnum.delete,
                idList,
                1,
                idList.size());
        String batchDeleteApi=k3UltimateApiTemplateManager.getApiTemplate(tenantId,dataCenterId,objApiName).getBatchDeleteApi();
        if(StringUtils.isBlank(batchDeleteApi)||"/".equals(batchDeleteApi)){
            return Result.newError(I18NStringEnum.s5109);
        }
        String url = k3UltimateConnectParam.getBaseUrl() + batchDeleteApi;
        return postUrl2(url, request,k3UltimateConnectParam);
    }

    @Override
    public Result<K3UltimateResponseByT<String>> getFormIdMetaData(String tenantId, String dataCenterId, String formId, K3UltimateConnectParam k3UltimateConnectParam) {
        StringBuilder requestBuilder = new StringBuilder();
        String mateDataApi=k3UltimateApiTemplateManager.getApiTemplate(tenantId,dataCenterId,formId).getMateDataApi();
        requestBuilder.append(k3UltimateConnectParam.getBaseUrl());
        if(StringUtils.isNotBlank(mateDataApi)){
            requestBuilder.append(mateDataApi).append("?formId=").append(formId);
        }else{
            requestBuilder.append("/kapi/v2/xkframe/metadata/getEntityMetadata?formId=").append(formId);
        }
        String url = requestBuilder.toString();
        return getUrl(url,k3UltimateConnectParam);
    }
    private Result<K3UltimateResponseByT<String>> getUrl(String url,K3UltimateConnectParam k3UltimateConnectParam) {
        Result<GetAccessTokenData> accessTokenEx = getAccessTokenEx(k3UltimateConnectParam);
        if(!accessTokenEx.isSuccess()){
            return Result.newError(accessTokenEx.getErrCode(),accessTokenEx.getErrMsg());
        }
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("accesstoken", accessTokenEx.getData().getAccessToken());
        String identity = k3UltimateConnectParam.getXAcgwIdentity();
        if (identity != null){
            headerMap.put("x-acgw-identity", identity);
        }
        HttpRspLimitLenUtil.ResponseBodyModel result = null;
        try {
            result = proxyHttpClient.getUrl(url, headerMap, ConfigCenter.CONTENT_LENGTH_LIMIT);
            log.info("K3UltimateApiServiceImpl.getUrl,url={},result={}", url, result);
            K3UltimateResponseByT<String> response = JSONObject.parseObject(result.getBody(), K3UltimateResponseByT.class);
            if (response.isStatus() && StringUtils.equalsIgnoreCase(response.getErrorCode(), "0") && response.getData() != null) {
                return Result.newSuccess(response);
            }
            return Result.newError(response.getErrorCode(), response.getMessage());
        } catch (Exception e) {
            if(e.getCause() instanceof SocketTimeoutException){//超时
                return new Result<>(ResultCodeEnum.SOCKETTIMEOUT, e.getMessage());
            }
            if(result!=null&&StringUtils.isNotBlank(result.getBody())){
                return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, result);
            }
            return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, e.getMessage());
        }
    }

    private Result<K3UltimateResponseByQuery> postUrl(String url,K3UltimateRequest arg,K3UltimateConnectParam k3UltimateConnectParam) {
        Result<GetAccessTokenData> accessTokenEx = getAccessTokenEx(k3UltimateConnectParam);
        if(!accessTokenEx.isSuccess()) return Result.newError(accessTokenEx.getErrCode(),accessTokenEx.getErrMsg());

        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("accesstoken", accessTokenEx.getData().getAccessToken());
        String identity = k3UltimateConnectParam.getXAcgwIdentity();
        if (identity != null)   headerMap.put("x-acgw-identity", identity);

        final String tenantId = Optional.ofNullable(arg.getInterfaceMonitorData())
                .map(InterfaceMonitorData::getTenantId)
                .orElse(null);

        HttpRspLimitLenUtil.ResponseBodyModel result = null;
        Long callTime=System.currentTimeMillis();
        Long costTime = null;
        int status = 1;
        String remark = null;
        try {
            if(arg.isSerializeNull()) {
                result = proxyHttpClient.postUrlSerialNull(url, arg, headerMap, ConfigCenter.CONTENT_LENGTH_LIMIT);
            } else {
                result = proxyHttpClient.postUrl(url, arg, headerMap, ConfigCenter.CONTENT_LENGTH_LIMIT);
            }
            remark = i18NStringManager.getByEi(I18NStringEnum.s3774, tenantId);
            log.info("K3UltimateApiServiceImpl.postUrl,arg={},result={}", arg, result);

            K3UltimateResponseByQuery response = JSONObject.parseObject(result.getBody(), K3UltimateResponseByQuery.class);
            if (response.isStatus() && StringUtils.equalsIgnoreCase(response.getErrorCode(), "0") && response.getData() != null) {
                return Result.newSuccess(response);
            }
            status = 2;
            remark = i18NStringManager.getByEi2(I18NStringEnum.s3775, tenantId, response.getMessage());
            return Result.newError(response.getErrorCode(), response.getMessage());
        } catch (Exception e) {
            status = 2;
            remark = i18NStringManager.getByEi2(I18NStringEnum.s3776, tenantId, e.getMessage());

            if(e.getCause() instanceof SocketTimeoutException){//超时
                return new Result<>(ResultCodeEnum.SOCKETTIMEOUT, e.getMessage());
            }
            return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, e.getMessage());

        } finally {
            costTime = System.currentTimeMillis() - callTime;

            if(arg.getInterfaceMonitorData()!=null) {
                ProxyRequest proxyRequest = new ProxyRequest(url, headerMap, arg);
                String jsonArg = JSONObject.toJSONString(proxyRequest);
                arg.getInterfaceMonitorData().setArg(jsonArg);
                arg.getInterfaceMonitorData().setResult(result.getBody());
                arg.getInterfaceMonitorData().setCallTime(callTime);
                arg.getInterfaceMonitorData().setReturnTime(System.currentTimeMillis());
                arg.getInterfaceMonitorData().setCostTime(costTime);
                arg.getInterfaceMonitorData().setStatus(status);
                arg.getInterfaceMonitorData().setRemark(remark);
                arg.getInterfaceMonitorData().setTraceId(TraceUtil.get());
                BaseErpDataManager.saveErpInterfaceMonitor(arg.getInterfaceMonitorData());
            }
        }
    }

    private Result<K3UltimateResponseBySave2> postUrl2(String url,
                                                       K3UltimateBaseRequest arg,
                                                       K3UltimateConnectParam k3UltimateConnectParam) {
        Result<GetAccessTokenData> accessTokenEx = getAccessTokenEx(k3UltimateConnectParam);
        if(!accessTokenEx.isSuccess()) return Result.newError(accessTokenEx.getErrCode(),accessTokenEx.getErrMsg());

        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("accesstoken", accessTokenEx.getData().getAccessToken());
        String identity = k3UltimateConnectParam.getXAcgwIdentity();
        if (identity != null)   headerMap.put("x-acgw-identity", identity);
        final String tenantId = Optional.ofNullable(arg.getInterfaceMonitorData())
                .map(InterfaceMonitorData::getTenantId)
                .orElse(null);

        HttpRspLimitLenUtil.ResponseBodyModel result = null;
        Long callTime=System.currentTimeMillis();
        Long costTime = null;
        int status = 1;
        String remark = null;
        try {
            result = proxyHttpClient.postUrl(url, arg, headerMap, ConfigCenter.CONTENT_LENGTH_LIMIT);
            log.info("K3UltimateApiServiceImpl.postUrl2,arg={},result={}",arg,result);

            K3UltimateResponseBySave2 response = JSONObject.parseObject(result.getBody(),K3UltimateResponseBySave2.class);
            if(response.isStatus() && StringUtils.equalsIgnoreCase(response.getErrorCode(),"0") && response.getData()!=null) {
                return Result.newSuccess(response);
            }
            status = 2;
            remark = i18NStringManager.getByEi2(I18NStringEnum.s3775, tenantId, response.getMessage());
            return Result.newError(response.getErrorCode(),response.getMessage());
        } catch (Exception e) {
            status = 2;
            remark = i18NStringManager.getByEi2(I18NStringEnum.s3776, tenantId, e.getMessage());

            if(e.getCause() instanceof SocketTimeoutException){//超时
                return new Result<>(ResultCodeEnum.SOCKETTIMEOUT, e.getMessage());
            }
            return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, e.getMessage());
        } finally {
            costTime = System.currentTimeMillis() - callTime;

            if(arg.getInterfaceMonitorData()!=null) {
                ProxyRequest proxyRequest = new ProxyRequest(url, headerMap, arg);
                String jsonArg = JSONObject.toJSONString(proxyRequest);
                arg.getInterfaceMonitorData().setArg(jsonArg);
                arg.getInterfaceMonitorData().setResult(result.getBody());
                arg.getInterfaceMonitorData().setCallTime(callTime);
                arg.getInterfaceMonitorData().setReturnTime(System.currentTimeMillis());
                arg.getInterfaceMonitorData().setCostTime(costTime);
                arg.getInterfaceMonitorData().setStatus(status);
                arg.getInterfaceMonitorData().setRemark(remark);
                arg.getInterfaceMonitorData().setTraceId(TraceUtil.get());
                BaseErpDataManager.saveErpInterfaceMonitor(arg.getInterfaceMonitorData());
            }
        }
    }
}
