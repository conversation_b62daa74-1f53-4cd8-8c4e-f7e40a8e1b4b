package com.fxiaoke.open.erpsyncdata.apiproxy.utils;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3Model;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * K3C客户分配或取消分配工具
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
public class DistributeCustomerUtils {
    /**
     * 根据组织编码获取K3C组织ID
     *
     * @param apiClient
     * @param orgNumber
     * @return
     */
    public static String getOrgId(K3CloudApiClient apiClient, String orgNumber) {
        QueryArg queryArg = new QueryArg();
        queryArg.setFormId("ORG_Organizations");
        queryArg.setFieldKeys("FOrgId");
        queryArg.setFilterString(String.format("FNumber='%s'", orgNumber));
        Result<List<K3Model>> orgResult = apiClient.queryReturnMap(queryArg);
        List<K3Model> orgData = orgResult.getData();
        if (CollectionUtils.isEmpty(orgData)) {
            return null;
        }
        String orgId = orgData.get(0).getString("FOrgId");
        return orgId;
    }

    /**
     * 根据客户编码，获取客户ID
     *
     * @param apiClient
     * @param customerNumber
     * @return
     */
    public static String getCustomerId(K3CloudApiClient apiClient, String customerNumber) {
        QueryArg queryArg = new QueryArg();
        queryArg.setFormId("BD_Customer");
        queryArg.setFieldKeys("FCUSTID");
        queryArg.setFilterString(String.format("FUseOrgId.FNumber = FCreateOrgId.FNumber and FNumber='%s'", customerNumber));
        Result<List<K3Model>> cusResult = apiClient.queryReturnMap(queryArg);
        List<K3Model> cusData = cusResult.getData();
        if (CollectionUtils.isEmpty(cusData)) {
            return null;
        }
        String customerId = cusData.get(0).getString("FCUSTID");
        return customerId;
    }

    /**
     * 获取客户在使用组织下的客户ID
     *
     * @param apiClient
     * @param customerNumber
     * @param useOrgNumber
     * @return
     */
    public static String getCustomerId(K3CloudApiClient apiClient, String customerNumber, String useOrgNumber) {
        QueryArg queryArg = new QueryArg();
        queryArg.setFormId("BD_Customer");
        queryArg.setFieldKeys("FCUSTID");
        queryArg.setFilterString(String.format("FUseOrgId.FNumber = '%s' and FNumber='%s'", useOrgNumber, customerNumber));
        Result<List<K3Model>> cusResult = apiClient.queryReturnMap(queryArg);
        List<K3Model> cusData = cusResult.getData();
        if (CollectionUtils.isEmpty(cusData)) {
            return null;
        }
        String customerId = cusData.get(0).getString("FCUSTID");
        return customerId;
    }
}
