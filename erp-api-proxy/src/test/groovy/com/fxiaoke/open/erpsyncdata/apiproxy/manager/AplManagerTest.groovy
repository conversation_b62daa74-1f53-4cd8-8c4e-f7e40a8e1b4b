package com.fxiaoke.open.erpsyncdata.apiproxy.manager

import com.fxiaoke.open.erpsyncdata.apiproxy.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData
import com.fxiaoke.open.erpsyncdata.apiproxy.model.standard.SyncResult
import spock.lang.Unroll

/**
 *
 * <AUTHOR> (^_−)☆
 */
class AplManagerTest extends BaseSpockTest {

    @Unroll
    def "testSyncResult"(String resStr, boolean success, com.alibaba.fastjson.TypeReference tTypeReference) {
        SyncResult syncResult = SyncResult.parseFromStr(resStr)
        def data = syncResult.parseData(tTypeReference)
        expect:
        syncResult != null && success == syncResult.isSuccess()
        println(data)
        where:
        resStr << [
                '''
                {
                  "code": 0,
                  "data": {
                    "totalNum": 1,
                    "dataList": [
                      {
                        "masterFieldVal": {
                          "birthday": ""
                        },
                        "objAPIName": "VIRContactObj",
                        "detailFieldVals": {}
                      }
                    ]
                  }
                }
                ''',
                '''
                {
                  "code": -1,
                  "message":"test error"
                }
                ''',
                '''
                {
                  "code": 0,
                }
                ''',
                '''
                {
                  "code": 0,
                  "data":"test String"
                }
                ''',
                '''
                {
                  "code": 0,
                  "data":"9999999"
                }
                ''',
                '''
                {
                  "code": 0,
                  "data":"99"
                }
                ''',
                '''
                {
                  "code": 0,
                  "data":"99.99"
                }
                '''
        ]
        success << [
                true,
                false,
                true,
                true,
                true,
                true,
                true,
        ]
        tTypeReference << [
                new com.alibaba.fastjson.TypeReference<StandardListData>() {},
                new com.alibaba.fastjson.TypeReference<StandardListData>() {},
                new com.alibaba.fastjson.TypeReference<Void>() {},
                new com.alibaba.fastjson.TypeReference<String>() {},
                new com.alibaba.fastjson.TypeReference<Long>() {},
                new com.alibaba.fastjson.TypeReference<Integer>() {},
                new com.alibaba.fastjson.TypeReference<Double>() {}
        ]
    }
}
