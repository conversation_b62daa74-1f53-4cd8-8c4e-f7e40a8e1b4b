package com.fxiaoke.open.erpsyncdata.apiproxy.aop;

import com.fxiaoke.open.erpsyncdata.apiproxy.aop.annotation.InvokeMonitor;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.InvokeFeature;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.ActionEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.InvokeTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

/**
 * <AUTHOR>
 * @date 2022/12/7 14:57:43
 */
public interface TestBaseErpDataManager {
    @InvokeMonitor(tenantId = "#connectInfo.tenantId", dcId = "#connectInfo.id", invokeType = InvokeTypeEnum.ERP, count = "1", objAPIName = "#erpIdArg.objAPIName", dataId = "#erpIdArg.dataId", sourceEventType = "#erpIdArg.sourceEventType", snapshotId = "#erpIdArg.snapshotId", action = ActionEnum.GET, invokeFeatures = {InvokeFeature.calculateResultSize, InvokeFeature.sendBizLog})
    Result<StandardData> getErpObjData(final ErpIdArg erpIdArg, final ErpConnectInfoEntity connectInfo);

    default Result<StandardData> getErpObjData2(final ErpIdArg erpIdArg, final ErpConnectInfoEntity connectInfo) {
        return getErpObjData(erpIdArg, connectInfo);
    }
}
