//package com.fxiaoke.open.erpsyncdata.apiproxy.manager
//
//import cn.hutool.core.util.IdUtil
//import cn.hutool.setting.yaml.YamlUtil
//import com.alibaba.fastjson.JSONArray
//import com.alibaba.fastjson.JSONObject
//import com.alibaba.fastjson.TypeReference
//import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ProxyHttpClient
//import com.fxiaoke.open.erpsyncdata.apiproxy.manager.TestOkHttpSupport
//import com.fxiaoke.open.erpsyncdata.apiproxy.manager.dbproxy.DBProxyV2Handler
//import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData
//import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardInvalidData
//import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData
//import com.fxiaoke.open.erpsyncdata.common.data.ObjectData
//import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpDbProxyConfigDao
//import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity
//import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpDBProxyConfigEntity
//import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil
//import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg
//import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg
//import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException
//import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result
//import spock.lang.Ignore
//import spock.lang.Specification
//import spock.lang.Unroll
//
///**
// *
// * <AUTHOR> (^_−)☆
// */
//@Ignore
//class DBProxyV2HandlerTest extends Specification {
//    private def proxyHttpClient
//    private def erpDbProxyConfigDao
//
//    DBProxyV2Handler dataHandler
//    ErpConnectInfoEntity connectInfo = new ErpConnectInfoEntity()
//
//    void setup() {
//        connectInfo.setConnectParams("""
//{
//  "baseUrl": "http://localhost:8080/dev/dbadapt/proxy/api",
//  "version": "2.0",
//  "dbName": "pg15",
//  "dbType": "postgres",
//  "userName": "admin",
//  "password": "1234qwer"
//}
//""")
//        proxyHttpClient = new ProxyHttpClient(
//                okHttpSupport: new TestOkHttpSupport()
//        )
//        erpDbProxyConfigDao = Mock(ErpDbProxyConfigDao)
//        dataHandler = new DBProxyV2Handler(
//                proxyHttpClient: proxyHttpClient,
//                erpDbProxyConfigDao: erpDbProxyConfigDao
//        )
//    }
//
//    @Unroll
//    def "get2-#name"(String name, ErpIdArg erpIdArg, def errCode, def errMsg, ErpDBProxyConfigEntity config, List<ErpDBProxyConfigEntity> detailConfig) {
//        when:
//        erpDbProxyConfigDao.getDBProxyConfigByTenantAndObjApiName(*_) >> {
//            return config
//        }
//        erpDbProxyConfigDao.queryDBProxyConfigByTenantAndParentObjApiName(*_) >> {
//            return detailConfig
//        }
//        dataHandler.getErpObjData(erpIdArg, connectInfo)
//        then:
//        def e = thrown(ErpSyncDataException)
//        //只允许出现业务异常
//        e.getErrCode() == errCode
//        e.getErrMsg() == errMsg
//        where:
//        [name, erpIdArg, errCode, errMsg, config, detailConfig] << dataGet2()
//    }
//
//    private static dataGet2() {
//        def list = YamlUtil.loadByPath("data/dbproxy/getByIdException.yaml", JSONArray)
//        return list.collect {
//            def o = it as JSONObject
//            return [
//                    o.getString("name"),
//                    o.getObject("arg", ErpIdArg),
//                    o.getString("errCode"),
//                    o.getString("errMsg"),
//                    o.getObject("config", ErpDBProxyConfigEntity),
//                    o.getObject("detailConfig", new TypeReference<List<ErpDBProxyConfigEntity>>() {})
//            ]
//        }
//    }
//
//    @Unroll
//    def "get-#name"(String name, ErpIdArg erpIdArg, Result result, ErpDBProxyConfigEntity config, List<ErpDBProxyConfigEntity> detailConfig) {
//        when:
//        erpDbProxyConfigDao.getDBProxyConfigByTenantAndObjApiName(*_) >> {
//            return config
//        }
//        erpDbProxyConfigDao.queryDBProxyConfigByTenantAndParentObjApiName(*_) >> {
//            return detailConfig
//        }
//        def realRes = dataHandler.getErpObjData(erpIdArg, connectInfo)
//        print(JacksonUtil.toJson(realRes))
//        then:
//        realRes.success
//        result.getData() == realRes.getData()
//        where:
//        [name, erpIdArg, result, config, detailConfig] << dataGet()
//    }
//
//    private static dataGet() {
//        def list = YamlUtil.loadByPath("data/dbproxy/getById.yaml", JSONArray)
//        return list.collect {
//            def o = it as JSONObject
//            return [
//                    o.getString("name"),
//                    o.getObject("arg", ErpIdArg),
//                    o.getObject("result", new TypeReference<Result<StandardData>>() {}),
//                    o.getObject("config", ErpDBProxyConfigEntity),
//                    o.getObject("detailConfig", new TypeReference<List<ErpDBProxyConfigEntity>>() {})
//            ]
//        }
//    }
//
//    @Unroll
//    def "list-#name"(String name, TimeFilterArg arg, Result result, ErpDBProxyConfigEntity config, List<ErpDBProxyConfigEntity> detailConfig) {
//        when:
//        erpDbProxyConfigDao.getDBProxyConfigByTenantAndObjApiName(*_) >> {
//            return config
//        }
//        erpDbProxyConfigDao.queryDBProxyConfigByTenantAndParentObjApiName(*_) >> {
//            return detailConfig
//        }
//        def realRes = dataHandler.listErpObjDataByTime(arg, connectInfo)
//        print(JacksonUtil.toJson(realRes))
//        then:
//        realRes.success
//        result.getData() == realRes.getData()
//        where:
//        [name, arg, result, config, detailConfig] << dataList()
//    }
//
//
//    private static dataList() {
//        def list = YamlUtil.loadByPath("data/dbproxy/batchQuery.yaml", JSONArray)
//        return list.collect {
//            def o = it as JSONObject
//            return [
//                    o.getString("name"),
//                    o.getObject("arg", TimeFilterArg),
//                    o.getObject("result", new TypeReference<Result<StandardListData>>() {}),
//                    o.getObject("config", ErpDBProxyConfigEntity),
//                    o.getObject("detailConfig", new TypeReference<List<ErpDBProxyConfigEntity>>() {})
//            ]
//        }
//    }
//
//    @Unroll
//    def "create-#name"(String name, ErpDBProxyConfigEntity config, List<ErpDBProxyConfigEntity> detailConfig) {
//        when:
//        erpDbProxyConfigDao.getDBProxyConfigByTenantAndObjApiName(*_) >> {
//            return config
//        }
//        erpDbProxyConfigDao.queryDBProxyConfigByTenantAndParentObjApiName(*_) >> {
//            return detailConfig
//        }
//        def main = new ObjectData()
//        def code = IdUtil.nanoId()
//        main.put("name", "cus-" + code)
//        main.put("address", "测试地址")
//        main.put("code", code)
//        StandardData standardData = new StandardData()
//        standardData.setMasterFieldVal(main)
//        standardData.setObjAPIName("customers")
//        def realRes = dataHandler.createErpObjData(standardData, connectInfo)
//        print(JacksonUtil.toJson(realRes))
//        then:
//        realRes.success
//        realRes.getData().getMasterDataId() != null
//        where:
//        [name, config, detailConfig] << dataCreate()
//    }
//
//    private static dataCreate() {
//        def list = YamlUtil.loadByPath("data/dbproxy/create.yaml", JSONArray)
//        return list.collect {
//            def o = it as JSONObject
//            return [
//                    o.getString("name"),
//                    o.getObject("config", ErpDBProxyConfigEntity),
//                    o.getObject("detailConfig", new TypeReference<List<ErpDBProxyConfigEntity>>() {})
//            ]
//        }
//    }
//
//
//    @Unroll
//    def "update-#name"(String name, ErpDBProxyConfigEntity config, List<ErpDBProxyConfigEntity> detailConfig,String resultId) {
//        when:
//        erpDbProxyConfigDao.getDBProxyConfigByTenantAndObjApiName(*_) >> {
//            return config
//        }
//        erpDbProxyConfigDao.queryDBProxyConfigByTenantAndParentObjApiName(*_) >> {
//            return detailConfig
//        }
//        def main = new ObjectData()
//        def code = IdUtil.nanoId()
//        main.put("id", "594")
//        main.put("name", "cus-blbLQ7ctOsW4rZVjruVRv")
//        main.put("address", "修改" + IdUtil.nanoId())
//        main.put("code", code)
//        StandardData standardData = new StandardData()
//        standardData.setMasterFieldVal(main)
//        standardData.setObjAPIName("customers")
//        def realRes = dataHandler.updateErpObjData(standardData, connectInfo)
//        print(JacksonUtil.toJson(realRes))
//        then:
//        realRes.success
//        realRes.getData().getMasterDataId() ==resultId
//        where:
//        [name, config, detailConfig,resultId] << dataUpdate()
//    }
//
//    private static dataUpdate() {
//        def list = YamlUtil.loadByPath("data/dbproxy/update.yaml", JSONArray)
//        return list.collect {
//            def o = it as JSONObject
//            return [
//                    o.getString("name"),
//                    o.getObject("config", ErpDBProxyConfigEntity),
//                    o.getObject("detailConfig", new TypeReference<List<ErpDBProxyConfigEntity>>() {}),
//                    o.getString("resultId")
//            ]
//        }
//    }
//
//
//    @Unroll
//    def "invalid-#name"(String name, ErpDBProxyConfigEntity config, List<ErpDBProxyConfigEntity> detailConfig,String resultId) {
//        when:
//        erpDbProxyConfigDao.getDBProxyConfigByTenantAndObjApiName(*_) >> {
//            return config
//        }
//        erpDbProxyConfigDao.queryDBProxyConfigByTenantAndParentObjApiName(*_) >> {
//            return detailConfig
//        }
//        def main = new ObjectData()
//        def code = "IP4RGIXFXfjxI8W1U7d57"
//        main.put("id", "593")
//        main.put("name", "cus-IP4RGIXFXfjxI8W1U7d57")
//        main.put("address", "修改" + IdUtil.nanoId())
//        main.put("code", code)
//        StandardInvalidData standardData = new StandardInvalidData()
//        standardData.setMasterFieldVal(main)
//        standardData.setObjAPIName("customers")
//        def realRes = dataHandler.invalidErpObjData(standardData, connectInfo)
//        print(JacksonUtil.toJson(realRes))
//        then:
//        realRes.success
//        where:
//        [name, config, detailConfig,resultId] << dataInvalid()
//    }
//
//    private static dataInvalid() {
//        def list = YamlUtil.loadByPath("data/dbproxy/invalid.yaml", JSONArray)
//        return list.collect {
//            def o = it as JSONObject
//            return [
//                    o.getString("name"),
//                    o.getObject("config", ErpDBProxyConfigEntity),
//                    o.getObject("detailConfig", new TypeReference<List<ErpDBProxyConfigEntity>>() {}),
//                    o.getString("resultId")
//            ]
//        }
//    }
//}
