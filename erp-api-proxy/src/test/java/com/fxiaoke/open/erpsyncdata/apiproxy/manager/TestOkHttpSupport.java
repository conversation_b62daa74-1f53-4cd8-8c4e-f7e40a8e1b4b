//package com.fxiaoke.open.erpsyncdata.apiproxy.manager;
//
//import com.alibaba.fastjson.TypeReference;
//import com.fxiaoke.common.http.handler.AsyncCallback;
//import com.fxiaoke.common.http.handler.SyncCallback;
//import com.fxiaoke.common.http.spring.OkHttpSupport;
//import org.junit.Ignore;
//
//import java.io.File;
//import java.io.IOException;
//import java.util.Map;
//import okhttp3.Request;
//
///**
// * <AUTHOR> (^_−)☆
// */
//@Ignore
//public class TestOkHttpSupport implements OkHttpSupport {
//    @Override
//    public String getString(String url) {
//        return "";
//    }
//
//    @Override
//    public String getString(String url, long timeoutMillis) {
//        return "";
//    }
//
//    @Override
//    public byte[] getBytes(String url) {
//        return new byte[0];
//    }
//
//    @Override
//    public byte[] getBytes(String url, long timeoutMillis) {
//        return new byte[0];
//    }
//
//    @Override
//    public Object syncExecute(Request request, SyncCallback callback) {
//        Call call = new OkHttpClient().newCall(request);
//        try {
//            Response response = call.execute();
//            return callback.response(response);
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//    }
//
//    @Override
//    public Object syncExecute(Request request, SyncCallback callback, long timeoutMillis) {
//        return null;
//    }
//
//    @Override
//    public void asyncExecute(Request request, AsyncCallback callback) {
//
//    }
//
//    @Override
//    public void execute(Request request) {
//
//    }
//
//    @Override
//    public <T> T parseObject(Request request, TypeReference<T> typeReference) {
//        return null;
//    }
//
//    @Override
//    public Object syncNonblockingExecute(Request request, SyncCallback callback) throws IOException {
//        return null;
//    }
//
//    @Override
//    public Object syncNonblockingExecute(Request request, SyncCallback callback, long timeoutMillis) throws IOException {
//        return null;
//    }
//
//    @Override
//    public void sendNoWait(Request request) {
//
//    }
//
//    @Override
//    public int downloadFile(String url, File localFile, boolean report404) {
//        return 0;
//    }
//
//    @Override
//    public int downloadFileHeader(String url, Map<String, String> headers, File localFile, boolean report404) {
//        return 0;
//    }
//}
