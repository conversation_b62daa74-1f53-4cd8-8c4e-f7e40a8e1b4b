<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.springframework.org/schema/beans"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

    <bean class="com.fxiaoke.open.erpsyncdata.apiproxy.aop.SpelAspect"/>
    <bean class="com.fxiaoke.open.erpsyncdata.apiproxy.aop.TestBaseErpDataManagerFactoryBean"/>

    <aop:aspectj-autoproxy proxy-target-class="true"/>
</beans>