#YAML
# 当时间精度为ms
querySql: select * from test2 where ${__where}
batchWhere:
  - date = ${startTime__d} and id > ${lastMaxId} order by date,id # 当lastMaxId为空时，不会执行
  - date > ${startTime__d} and date <= ${endTime__d} order by date,id
idWhere:
  - id = ${dataId}::INTEGER
insertSql: |
  INSERT INTO test2 ("name", "date") VALUES(${name}, ${date__d});
updateSql: |
  UPDATE test2 SET name=${name}, date=${date__d} WHERE id=${id}::INTEGER;