package com.facishare.open.erp.connertor.sdk.model;

import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/3/20 19:23:20
 */
public interface RefreshToken {

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg extends Base.ConnectorArg {
        private String appId;
        private String appSecret;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    @AllArgsConstructor
    @NoArgsConstructor
    class Result extends Base.Result {
        private String connectParam;
        private Long expireTime;
    }
}
