<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.facishare.open</groupId>
        <artifactId>fs-erp-sync-data</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>erp-connector-proxy</artifactId>
    <packaging>war</packaging>

    <dependencies>

        <!--    facebook api    -->
        <!-- https://mvnrepository.com/artifact/com.restfb/restfb -->
        <dependency>
            <groupId>com.restfb</groupId>
            <artifactId>restfb</artifactId>
            <version>2023.6.0</version>
        </dependency>
        <!--    facebook api    -->

        <!--    linkedin api    -->
        <!-- https://mvnrepository.com/artifact/com.linkedin.pegasus/restli-client -->
        <dependency>
            <groupId>com.linkedin.pegasus</groupId>
            <artifactId>restli-client</artifactId>
            <version>27.7.18</version>
        </dependency>
        <!--    linkedin api    -->


        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>jedis-spring-support</artifactId>
        </dependency>

        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
        </dependency>


        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>

        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>erp-connector-proxy-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>erp-connector-proxy-sdk</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aspects</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>http-spring-support</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-performance-log</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dubbo</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>metrics-oss</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>erp-i18n</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.tomcat.maven</groupId>
                <artifactId>tomcat7-maven-plugin</artifactId>
                <configuration>
                    <server>tomcat-development-server</server>
                    <port>8085</port>
                    <path>/</path>
                    <uriEncoding>UTF-8</uriEncoding>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>