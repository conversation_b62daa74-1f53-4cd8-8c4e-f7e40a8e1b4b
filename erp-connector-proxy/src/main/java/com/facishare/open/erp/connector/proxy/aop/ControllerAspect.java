package com.facishare.open.erp.connector.proxy.aop;

import com.alibaba.fastjson.JSON;
import com.facishare.open.erp.connector.proxy.linkedin.exception.LinkedinException;
import com.facishare.open.erp.connertor.sdk.model.Base;
import com.restfb.exception.FacebookException;
import com.restfb.exception.FacebookGraphException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/23 15:45:06
 */
@Component
@Aspect
@Slf4j
public class ControllerAspect {

    @Around("execution(* com.facishare.open.erp.connector.proxy.*.controller.*.*(..))")
    public Object ignoreException(ProceedingJoinPoint jp) throws Throwable {
        final MethodSignature signature = (MethodSignature) jp.getSignature();
        final Class returnType = signature.getReturnType();
        try {
            return jp.proceed();
        } catch (FacebookGraphException e) {
            return Base.Result.error(e.getErrorCode(), e.getErrorMessage(), returnType);
        } catch (FacebookException | LinkedinException e) {
            return Base.Result.error(Base.Result.ERP_ERROR, e.getMessage(), returnType);
        } catch (Throwable t) {
            final String methodName = signature.getName();
            final Object[] args = jp.getArgs();
            final List<String> argList = Arrays.stream(args).map(JSON::toJSONString).collect(Collectors.toList());
            final String className = jp.getTarget().getClass().getSimpleName();
            log.error("{} {} error, args:{}", className, methodName, argList, t);

            return Base.Result.error(Base.Result.SYSTEM_ERROR, t.getMessage(), returnType);
        }
    }
}
