package com.facishare.open.erp.connector.proxy.constant;

import lombok.Getter;

@Getter
public enum LinkedinAdErpObjectEnum {

    CAMPAIGN(1, "广告计划", "linkedin_campaign"),
    AD(2, "广告", "linkedin_ad"),
    AD_LAUNCH_DETAIL(6, "广告投放明细", "linkedin_ad_launch_detail"),
    ;

    private int code;

    private String name;

    private String apiName;

    LinkedinAdErpObjectEnum(int code, String name, String apiName) {
        this.code = code;
        this.name = name;
        this.apiName = apiName;
    }

    public static LinkedinAdErpObjectEnum findByApiName(String apiName) {
        for (LinkedinAdErpObjectEnum googleErpObjectEnum: LinkedinAdErpObjectEnum.values()) {
            if (googleErpObjectEnum.getApiName().equals(apiName)) {
                return googleErpObjectEnum;
            }
        }
        return null;
    }

}
