// package com.facishare.open.erp.connector.proxy.exceptionhandler;
//
// import com.facishare.open.erp.connector.proxy.linkedin.exception.LinkedinException;
// import com.facishare.open.erp.connertor.sdk.model.Base;
// import com.facishare.open.erp.connertor.util.AesUtil;
// import com.restfb.exception.FacebookException;
// import com.restfb.exception.FacebookGraphException;
// import lombok.extern.slf4j.Slf4j;
// import org.springframework.web.bind.annotation.ControllerAdvice;
// import org.springframework.web.bind.annotation.ExceptionHandler;
// import org.springframework.web.bind.annotation.ResponseBody;
//
// import javax.servlet.http.HttpServletRequest;
// import java.io.BufferedReader;
// import java.util.stream.Collectors;
//
// /**
//  * <AUTHOR>
//  * @date 2023/5/11 17:20:46
//  */
//
// @ControllerAdvice
// @ResponseBody
// @Slf4j
// public class ExceptionHandlerController {
//
//     @ExceptionHandler(FacebookGraphException.class)
//     public Base.Result handleFacebookGraphException(FacebookGraphException e) {
//         return new Base.Result(e.getErrorCode(), e.getErrorMessage());
//     }
//
//     @ExceptionHandler({FacebookException.class, LinkedinException.class})
//     public Base.Result handleBizException(HttpServletRequest request, RuntimeException e) {
//         final String requestURI = request.getRequestURI();
//         final String requestBody = getDecryptedRequestBody(request);
//         log.warn("{} biz error, Body:{}", requestURI, requestBody, e);
//         return new Base.Result(Base.Result.ERP_ERROR, e.getMessage());
//     }
//
//     @ExceptionHandler({Throwable.class})
//     public Base.Result handleBizException(HttpServletRequest request, Throwable t) {
//         final String requestURI = request.getRequestURI();
//         final String requestBody = getDecryptedRequestBody(request);
//
//         log.error("{} error, Body:{}", requestURI, requestBody, t);
//
//         return new Base.Result(Base.Result.SYSTEM_ERROR, t.getMessage());
//     }
//
//     private String getDecryptedRequestBody(HttpServletRequest request) {
//         StringBuilder sb = new StringBuilder();
//         try (BufferedReader reader = request.getReader()) {
//             String encryptedBody = reader.lines().collect(Collectors.joining());
//             String decryptedBody = AesUtil.decrypt(encryptedBody); // decrypt the encrypted request body
//             sb.append(decryptedBody);
//         } catch (Exception e) {
//             log.error("Failed to decrypt the request body", e);
//         }
//         return sb.toString();
//     }
// }
