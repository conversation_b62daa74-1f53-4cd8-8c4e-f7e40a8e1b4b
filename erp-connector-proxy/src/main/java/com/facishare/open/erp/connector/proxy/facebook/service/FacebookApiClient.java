package com.facishare.open.erp.connector.proxy.facebook.service;

import com.alibaba.fastjson.JSON;
import com.facishare.open.erp.connector.proxy.model.Oauth2ConnectParam;
import com.google.common.collect.ImmutableMap;
import com.restfb.Connection;
import com.restfb.FacebookClient;
import com.restfb.Parameter;
import com.restfb.Version;
import com.restfb.types.Page;
import lombok.Data;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023/3/16 10:54:08
 */
@Data
public class FacebookApiClient {

    public static final Version version = Version.VERSION_16_0;

    private String tenantId;
    private String connectParam;

    private Oauth2ConnectParam param;

    private Map<String, String> pageTokenMap = new ConcurrentHashMap<>();

    public FacebookApiClient(final String tenantId, final String connectParam) {
        this.tenantId = tenantId;
        this.connectParam = connectParam;
        param = JSON.parseObject(connectParam, Oauth2ConnectParam.class);
    }

    public <T> T connectionObject(String path, Class<T> connectionType, Map<String, String> arg, Parameter... parameters) {
        FacebookClient client = getFacebookClient(param.getToken());

        return getObject(path, connectionType, arg, client, parameters);
    }

    public <T> T connectionObject(String pageId, String path, Class<T> connectionType, Map<String, String> arg, Parameter... parameters) {
        String token = getToken(pageId);
        FacebookClient client = getFacebookClient(token);

        return getObject(path, connectionType, arg, client, parameters);
    }

    private static FacebookClient getFacebookClient(final String token) {
        return FacebookClientHelp.getFacebookClient(token);
    }

    private String getToken(final String pageId) {
        if (StringUtils.isBlank(pageId)) {
            return param.getToken();
        }

        return pageTokenMap.computeIfAbsent(pageId, this::getPageToken);
    }

    private String getPageToken(final String pageId) {
        FacebookClient client = getFacebookClient(param.getToken());
        Page page = getObject(pageId, Page.class, ImmutableMap.of("fields", "access_token"), client, null);
        return page.getAccessToken();
    }

    private static <T> T getObject(final String path, final Class<T> connectionType, final Map<String, String> arg, final FacebookClient client, Parameter... parameterList) {
        Parameter[] parameters = convert2Parameter(arg, parameterList);

        return client.fetchObject(path, connectionType, parameters);
    }

    private static Parameter[] convert2Parameter(final Map<String, String> arg, Parameter[] parameterList) {
        parameterList = Objects.isNull(parameterList) ? new Parameter[0] : parameterList;
        Parameter[] parameters =
                Stream.concat(
                        Stream.of(parameterList),
                        MapUtils.emptyIfNull(arg).entrySet().stream().map(entry -> Parameter.with(entry.getKey(), entry.getValue()))
                ).toArray(Parameter[]::new);
        return parameters;
    }

    public <T, V> List<V> connectionWithHandler(String path, Class<T> connectionType, Map<String, String> arg, Function<T, V> handler, Parameter... parameterList) {
        FacebookClient client = getFacebookClient(param.getToken());

        return getList(path, connectionType, arg, handler, client, parameterList);
    }

    public <T, V> List<V> connectionWithHandler(String pageId, String path, Class<T> connectionType, Map<String, String> arg, Function<T, V> handler, Parameter... parameterList) {
        FacebookClient client = getFacebookClient(getToken(pageId));

        return getList(path, connectionType, arg, handler, client, parameterList);
    }

    private static <T, V> List<V> getList(final String path, final Class<T> connectionType, final Map<String, String> arg, final Function<T, V> handler, final FacebookClient client, final Parameter[] parameterList) {
        Parameter[] parameters = convert2Parameter(arg, parameterList);

        Connection<T> connection = client.fetchConnection(path, connectionType, parameters);

        List<V> result = new ArrayList<>();
        connection.forEach(list -> list.stream().map(handler).forEach(result::add));
        return result;
    }
}
