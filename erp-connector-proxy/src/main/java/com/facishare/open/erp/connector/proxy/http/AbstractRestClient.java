package com.facishare.open.erp.connector.proxy.http;

import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.http.client.utils.URIBuilder;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractRestClient {

    @Autowired
    private OkHttpSupport httpClient;

    public String post(Object body, Map<String, String> headers, String url) throws IOException {
        final RequestBody requestBody = createRequestBody(body);
        final Request.Builder builder = new Request.Builder()
                .url(url)
                .post(requestBody);
        return rest(body, headers, builder, true);
    }

    private String rest(final Object body, final Map<String, String> headers, final Request.Builder builder, boolean post) throws IOException {
        headers.forEach(builder::addHeader);
        final Request request = builder.build();

        if (log.isInfoEnabled()) {
            log.info(printCurlLog(body, request.headers(), request.url().toString(), post));
        }
        return (String) httpClient.syncExecute(request, new SyncCallback() {
            @Override
            public Object response(final Response response) throws Exception {
                final String result = response.body().string();
                if (!response.isSuccessful()) {
                    throw new RuntimeException("post error status:" + response.code() + " response:" + result);
                }
                return result;
            }
        });
    }

    public String get(Map<String, String> body, Map<String, String> headers, String url) throws Exception {
        URIBuilder uriBuilder = new URIBuilder(url);
        body.forEach(uriBuilder::addParameter);
        final Request.Builder builder = new Request.Builder()
                .url(uriBuilder.toString())
                .get();
        return rest(body, headers, builder, false);
    }

    protected String printCurlLog(Object body, Headers headers, String url, boolean post) {
        StringBuilder sb = new StringBuilder("curl '");
        if (post) {
            sb.append("-X POST ");
        }
        sb.append(url);
        sb.append("' ");
        headers.names().stream().forEach(n -> sb.append(" -H '").append(n).append(": ").append(headers.get(n)).append("' "));
        sb.append(printBodyLog(body));

        return sb.toString();
    }

    protected abstract RequestBody createRequestBody(final Object body);

    protected abstract String printBodyLog(final Object body);

}
