package com.facishare.open.erp.connector.proxy.http;

import com.alibaba.fastjson.JSON;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022/10/7 20:09:29
 */
@Service
public class JsonRestClient extends AbstractRestClient {

    private static final MediaType mediaType = MediaType.get("application/json;charset=utf-8");

    @Override
    protected RequestBody createRequestBody(final Object body) {
        final String restBody = getBody(body);
        return RequestBody.create(mediaType, restBody);
    }

    private static String getBody(final Object body) {
        return body instanceof String ? body.toString() : JSON.toJSONString(body);
    }

    @Override
    protected String printBodyLog(final Object body) {
        return "-H 'Content-Type: application/json;charset=utf-8'\n-d '" + getBody(body) + "' ";
    }
}
