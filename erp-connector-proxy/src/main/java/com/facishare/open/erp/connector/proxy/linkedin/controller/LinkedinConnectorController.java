package com.facishare.open.erp.connector.proxy.linkedin.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.erp.connector.proxy.constant.LinkedinAdErpObjectEnum;
import com.facishare.open.erp.connector.proxy.linkedin.exception.LinkedinException;
import com.facishare.open.erp.connector.proxy.linkedin.model.LinkedinLeadExtendInfo;
import com.facishare.open.erp.connector.proxy.linkedin.service.LinkedinApiClient;
import com.facishare.open.erp.connertor.sdk.controller.IConnectorController;
import com.facishare.open.erp.connertor.sdk.model.GetById;
import com.facishare.open.erp.connertor.sdk.model.ListByTime;
import com.facishare.open.erp.connertor.sdk.model.dto.ErpObjectData;
import com.facishare.open.erp.connertor.sdk.model.dto.FormMetaData;
import com.facishare.open.erp.connertor.sdk.model.linkedin.GetFormMetaData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.http.client.utils.URIBuilder;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/8 16:01:09
 */
@Slf4j
@RestController
@RequestMapping("erp/proxy/linkedin")
public class LinkedinConnectorController implements IConnectorController {
    // 根据id查询数据 不管accountId存不存在或者对不对应的上 只要有dataId且正确 就能返回数据
    // 因此 考虑是否开启根据id查询数据 或者在代码做修改
    @Override
    public GetById.Result getById(@RequestBody final GetById.Arg arg) {
        GetById.Result result = new GetById.Result();
        ErpObjectData erpObjectData;

        LinkedinAdErpObjectEnum linkedinAdErpObjectEnum = LinkedinAdErpObjectEnum.findByApiName(arg.getObjAPIName());
        if (linkedinAdErpObjectEnum != null) {
            erpObjectData = getLinkedinAdObjectDataById(arg);
        } else {
            erpObjectData = getAdFormResponsesById(arg);
        }
        result.setErpObjectData(erpObjectData);
        return result;
    }

    private ErpObjectData getLinkedinAdObjectDataById(GetById.Arg arg) {
        ErpObjectData erpObjectData = null;
        if (LinkedinAdErpObjectEnum.CAMPAIGN.getApiName().equals(arg.getObjAPIName())) {
            erpObjectData = getCampaignById(arg);
        } else if (LinkedinAdErpObjectEnum.AD.getApiName().equals(arg.getObjAPIName())) {
            erpObjectData = getAdvertiseById(arg);
        } else if (LinkedinAdErpObjectEnum.AD_LAUNCH_DETAIL.getApiName().equals(arg.getObjAPIName())) {
            erpObjectData = getAnalysisDataById(arg);
        }
        return erpObjectData;
    }

    private ErpObjectData getAdFormResponsesById(GetById.Arg arg) {
        final String formId = JSON.parseObject(arg.getExtendValue(), LinkedinLeadExtendInfo.class).getFormId();
        final LinkedinApiClient linkedinApiClient = new LinkedinApiClient(arg.getConnectParam());
        GetFormMetaData.Result formMetaData = getFormMetaData(linkedinApiClient, formId);
        List<String> accountIdList = new ArrayList<>();
        if(!Objects.isNull(arg.getAccountIds()) && arg.getAccountIds().length()>0){
            String[] split = arg.getAccountIds().split(",");
            for(String accountId : split){
                accountIdList.add(accountId);
            }
        }else {
            accountIdList = getAllAccountId(linkedinApiClient);
        }
        for (String accountId : accountIdList) {
            try {
                final URIBuilder uriBuilder = new URIBuilder();
                uriBuilder.setScheme("https");
                uriBuilder.setHost("api.linkedin.com");
                uriBuilder.setPath("rest/leadFormResponses");
                String owner = "&owner=(sponsoredAccount:urn%3Ali%3AsponsoredAccount%3A" + accountId + ")";
                final String url = uriBuilder.toString() + "?leadType=(leadType:SPONSORED)" + owner + String.format("&ids=List(\"%s\")", arg.getDataId());
                return linkedinApiClient.get(url, body -> {
                    log.info("getAdFormResponsesById arg: {} body: {}", arg, body);
                    JSONObject jsonObject = JSON.parseObject(body);
                    jsonObject = jsonObject.getJSONObject("results").getJSONObject(arg.getDataId());
                    if (jsonObject == null) {
                        return null;
                    }
                    return parseFormResponseToErpObject(jsonObject, formMetaData);
                });
            } catch (Exception e) {
                log.warn("linkedin adFormResponses error, accountId: {} arg: {}", accountId, arg, e);
            }
        }
        return null;
    }

    private static ErpObjectData parseFormResponseToErpObject(final JSONObject jsonObject, GetFormMetaData.Result formMetaDataResult) {
        Map<String, List<FormMetaData.Options>> questionIdToOptionMap = formMetaDataResult.getMetaDataList().stream().filter(e -> CollectionUtils.isNotEmpty(e.getOptions()))
                .collect(Collectors.toMap(FormMetaData::getKey, FormMetaData::getOptions));
        final String id = jsonObject.getString("id");
        final Map<String, Object> data = jsonObject.getJSONObject("formResponse").getJSONArray("answers").stream()
                .map(o -> (JSONObject) o)
                .collect(Collectors.toMap(
                        o -> o.getString("questionId"),
                        o -> {
                            final JSONObject details = o.getJSONObject("answerDetails").getJSONObject("textQuestionAnswer");
                            if (details != null) {
                                return details.getString("answer");
                            }
                            // linkedin只有单选
                            final Integer optionId = o.getJSONObject("answerDetails").getJSONObject("multipleChoiceAnswer").getJSONArray("options").getInteger(0);
                            List<FormMetaData.Options> optionsList = questionIdToOptionMap.getOrDefault(o.getString("questionId"), Lists.newArrayList());
                            Optional<FormMetaData.Options> optional = optionsList.stream().filter(e -> e.getKey().equals(String.valueOf(optionId))).findFirst();
                            return optional.isPresent() ? optional.get().getValue() : "";
                        }
                ));
        data.put("id", id);
        final ErpObjectData objectData = new ErpObjectData();
        objectData.setMaster(data);
        return objectData;
    }

    @Override
    public ListByTime.Result listByTime(@RequestBody final ListByTime.Arg arg) {
        log.info("api对象名称为=======>"+arg.getObjAPIName());
        ListByTime.Result result = new ListByTime.Result();
        LinkedinAdErpObjectEnum linkedinAdErpObjectEnum = LinkedinAdErpObjectEnum.findByApiName(arg.getObjAPIName());
        if (linkedinAdErpObjectEnum != null) {
            List<ErpObjectData> erpObjectDataList = getLinkedinAdObjectDataByTime(arg);
            result.setErpObjectData(erpObjectDataList);
            result.setTotalNum(erpObjectDataList.size());
            return result;
        }
        return getAdFromResponses(arg);
    }

    private List<ErpObjectData> getLinkedinAdObjectDataByTime(ListByTime.Arg arg) {
        if (arg.getOffset() != null && arg.getOffset() > 0) {
            return Lists.newArrayList();
        }
        List<ErpObjectData> erpObjectDataList = Lists.newArrayList();
        if (LinkedinAdErpObjectEnum.CAMPAIGN.getApiName().equals(arg.getObjAPIName())) {
            erpObjectDataList = getAllCampaign(arg);
        } else if (LinkedinAdErpObjectEnum.AD.getApiName().equals(arg.getObjAPIName())) {
            erpObjectDataList = getAllAdvertise(arg);
        } else if (LinkedinAdErpObjectEnum.AD_LAUNCH_DETAIL.getApiName().equals(arg.getObjAPIName())) {
            erpObjectDataList = getAnalysisData(arg);
        }
        return erpObjectDataList;
    }

    private ListByTime.Result getAdFromResponses(ListByTime.Arg arg) {

        final String formId = JSON.parseObject(arg.getExtendValue(), LinkedinLeadExtendInfo.class).getFormId();
        final LinkedinApiClient linkedinApiClient = new LinkedinApiClient(arg.getConnectParam());

        final ListByTime.Result result = new ListByTime.Result();
        final List<ErpObjectData> elements = new ArrayList<>();
        result.setErpObjectData(elements);
        GetFormMetaData.Result formMetaData = getFormMetaData(linkedinApiClient, formId);
        int formVersion = formMetaData.getVersionId();
        List<String> allAccountIdList = new ArrayList<>();
        if(!Objects.isNull(arg.getAccountIds()) && arg.getAccountIds().length()>0){
            String[] split = arg.getAccountIds().split(",");
            for(String accountId : split){
                allAccountIdList.add(accountId);
            }
        }else {
            allAccountIdList = getAllAccountId(linkedinApiClient);
        }
        for (String accountId : allAccountIdList) {
            final URIBuilder uriBuilder = new URIBuilder();
            // 接口不支持分页，只能一次性获取所有数据
            uriBuilder.setScheme("https");
            uriBuilder.setHost("api.linkedin.com");
            uriBuilder.setPath("/rest/leadFormResponses");
            uriBuilder.setParameter("q", "owner");
            uriBuilder.setParameter("versionedLeadGenFormUrn", String.format("urn:li:versionedLeadGenForm:(urn:li:leadGenForm:%s,%s)", formId, formVersion));
            String owner = "&owner=(sponsoredAccount:urn%3Ali%3AsponsoredAccount%3A" + accountId + ")";
            String timeRange = "&submittedAtTimeRange=" + String.format("(start:%s,end:%s)", arg.getStartTime(), arg.getEndTime());
            final String url = uriBuilder.toString() + "&leadType=(leadType:SPONSORED)" + owner + timeRange;

            final Integer total = linkedinApiClient.get(url, body -> {
                log.info("getAdFromResponses arg: {} body: {}", arg, body);
                final JSONObject jsonObject = JSON.parseObject(body);
                elements.addAll(jsonObject.getJSONArray("elements").stream()
                        .map(o -> (JSONObject) o)
                        .map(e -> parseFormResponseToErpObject(e, formMetaData))
                        .collect(Collectors.toList()));
                return jsonObject.getJSONObject("paging").getInteger("total");
            });
            if (Objects.isNull(result.getTotalNum())) {
                result.setTotalNum(total);
            }
            // 如果找到数据了，直接返回，说明这个表单是在这个账户下的
            if (CollectionUtils.isNotEmpty(elements)) {
                return result;
            }
        }
        return result;
    }

    // 这里的表单元数据的option的key是option的id,而在创建表单连接对象的时候，option的key和value都是领英表单的值
    // com.facishare.open.erp.connector.proxy.linkedin.controller.LinkedinController.getFormMetaData 和这个接口是不一样的
    public GetFormMetaData.Result getFormMetaData(LinkedinApiClient linkedinApiClient, String formId) {
        final URIBuilder uriBuilder = new URIBuilder();
        uriBuilder.setScheme("https");
        uriBuilder.setHost("api.linkedin.com");
        uriBuilder.setPath("/rest/leadForms/" + formId);
        final String url = uriBuilder.toString();
        GetFormMetaData.Result result = new GetFormMetaData.Result();
        List<FormMetaData> metaDataList = linkedinApiClient.get(url,
                body -> {
                    log.info("getFormMetaData formId: {} body: {}", formId, body);
                    final JSONObject jsonObject = JSON.parseObject(body);
                    final JSONArray questions = jsonObject.getJSONObject("content").getJSONArray("questions");
                    result.setVersionId(jsonObject.getInteger("versionId"));
                    return questions.stream()
                            .map(o -> (JSONObject) o)
                            .map(json -> {
                                final String name = json.getString("name");
                                final String key = json.getString("questionId");
                                String questionType = json.getString("predefinedField");
                                final JSONObject details = json.getJSONObject("questionDetails").getJSONObject("multipleChoiceQuestionDetails");
                                List<FormMetaData.Options> options = new ArrayList<>();
                                if (Objects.nonNull(details)) {
                                    options = details.getJSONArray("options").stream()
                                            .map(option -> {
                                                JSONObject optionJsonObject = (JSONObject) option;
                                                String optionId = optionJsonObject.getString("id");
                                                final String s = optionJsonObject.getJSONObject("text").getJSONObject("localized").getString("en_US");
                                                return new FormMetaData.Options(optionId, s);
                                            }).collect(Collectors.toList());
                                    questionType = FormMetaData.TYPE_SELECT_ONE;
                                }
                                questionType = org.apache.commons.lang3.StringUtils.isBlank(questionType) ? "text" : questionType;
                                return new FormMetaData(key, name, questionType, options);
                            })
                            .collect(Collectors.toList());
                });

        result.setVersionId(result.getVersionId() == null ? 1 : result.getVersionId());
        result.setMetaDataList(metaDataList);
        return result;
    }

    private List<String> getAllAccountId(final LinkedinApiClient linkedinApiClient) {
        return linkedinApiClient.get("https://api.linkedin.com/rest/adAccounts?q=search", body -> {
            final JSONArray elements = JSON.parseObject(body).getJSONArray("elements");
            if (elements.isEmpty()) {
                throw new LinkedinException("Failed to obtain the account ID. Please create a marketing management tool account first");
            }
            return elements.stream().map(o -> ((JSONObject) o).getString("id")).collect(Collectors.toList());
        });
    }

    // 不支持时间筛选，直接全部返回
    private List<ErpObjectData> getAllCampaign(ListByTime.Arg arg) {
        LinkedinApiClient linkedinApiClient = new LinkedinApiClient(arg.getConnectParam());
        List<ErpObjectData> resultList = Lists.newArrayList();
        int pageSize = 500;
        List<String> accountIdList = new ArrayList<>();
        if(!Objects.isNull(arg.getAccountIds()) && arg.getAccountIds().length()>0){
            String[] split = arg.getAccountIds().split(",");
            for(String accountId : split){
                accountIdList.add(accountId);
            }
        }else {
            accountIdList = getAllAccountId(linkedinApiClient);
        }
        for (String accountId : accountIdList) {
            final URIBuilder uriBuilder = new URIBuilder();
            uriBuilder.setScheme("https");
            uriBuilder.setHost("api.linkedin.com");
            uriBuilder.setPath("/rest/adAccounts/" + accountId + "/adCampaigns");
            uriBuilder.setParameter("q", "search");
            uriBuilder.setParameter("sortOrder", "ASCENDING");
            uriBuilder.setParameter("pageSize", String.valueOf(pageSize));
            AtomicReference<String> nextPageToken = new AtomicReference<>();
            while (true) {
                if (StringUtils.isNotBlank(nextPageToken.get())) {
                    uriBuilder.setParameter("pageToken", nextPageToken.get());
                }
                String url = uriBuilder.toString();
                List<ErpObjectData> erpObjectDataList = linkedinApiClient.get(url, body -> {
                    final JSONObject jsonObject = JSON.parseObject(body);
                    JSONArray elements = jsonObject.getJSONArray("elements");
                    JSONObject metadata = jsonObject.getJSONObject("metadata");
                    if (metadata != null) {
                        nextPageToken.set(metadata.getString("nextPageToken"));
                    }
                    return parseCampaignToErpObjectData(elements);
                });
                if (CollectionUtils.isNotEmpty(erpObjectDataList)) {
                    resultList.addAll(erpObjectDataList);
                }
                if (CollectionUtils.isEmpty(erpObjectDataList) || erpObjectDataList.size() < pageSize) {
                    break;
                }
            }

        }
        return resultList;
    }

    // 领英接口不支持分页
    private List<ErpObjectData> getAnalysisData(ListByTime.Arg arg) {
        // 获取广告数据 并抽取出id进行过滤查询
        List<ErpObjectData> allAdvertiseList = getAllAdvertise(arg);
        if (CollectionUtils.isEmpty(allAdvertiseList)) {
            return Lists.newArrayList();
        }
        LinkedinApiClient linkedinApiClient = new LinkedinApiClient(arg.getConnectParam());
        // 获取间隔的天数
        long diffInMilliseconds = Math.abs(arg.getEndTime() - arg.getStartTime());
        long diffDay = TimeUnit.DAYS.convert(diffInMilliseconds, TimeUnit.MILLISECONDS);
        String dateRange = "(start:(year:%s,month:%s,day:%s),end:(year:%s,month:%s,day:%s))";
        Date startDate = new Date(arg.getStartTime());
        List<ErpObjectData> resultList = Lists.newArrayList();
        for (int i = 0; i <= diffDay; i++) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);
            dateRange = String.format(dateRange, calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH) + 1, calendar.get(Calendar.DAY_OF_MONTH),
                    calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH) + 1, calendar.get(Calendar.DAY_OF_MONTH));
            String url = "https://api.linkedin.com/rest/adAnalytics?q=analytics&timeGranularity=DAILY&dateRange=" + dateRange + "&pivot=";
            // 取全部账号的广告id进行查询
            List<String> idList = allAdvertiseList.stream().map(e -> URLEncoder.encode(e.getMaster().get("id").toString())).collect(Collectors.toList());
            url += "CREATIVE" + "&creatives=List(" + String.join(",", idList) + ")";
            String launchDate = DateFormatUtils.format(startDate, "yyyy-MM-dd");
            List<ErpObjectData> erpObjectDataList = linkedinApiClient.get(url, body -> parseAnalysisDataToErpObject(body, launchDate));
            // 减掉1小时是为了查询最后一天的数据
            startDate = DateUtils.addDays(startDate, 1);
            resultList.addAll(erpObjectDataList);
        }

        return resultList;
    }

    private ErpObjectData getAnalysisDataById(GetById.Arg arg) {
        LinkedinApiClient linkedinApiClient = new LinkedinApiClient(arg.getConnectParam());
        String dataId = arg.getDataId();
        String[] arr = dataId.split(":");
        String launchDate = arr[arr.length - 1];
        String dateRange = "(start:(year:%s,month:%s,day:%s),end:(year:%s,month:%s,day:%s))";
        Date date = null;
        try {
            date = DateUtils.parseDate(launchDate, new String[]{"yyyy-MM-dd"});
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        dateRange = String.format(dateRange, calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH) + 1, calendar.get(Calendar.DAY_OF_MONTH),
                calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH) + 1, calendar.get(Calendar.DAY_OF_MONTH));
        String url = "https://api.linkedin.com/rest/adAnalytics?q=analytics&timeGranularity=DAILY&dateRange=" + dateRange + "&pivot=";
        url += "CREATIVE" + "&creatives=List(" + URLEncoder.encode(dataId.replace(":" + launchDate, "")) + ")";
        List<ErpObjectData> erpObjectDataList = linkedinApiClient.get(url, body -> parseAnalysisDataToErpObject(body, launchDate));
        return CollectionUtils.isEmpty(erpObjectDataList) ? null : erpObjectDataList.get(0);
    }

    private static List<ErpObjectData> parseAnalysisDataToErpObject(String body, String launchDate) {
        JSONObject jsonObject = JSONObject.parseObject(body);
        JSONArray elements = jsonObject.getJSONArray("elements");
        List<ErpObjectData> list = Lists.newArrayList();
        if (CollectionUtils.isEmpty(elements)) {
            return list;
        }
        for (Object object : elements) {
            JSONObject element = (JSONObject) object;
            JSONArray pivotValues = element.getJSONArray("pivotValues");
            if (CollectionUtils.isEmpty(pivotValues)) {
                continue;
            }
            ErpObjectData erpObjectData = new ErpObjectData();
            Map<String, Object> objectData = new HashMap<>();
            erpObjectData.setMaster(objectData);
            // 广告的id
            String pivot = (String) pivotValues.get(0);
            objectData.put("linkedin_ad", pivot);
            objectData.put("id", pivot + ":" + launchDate);
            //点击
            Long clicks = element.getLong("clicks");
            objectData.put("click", clicks == null ? 0L : clicks);
            // 展现
            Long impressions = element.getLong("impressions");
            objectData.put("show", impressions == null ? 0L : impressions);
            // 消费
            BigDecimal costInUsd = element.getBigDecimal("costInUsd");
            objectData.put("cost", costInUsd == null ? BigDecimal.ZERO : costInUsd);
            // 投放日期
            objectData.put("launch_date", launchDate);
            list.add(erpObjectData);
        }
        return list;
    }


    // 不支持时间筛选，直接全部返回
    private List<ErpObjectData> getAllAdvertise(ListByTime.Arg arg) {
        LinkedinApiClient linkedinApiClient = new LinkedinApiClient(arg.getConnectParam());
        List<ErpObjectData> resultList = Lists.newArrayList();
        int pageSize = 500;
        List<String> accountIdList = new ArrayList<>();
        if(!Objects.isNull(arg.getAccountIds()) && arg.getAccountIds().length()>0){
            String[] split = arg.getAccountIds().split(",");
            for(String accountId : split){
                accountIdList.add(accountId);
            }
        }else {
            accountIdList = getAllAccountId(linkedinApiClient);
        }
        for (String accountId : accountIdList) {
            final URIBuilder uriBuilder = new URIBuilder();
            uriBuilder.setScheme("https");
            uriBuilder.setHost("api.linkedin.com");
            uriBuilder.setPath("/rest/adAccounts/" + accountId + "/creatives");

            uriBuilder.setParameter("q", "criteria");
            uriBuilder.setParameter("sortOrder", "ASCENDING");
            uriBuilder.setParameter("pageSize", String.valueOf(pageSize));

            AtomicReference<String> nextPageToken = new AtomicReference<>();
            while (true) {
                if (StringUtils.isNotBlank(nextPageToken.get())) {
                    uriBuilder.setParameter("pageToken", nextPageToken.get());
                }
                String url = uriBuilder.toString();
                List<ErpObjectData> erpObjectDataList = linkedinApiClient.get(url, body -> {
                    final JSONObject jsonObject = JSON.parseObject(body);
                    JSONArray elements = jsonObject.getJSONArray("elements");
                    JSONObject metadata = jsonObject.getJSONObject("metadata");
                    if (metadata != null) {
                        nextPageToken.set(metadata.getString("nextPageToken"));
                    }
                    return parseAdvertiseToErpObjectData(elements, linkedinApiClient);
                });
                if (CollectionUtils.isNotEmpty(erpObjectDataList)) {
                    resultList.addAll(erpObjectDataList);
                }
                if (CollectionUtils.isEmpty(erpObjectDataList) || erpObjectDataList.size() < pageSize) {
                    break;
                }
            }

        }
        return resultList;
    }

    private ErpObjectData getCampaignById(GetById.Arg arg) {
        LinkedinApiClient linkedinApiClient = new LinkedinApiClient(arg.getConnectParam());
        List<String> accountIdList = new ArrayList<>();
        if(!Objects.isNull(arg.getAccountIds()) && arg.getAccountIds().length()>0){
            String[] split = arg.getAccountIds().split(",");
            for(String accountId : split){
                accountIdList.add(accountId);
            }
        }else {
            accountIdList = getAllAccountId(linkedinApiClient);
        }
        for (String accountId : accountIdList) {
            try {
                String url = "https://api.linkedin.com/rest/adAccounts/" + accountId + "/adCampaigns/" + URLEncoder.encode(arg.getDataId(), "UTF-8");
                ErpObjectData erpObjectData = linkedinApiClient.get(url, body -> {
                    JSONObject jsonObject = JSON.parseObject(body);
                    String id = jsonObject.getString("id");
                    if (StringUtils.isBlank(id)) {
                        return null;
                    }
                    JSONArray elements = new JSONArray();
                    elements.add(jsonObject);
                    return parseCampaignToErpObjectData(elements).get(0);
                });
                if (erpObjectData != null) {
                    return erpObjectData;
                }
            } catch (Exception e) {
                log.warn("getCampaignById error, arg: {}", arg);
            }
        }
        return null;
    }


    private ErpObjectData getAdvertiseById(GetById.Arg arg) {
        LinkedinApiClient linkedinApiClient = new LinkedinApiClient(arg.getConnectParam());
        List<String> accountIdList = new ArrayList<>();
        if(!Objects.isNull(arg.getAccountIds()) && arg.getAccountIds().length()>0){
            String[] split = arg.getAccountIds().split(",");
            for(String accountId : split){
                accountIdList.add(accountId);
            }
        }else {
            accountIdList = getAllAccountId(linkedinApiClient);
        }
        for (String accountId : accountIdList) {
            try {
                String url = "https://api.linkedin.com/rest/adAccounts/" + accountId + "/creatives/" + URLEncoder.encode(arg.getDataId(), "UTF-8");
                ErpObjectData erpObjectData = linkedinApiClient.get(url, body -> {
                    JSONObject jsonObject = JSON.parseObject(body);
                    String id = jsonObject.getString("id");
                    if (StringUtils.isBlank(id)) {
                        return null;
                    }
                    JSONArray elements = new JSONArray();
                    elements.add(jsonObject);
                    return parseAdvertiseToErpObjectData(elements, linkedinApiClient).get(0);
                });
                if (erpObjectData != null) {
                    return erpObjectData;
                }
            } catch (Exception e) {
                log.warn("getAdvertiseById error, arg: {}", arg);
            }
        }
        return null;
    }

    private static final String defaultSubject = "广告id-";   // ignoreI18n
    private List<ErpObjectData> parseAdvertiseToErpObjectData(JSONArray elements, LinkedinApiClient linkedinApiClient) throws UnsupportedEncodingException {
        List<ErpObjectData> erpObjectDataList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(elements)) {
            return erpObjectDataList;
        }
        for (Object o : elements) {
            JSONObject element = (JSONObject) o;
            Map<String, Object> data = Maps.newHashMap();
            String id = element.getString("id");
            data.put("id", id);
            JSONObject review = element.getJSONObject("review");
            String status = "";
            if (review != null) {
                status = getObjectStatusByAdvertiseStatus(review.getString("status"));
            }
            JSONObject content = element.getJSONObject("content");
            String subject = null;
            if (content != null) {
                String shareId = content.getString("reference");
                // 没有批量接口，只能一个一个查
                subject = getShareSubject(shareId, linkedinApiClient);
            }
            if (StringUtils.isBlank(subject)) {
                subject = defaultSubject + id;
            }
            data.put("name", subject);
            String campaign = element.getString("campaign");
            String campaignId = campaign.split(":")[3];
            data.put("status", status);
            data.put("startDate", "2024-05-25");
            data.put("endDate", "2034-05-25");
            data.put("linkedin_campaign", campaignId);
            ErpObjectData erpObjectData = new ErpObjectData();
            erpObjectData.setMaster(data);
            erpObjectDataList.add(erpObjectData);
        }
        return erpObjectDataList;
    }

    private String getShareSubject(String id, LinkedinApiClient linkedinApiClient) throws UnsupportedEncodingException {
        String url = "https://api.linkedin.com/v2/shares/" + URLEncoder.encode(id, "UTF-8");
        JSONObject jsonObject = linkedinApiClient.get(url, JSON::parseObject);
        return jsonObject.getString("subject");
    }


    private List<ErpObjectData> parseCampaignToErpObjectData(JSONArray elements) {
        List<ErpObjectData> erpObjectDataList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(elements)) {
            return erpObjectDataList;
        }
        for (Object o : elements) {
            JSONObject element = (JSONObject) o;
            Map<String, Object> data = Maps.newHashMap();
            Long campaignId = element.getLong("id");
            data.put("id", String.valueOf(campaignId));

            String name = element.getString("name");
            data.put("name", name);

            String status = element.getString("status");
            data.put("status", getObjectStatusByCampaignStatus(status));

            JSONObject runSchedule = element.getJSONObject("runSchedule");
            Long start = null;
            Long end = null;
            if (runSchedule != null) {
                start = runSchedule.getLong("start");
                end = runSchedule.getLong("end");
            }
            if (start != null) {
                String startDate = DateFormatUtils.format(start, "yyyy-MM-dd");
                data.put("startDate", startDate);
            }
            if (end != null) {
                String endDate = DateFormatUtils.format(end, "yyyy-MM-dd");
                data.put("endDate", endDate);
            }
            ErpObjectData erpObjectData = new ErpObjectData();
            erpObjectData.setMaster(data);
            erpObjectDataList.add(erpObjectData);
        }
        return erpObjectDataList;
    }


    private String getObjectStatusByCampaignStatus(String status) {
        if ("ACTIVE".equals(status) || "DRAFT".equals(status)) {
            return "ongoing";
        }
        return "complete";
    }

    private String getObjectStatusByAdvertiseStatus(String status) {
        if ("APPROVED".equals(status) || "NEEDS_REVIEW".equals(status)) {
            return "ongoing";
        }
        return "complete";
    }
}
