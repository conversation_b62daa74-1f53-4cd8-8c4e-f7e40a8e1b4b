package com.facishare.open.erp.connector.proxy.linkedin.service;

import com.alibaba.fastjson.JSON;
import com.facishare.open.erp.connector.proxy.linkedin.exception.LinkedinException;
import com.facishare.open.erp.connector.proxy.linkedin.model.LinkedinConnectParam;
import com.facishare.open.erp.connector.proxy.linkedin.model.SimpleResponse;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import lombok.Data;
import lombok.SneakyThrows;
import okhttp3.FormBody;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/5/9 17:45:54
 */
@Data
public class LinkedinApiClient {
    private String token;

    private LinkedinConnectParam param;

    private OkHttpSupportHelp okHttpSupport = OkHttpSupportHelp.getOkHttpSupportHelp();

    public LinkedinApiClient() {
    }

    public LinkedinApiClient(final String connectParam) {
        param = JSON.parseObject(connectParam, LinkedinConnectParam.class);
        this.token = param.getToken();
    }

    public <V> V get(String url, ExceptionFunction<String, V> callback) {
        final Request.Builder builder = new Request.Builder()
                .url(url);
        if (StringUtils.isNotBlank(token)) {
            builder.addHeader("Authorization", "Bearer " + token);
        }
        builder.addHeader("X-Restli-Protocol-Version", "2.0.0");
        builder.addHeader("LinkedIn-Version", "202405");
        return execute(builder.build(), callback);
    }

    // 暂时固定为application/x-www-form-urlencoded
    public <V> V post(String url, Map<String, String> body, ExceptionFunction<String, V> callback) {
        final FormBody.Builder bodyBuilder = new FormBody.Builder();
        body.forEach(bodyBuilder::add);
        final Request.Builder builder = new Request.Builder()
                .url(url)
                .post(bodyBuilder.build());
        if (StringUtils.isNotBlank(token)) {
            builder.addHeader("Authorization", "Bearer " + token);
        }
        builder.addHeader("X-Restli-Protocol-Version", "2.0.0");
        builder.addHeader("LinkedIn-Version", "202405");
        return execute(builder.build(), callback);
    }

    public interface ExceptionFunction<T, V> {
        V apply(T t) throws Exception;
    }

    @SneakyThrows
    public <V> V execute(Request request, ExceptionFunction<String, V> callback) {
        SimpleResponse response = okHttpSupport.getResponse(request);
        return processRest(response, request, callback);
    }

    private static <V> V processRest(final SimpleResponse response, final Request request, final ExceptionFunction<String, V> callback) throws Exception {
        final String responseBody = response.getBody();

        if (!response.isSuccessful()) {
            throw new LinkedinException(request.url().toString(), response.getCode(), responseBody);
        }

        if (Objects.isNull(responseBody)) {
            throw new LinkedinException("Calling LinkedIn returns null value,url:" + request.url());
        }

        return callback.apply(responseBody);
    }
}
