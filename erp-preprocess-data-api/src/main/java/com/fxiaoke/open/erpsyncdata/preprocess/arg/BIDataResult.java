package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/3/28 10:31
 *
 * bi看板的数据
 * @desc
 */
@Data
public class BIDataResult  implements Serializable {
    private Integer code;
    private String message;
    private DataViewResult data;

    @Data
    public static class DataViewResult{
        private List<List<BIDataSetResult>>  dataSet;
        private List<BIDisplayResult> displayFields;
    }
    @Data
    public static class BIDataSetResult implements Serializable{
        private String formattedValue;

        private String formattedShowValue;

        private String valueCode;

        private String value;
    }
    @Data
    public static class BIDisplayResult implements Serializable{
        private String fieldName;
        private String dim;
        private boolean canShowDetail;
        private String fieldId;
        private DimenstionConfig dimenstionConfig;
        private String total;
        private String totalValue;
        private String totalFormattedShowValue;
    }
    @Data
    public static class DimenstionConfig implements Serializable{
        private Integer groupType;
        private String dimType;
        private String dimensionConfigAnalysisType;

    }
    public boolean isSuccess() {
        return 200==code;
    }
}
