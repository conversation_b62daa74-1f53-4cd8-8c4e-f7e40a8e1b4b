package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> (^_−)☆
 */
@Data
@Accessors(chain = true)
public class CheckStreamEnableArg {
    /**
     * crm对象apiName
     */
    private String crmObjApiName;
    /**
     * erp中间对象ApiName
     */
    private String erpSplitObjApiName;
    /**
     * erp真实对象apiName
     */
    private String erpRealObjApiName;

    /**
     * 源系统类型，1为CRM->ERP,2为ERP往CRM
     */
    private Integer sourceTenantType;
}
