package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 17:33 2020/12/22
 * @Desc:
 */
@Data
public class CopyTenantConfigurationArg implements Serializable {
    @Data
    public static class CopyConnectArg implements Serializable {
        /**
         * 源企业
         */
        private String sourceTenantId;
        /**
         * 源企业数据中心id
         */
        private String sourceDataCenterId;
        /**
         * 目标企业
         */
        private String targetTenantId;
    }

    @Data
    public static class CopyErpObjArg implements Serializable {
        /**
         * 源企业
         */
        private String sourceTenantId;
        /**
         * 源企业数据中心id
         */
        private String sourceDataCenterId;
        /**
         * 目标企业
         */
        private String targetTenantId;
        /**
         * 目标企业数据中心id
         */
        private String destDataCenterId;
        /**
         * 指定对象，
         */
        private List<String> objApiNames;
    }
    @Data
    public static class CopyEaiConfigArg implements Serializable {
        /**
         * 源企业
         */
        private String sourceTenantId;
        /**
         * 源企业数据中心id
         */
        private String sourceDataCenterId;
        /**
         * 目标企业
         */
        private String targetTenantId;
        /**
         * 目标企业数据中心id
         */
        private String destDataCenterId;
        /**
         * 指定对象，
         */
        private List<String> objApiNames;
    }
    @Data
    public static class CopyPloyAndDetailArg implements Serializable {
        /**
         * 源企业
         */
        private String sourceTenantId;
        /**
         * 源企业数据ERP中心id
         */
        private String sourceDataCenterId;
        /**
         * 目标企业
         */
        private String targetTenantId;
        /**
         * 目标企业数据ERP中心id
         */
        private String destDataCenterId;
        /**
         * 指定对象，crm对象以及erp中间对象名
         */
        private List<CopyApiNameMapping> objApiNames;
        /**
         * 方向,1:erp->crm    2:crm->erp
         */
        private Integer direction;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class CopyApiNameMapping implements Serializable{
        private String sourceApiName;
        private String destApiName;
    }


}
