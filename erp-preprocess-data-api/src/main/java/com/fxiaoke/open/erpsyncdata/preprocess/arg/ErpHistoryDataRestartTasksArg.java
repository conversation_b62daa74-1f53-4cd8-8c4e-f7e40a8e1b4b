package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 20:06 2021/8/0
 * @Desc:
 */
@Getter
@Setter
@ToString
@ApiModel("任务重新执行的请求参数")
public class ErpHistoryDataRestartTasksArg extends PageArg {

    @ApiModelProperty("任务数据id")
    private String id;


    @ApiModelProperty("任务执行重新设置时间")
    private Long executeTime;
}
