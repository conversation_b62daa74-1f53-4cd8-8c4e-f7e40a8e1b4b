package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.CompleteDataWriteMqData;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
public class ExecuteCustomFunctionArg implements Serializable {
    private static final long serialVersionUID = -4691813910750885021L;
    @ApiModelProperty("函数apiName")
    private String apiName;
    @ApiModelProperty("函数参数")
    private ExecuteCustomFunctionParameterData parameter;
    @ApiModelProperty("函数命名空间")
    private String nameSpace;
    @ApiModelProperty("绑定对象apiName")
    private String bindingObjectAPIName;
    @ApiModelProperty("对象数据")
    private Map<String, Object> objectData;
    @ApiModelProperty("从对象数据")
    private Map<String, List<Map<String, Object>>> details;
    @Data
    public static class ExecuteCustomFunctionParameterData {
        @ApiModelProperty("同步数据id")
        private String syncDataId;
        @ApiModelProperty("源企业id")
        private String sourceTenantId;
        @ApiModelProperty("源对象apiName")
        private String sourceObjectApiName;
        @ApiModelProperty("源事件类型")
        private Integer sourceEventType;
        @ApiModelProperty("目标企业id")
        private String destTenantId;
        @ApiModelProperty("同目标对象apiName")
        private String destObjectApiName;
        @ApiModelProperty("目标事件类型")
        private Integer destEventType;
        @ApiModelProperty("源数据，CRM不用，ERP使用")
        private ObjectData sourceData;
        @ApiModelProperty("返回写入结果")
        private CompleteDataWriteMqData completeDataWriteMqData;

    }
}
