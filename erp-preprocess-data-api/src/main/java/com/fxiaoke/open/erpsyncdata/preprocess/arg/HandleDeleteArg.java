package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/2/5 19:27:44
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HandleDeleteArg implements Serializable {
    /**
     * 前期灰度时,只有gray有代码
     * 依赖这个来路由到gray的all服务
     */
    private String route;
    private String tenantId;
    private String objectApiName;
    private String dataId;
}
