package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 14:14 2020/8/22
 * @Desc:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ListObjectFieldsArg implements Serializable {
    @ApiModelProperty("需要获取该对象的apiName")
    private String objectApiName;
    @ApiModelProperty("企业类型：1 crm,2 erp")
    private Integer tenantType;
}
