package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/6/17 16:53 查询库存可用量
 * @Version 1.0
 *
 * 参考url: https://vip.kingdee.com/article/86193102758814720
 *
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QueryCombineQtyArg implements Serializable {
//    [{
////        "fstockorgnumbers":"", /*组织编码，多个用,分隔*/
////        "fmaterialnumbers":"", /*物料编码，多个用,分隔*/
////        "fstocknumbers":"",/*仓库编码，多个用,分隔*/
////                "flotnumbers":"",/*批号编码，多个用,分隔*/
////                "isshowstockloc":true,/*是否查询仓位，查询仓位对性能有影响*/
////                "isshowauxprop":true,/*是否查询辅助属性，查询辅助属性对性能有影响*/
////                "pageindex":1,/*当前页*/
////                "pagerows":1000 /*每页显示行数*/
////    }]

    /*组织编码，多个用,分隔*/
    private String fstockorgnumbers;
    /*物料编码，多个用,分隔*/
    private String fmaterialnumbers;
    /*仓库编码，多个用,分隔*/
    private String fstocknumbers;
    /*批号编码，多个用,分隔*/
    private String flotnumbers;
    /*是否查询仓位，查询仓位对性能有影响*/
    private Boolean isshowstockloc;
    /*是否查询辅助属性，查询辅助属性对性能有影响*/
    private Boolean isshowauxprop;
    private Integer pageindex;
    private Integer pagerows;
    private String fstocklocid;//这个字段不起作用
}

//返回值示例
//{    
//        "rowcount": 1000,/*总行数*/    
//        "success": true,/*调用结果/    
//"message": "success",    
//"data": [        
//{            
//"FID": "",/*即时库存內码*/            
//        "FSTOCKORGID": 0,/*库存组织ID*/            
//        "FSTOCKORGNUMBER": "", /*库存组织编码*/           
//        "FSTOCKORGNAME": "",/*库存组织名称*/            
//        "FKEEPERTYPEID": "BD_KeeperOrg", /*保管者类型*/           
//        "FKEEPERTYPENAME": "",            
//        "FKEEPERID": 0,  /*保管者*/          
//        "FKEEPERNUMBER": "",/*保管者编码*/            
//        "FKEEPERNAME": "",/*保管者名称*/            
//        "FOWNERTYPEID": "",/*货主类型*/            
//        "FOWNERTYPENAME": null,            
//        "FOWNERID": 0,/*货主ID*/            
//        "FOWNERNUMBER": "",/*货主编码*/            
//        "FOWNERNAME": "",/*货主名称*/            
//        "FSTOCKID": ,/*仓库ID*/            
//        "FSTOCKNUMBER": "",/*仓库编码*/            
//        "FSTOCKNAME": "",/*仓库名称*/            
//        "FSTOCKLOCID": 0,/*仓位ID*/            
//        "FSTOCKLOC": null, /*仓位编码及名称*/           
//        "FAUXPROPID": 0,/*辅助属性ID*/            
//        "FAUXPROP": null, /*辅助属性*/           
//        "FSTOCKSTATUSID": 0, /*库存状态ID*/           
//        "FSTOCKSTATUSNUMBER": "",            
//        "FSTOCKSTATUSNAME": "",            
//        "FLOT": 0,  /*批号ID*/          
//        "FLOTNUMBER": "",            
//        "FBOMID": 0,            
//        "FBOMNUMBER": "",            
//        "FMTONO": "",            
//        "FPROJECTNO": "",            
//        "FPRODUCEDATE": "",            
//        "FEXPIRYDATE": "",            
//        "FBASEUNITID": 0,/*基本单位ID*/            
//        "FBASEUNITNUMBER": "",            
//        "FBASEUNITNAME": "",            
//        "FBASEQTY": 0,/*基本单位数量*/            
//        "FBASELOCKQTY": 0,/*锁库数量（基本单位）*/            
//        "FSECQTY": 0,/*辅单位数量*/            
//        "FSECLOCKQTY": 0,            
//        "FSTOCKUNITID": 0, /*库存单位*/           
//        "FSTOCKUNITNUMBER": "",            
//        "FSTOCKUNITNAME": "",            
//        "FMATERIALID": , /*物料內码*/           
//        "FMATERIALNUMBER": "",            
//        "FMATERIALNAME": "",            
//        "FQTY": 1,/*库存数量*/            
//        "FLOCKQTY": 0,/*锁库数量*/            
//        "FSECUNITID": 0, /*辅单位*/           
//        "FSECUNITNUMBER":"",            
//        "FSECUNITNAME": "",            
//        "FOBJECTTYPEID": "STK_Inventory",            
//        "FBASEAVBQTY": 1,/*可用量（基本单位）*/            
//        "FAVBQTY": 1,            
//        "FSECAVBQTY": 0        
//        }    
//        ]}
