package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.NoticeType;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/11/22
 */
@Data
@Accessors(chain = true)
public class SendSuperAdminNoticeArg implements Serializable {

    /**
     * 企业Id
     */
    private String tenantId;

    /**
     * 数据中心Id
     */
    private String dcId;

    /**
     * 消息类型
     */
    private final NoticeType noticeType;

    /**
     * 消息体
     */
    private String msg;


}
