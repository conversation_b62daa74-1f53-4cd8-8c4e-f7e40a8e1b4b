package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.NotifyType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/1/6 17:14
 * @Version 1.0
 */
@Data
@ApiModel("消息同步结果的模板参数")
public class SyncStatusMessageArg  implements Serializable {
    private static final long serialVersionUID = -1L;
    @ApiModelProperty("连接器的名字")
    private String connInfoName;
    @ApiModelProperty("企业ea")
    private String tenantId;
    @ApiModelProperty("同步方向")
    private String syncDirection;
    @ApiModelProperty("源对象名称")
    private String srcObjectName;
    @ApiModelProperty("开始时间")
    private Long startTime;
    @ApiModelProperty("结束时间")
    private Long endTime;
    @ApiModelProperty("同步成功的数据")
    private List<SyncDataStatusMessage> successList;
    @ApiModelProperty("同步失败的数据")
    private List<SyncDataStatusMessage> errorList;
    @ApiModelProperty("成功详情链接")
    private String successUrl;
    @ApiModelProperty("失败详情链接")
    private String errorUrl;
    @ApiModelProperty("通知类型")
    private List<NotifyType> notifyType;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SyncDataStatusMessage implements Serializable{
        private static final long serialVersionUID = -1L;
        @ApiModelProperty("源数据ID")
        private String dataId;
        @ApiModelProperty("数据")
        private String dataName;
        @ApiModelProperty("同步状态")
        private Integer status;
        @ApiModelProperty("是否回写crm失败")
        private Boolean reverseWrite2CrmFailed;
        @ApiModelProperty("是否同步后函数执行失败")
        private Boolean afterFuncFailed;
        @ApiModelProperty("同步详情")
        private String remark;
        @ApiModelProperty("状态详情")
        private String statusMessage;
    }
}
