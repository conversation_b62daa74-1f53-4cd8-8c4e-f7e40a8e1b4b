package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 11:57 2020/8/24
 * @Desc:
 */
@Data
@ApiModel
public class UpdatePloyDetailAndFieldMappingArg {
    @ApiModelProperty("策略id")
    private String ployId;
    @ApiModelProperty("策略明细id")
    private String ployDetailId;
    @ApiModelProperty("字段映射")
    private List<FieldMapping> fieldMappings;
    @Data
    @ApiModel
    public static class FieldMapping{
        @ApiModelProperty("erp对象apiName")
        private String erpObj;
        @ApiModelProperty("erp字段apiName")
        private String erpField;
        @ApiModelProperty("erp字段类型")
        private String erpFieldType;
        @ApiModelProperty("crm对象apiName")
        private String crmObj;
        @ApiModelProperty("crm字段apiName")
        private String crmField;
        @ApiModelProperty("crm字段类型")
        private String crmFieldType;
        @ApiModelProperty("单选映射")
        private List<OptionMapping> optionMappings;
    }
    @Data
    @ApiModel
    public static class OptionMapping{
        @ApiModelProperty("erp单选值")
        private String erpValue;
        @ApiModelProperty("crm单选值")
        private String crmValue;
    }
}
