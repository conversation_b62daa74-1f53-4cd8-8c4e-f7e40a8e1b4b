package com.fxiaoke.open.erpsyncdata.preprocess.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date: 19:17 2020/10/26
 * @Desc:
 */
@Getter
@AllArgsConstructor
public enum ErpCustomFunctionParameterEnum {
    SOURCE_DATA_ID("sourceDataId", "源数据id","String", I18NStringEnum.s69.getI18nKey()),
    SYNC_DATA_ID("syncDataId", "同步数据id","String", I18NStringEnum.s655.getI18nKey()),
    SOURCE_TENANT_ID("sourceTenantId", "源企业id", "String", I18NStringEnum.s656.getI18nKey()),
    SOURCE_OBJECT_APINAME("sourceObjectApiName", "源对象apiName","String", I18NStringEnum.s657.getI18nKey()),
    SOURCE_EVENT_TYPE("sourceEventType", "源事件类型", "Integer", I18NStringEnum.s658.getI18nKey()),
    DEST_TENANT_ID("destTenantId", "目标企业id", "String", I18NStringEnum.s659.getI18nKey()),
    DEST_OBJECT_APINAME("destObjectApiName", "目标对象apiName", "String", I18NStringEnum.s660.getI18nKey()),
    DEST_EVENT_TYPE("destEventType", "目标事件类型", "Integer", I18NStringEnum.s661.getI18nKey()),
    OBJECT_DATA("objectData", "主对象数据", "Map", I18NStringEnum.s278.getI18nKey()),
    DETAILS("details", "从对象数据", "Map",I18NStringEnum.s662.getI18nKey()),
    ;
    private String argName;
    private String name;
    private String type;
    private String i18nKey;
}
