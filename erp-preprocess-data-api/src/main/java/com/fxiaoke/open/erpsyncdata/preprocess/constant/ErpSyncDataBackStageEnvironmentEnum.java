package com.fxiaoke.open.erpsyncdata.preprocess.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 11:21 2022/5/13
 * @Desc:后端all环境
 */
@AllArgsConstructor
@Getter
public enum ErpSyncDataBackStageEnvironmentEnum {

    //vip环境
    VIP("vip","VIP_ENVIROMENT_TENANT"),
    //jacoco环境
    JACOCO("jacoco","JACOCO_ENVIROMENT_TENANT"),
    //gray环境
    GRAY("gray","GRAY_TENANTS"),
    //normal环境
    NORMAL("normal",null),

    /**
     * 好丽友环境
     */
    HAOLIYOU("haoliyou","HA<PERSON>IYOU_TENANTS"),
    ;
    /**
     * 环境名称
     */
    private final String environment;

    /**
     * 配置key
     */
    private final String configKey;

    public static List<String> listConfigKeys(){
        List<String> list = new ArrayList<>();
        for (ErpSyncDataBackStageEnvironmentEnum value : values()) {
            if (value.configKey!=null){
                list.add(value.configKey);
            }
        }
        return list;
    }
}
