package com.fxiaoke.open.erpsyncdata.preprocess.data;

import com.fxiaoke.open.erpsyncdata.common.data.BaseResult;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
public class CompleteDataWriteMqData extends BaseResult implements Serializable {
    private static final long serialVersionUID = -921688034317092339L;

    public static final Integer SUCCESS_CODE = 0;
    public static final Integer OUT_ERROR_CODE = 5001;
    public static final Integer UNSUPPORT_EVENT_TYPE = 5002;
    private String tenantId;
    private Integer destEventType;
    private WriteResult writeResult;
    private List<WriteResult> detailWriteResults = new ArrayList<>();
    private Integer destTenantType;
    private String syncPloyDetailSnapshotId;

    private String objectApiName ;//源对象apiName
    private String mainObjApiName ;//源主对象apiName
    private String dataId ;//源数据id
    private Long version ;//源数据版本
    private String syncPloyDetailId ;//集成流id

    @Data
    public static class WriteResult extends BaseResult {
        /**
         * 请不要get该属性做查表操作，保留是因为上层还无法一一去除。
         */
        private String syncDataId;
        private SimpleSyncData simpleSyncData;
        private String destDataId;
        private Map<String, ObjectData> destDetailSyncDataIdAndDestDataMap;
    }

}
