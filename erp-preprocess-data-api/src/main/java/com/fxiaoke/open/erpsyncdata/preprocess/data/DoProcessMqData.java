package com.fxiaoke.open.erpsyncdata.preprocess.data;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

@Data
public class DoProcessMqData implements Serializable {
    private SyncDataData syncDataData;
//    /** 目标主对象数据id */
//    private String destMasterDataId;
//    /**
//     * 目标主对象apiName
//     */
//    private String destMasterObjectApiName;
    private MasterMappingsData masterMappingsData;

    private Map<String,SyncDataData> detailSyncDataDataMap;
}
