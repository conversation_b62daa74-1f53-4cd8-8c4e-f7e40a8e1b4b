package com.fxiaoke.open.erpsyncdata.preprocess.data;

import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.Map;

@Data
public class DoWriteMqData {
    private String tenantId;
    private String syncDataId;
    private String sourceTenantId;
    private Integer destEventType;
    private String destTenantId;
    private String destObjectApiName;
    private String destDataId;
    private ObjectData destData;
    private LinkedHashMap<String, ObjectData> destDetailSyncDataIdAndDestDataMap;
    private LinkedHashMap<String,String> destDetailObjMasterDetailFieldApiName;//目标明细对象对应的主从字段:<对象apiName,主从字段apiName>
    private String syncPloyDetailSnapshotId;
    /** 目标企业类型 */
    private Integer destTenantType;
    /**
     * 主对象对应的mappings data
     */
    private MasterMappingsData masterMappingsData;
    /** requestId */
    private String requestId;
    /**
     * key:syncDataId
     */
    private Map<String,SimpleSyncData> syncDataMap;

    /**
     * 批量写时使用，序列化成string传输syncData
     */
    private String syncDataEntityStr;

    /**
     * 批量写时使用
     */
    private SyncLogBaseInfo syncLogBaseInfo;

    private String objectApiName ;//源对象apiName
    private String mainObjApiName ;//源主对象apiName
    private String dataId ;//源数据id
    private Long version ;//源数据版本
    private String syncPloyDetailId ;//集成流id
}
