package com.fxiaoke.open.erpsyncdata.preprocess.data;

import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;

@Data
@ApiModel
public class QueryObjectOrFilterFieldMappingsData extends ArrayList<QueryObjectOrFilterFieldMappingsData.QueryObjectAndFilterFieldMappingsData> implements Serializable{

    @Data
    public static class QueryObjectAndFilterFieldMappingsData extends ArrayList<FilterData> implements Serializable {

    }
}
