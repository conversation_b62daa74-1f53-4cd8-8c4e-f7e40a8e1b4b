package com.fxiaoke.open.erpsyncdata.preprocess.data;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/3/17 11:45
 * 范围条件查询
 * @Version 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RangeConditionFieldMappingData implements Serializable {
    /**
     * 源对象的ERP对象名
     */
    private String sourceErpObjApiName;

    /**
     * 源对象的range类型对接字段
     */
    private String sourceRangeFieldApiName;
    /**
     * 筛选条件是需要依赖哪些对象的字段。需要去获取字段
     */
    private String sourceConditionObjectApiName;
    /**
     * 字段的映射的相关条件
     */
    private List<ConditionMapping> matchCondition;
    /**
     * 目标的字段apiName e.g. apply_account_range  apply_org_range
     */
    private String destRangeFieldApiName;
    /**
     * 需要对接的相关字段。
     */
    private String destConditionObjectApiName;

    private String syncPloyDetailId;
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    /**
     * crm适用客户的相关条件
     */
    public static class  ConditionMapping{
        private String sourceExpression;

        //crm这边的取值即为：CONDITION ALL FIXED
        private String destExpression;

        private List<FieldMappingData> rangeFieldMapping;

    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class  CrmFilterData{

        private List<CrmConditionExpression> filters;

        private String connector;

        public  void addFilterData(CrmConditionExpression crmConditionExpression){
            if(ObjectUtils.isEmpty(filters)){
                filters= Lists.newArrayList(crmConditionExpression);
            }else {
                filters.add(crmConditionExpression);
            }
        }

    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class  CrmConditionExpression{
        private String field_name;

        private String operator;

        private List<Object> field_values;

        private String connector;

        private String type;


    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class  ExpressionFilterData{

        private String value;


        private String type;

    }

}
