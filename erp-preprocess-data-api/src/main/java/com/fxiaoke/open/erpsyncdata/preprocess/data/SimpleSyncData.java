package com.fxiaoke.open.erpsyncdata.preprocess.data;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/5/17
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@NoArgsConstructor()
public class SimpleSyncData  implements Serializable {
    private String syncDataId;
    private String sourceObjectApiName;
    private String destObjectApiName;
    private String sourceDataId;
    private String destDataId;
    private String destDataName;

    public static SimpleSyncData create(){
        return new SimpleSyncData();
    }
}
