package com.fxiaoke.open.erpsyncdata.preprocess.data;

import com.fxiaoke.open.erpsyncdata.common.data.BaseData;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import java.util.List;
import java.util.Map;

import com.fxiaoke.open.erpsyncdata.common.util.RAMEstimable;
import com.fxiaoke.open.erpsyncdata.preprocess.util.RamUsageEstimateUtil;
import lombok.Data;

@Data
public class SyncDataData extends BaseData implements RAMEstimable {
    private String id;
    private String tenantId;
    private String sourceTenantId;
    private Integer sourceTenantType;
    private Integer destTenantType;
    private String destTenantId;
    private Integer sourceEventType;
    private String sourceObjectMasterApiName;
    private String sourceObjectApiName;
    private String sourceDataId;
    private ObjectData sourceData;
    private Map<String, List<String>> sourceDetailSyncDataIds;
    private Integer destEventType;
    private String destObjectApiName;
    private String destDataId;
    private ObjectData destData;
    private Integer status;
    private String syncPloyDetailSnapshotId;

    @Override
    public long ramBytesUsed(int depth) {
        return RamUsageEstimateUtil.sizeOfObject(sourceData, depth) +
                RamUsageEstimateUtil.sizeOfObject(sourceDetailSyncDataIds, depth);
    }
}
