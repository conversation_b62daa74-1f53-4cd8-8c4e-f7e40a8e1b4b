package com.fxiaoke.open.erpsyncdata.preprocess.data;

import lombok.*;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/6/10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SyncLogBaseInfo {
    private String logId = "J-E.0.0.0.0";
    /**
     * CRM为CRM对象apiName，ERP为真实对象apiName
     * 新加类型支持按此作列表页查看
     */
    private String realObjApiName;
    /**
     * sourceData里面的apiName
     */
    private String sourceObjApiName;

    /**
     * 集成流Id
     * 新加类型支持按此作列表页查看
     */
    private String streamId;
    /**
     * 对象数据的id
     */
    private String dataId;
    /**
     * erp的dataCenterId
     */
    private String dataCenterId;
    /**
     * erp2crm
     */
    private Boolean erp2crm;

    /**
     * 失败重试
     * @param logId
     */
    private Boolean needRetry=false;

    public void setLogId(String logId) {
        if (logId != null) {
            this.logId = logId;
        }
    }
}
