package com.fxiaoke.open.erpsyncdata.preprocess.data;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/6/10
 */
@Data
@AllArgsConstructor
@Builder
public class TaskNumInfo {
    private final String taskNum;
    /**
     * 数据优先级
     */
    private final Integer priority;
    public static TaskNumInfo generateInfo(String taskNum,Integer priority){
       return TaskNumInfo.builder().taskNum(taskNum).priority(priority).build();
    }
}
