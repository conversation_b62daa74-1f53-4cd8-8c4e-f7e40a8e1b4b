package com.fxiaoke.open.erpsyncdata.preprocess.factory;

import com.fxiaoke.open.erpsyncdata.preprocess.service.OuterService;
import com.google.common.collect.Maps;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

@Component
public class OuterServiceFactory implements ApplicationContextAware {
    private ApplicationContext applicationContext;
    private Map<Integer, OuterService> outerServiceMap = Maps.newHashMap();


    @PostConstruct
    private void init() {
        Map<String, ? extends OuterService> map = applicationContext.getBeansOfType(OuterService.class);
        outerServiceMap = map.values().stream().collect(Collectors.toMap(OuterService::getTenantType, val -> val));
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
    public OuterService get(Integer tenantType) {
        return outerServiceMap.get(tenantType);
    }

}
