package com.fxiaoke.open.erpsyncdata.preprocess.model;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.DbTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
@ApiModel
public class DBProxyConnectParam extends BaseLangConnectParam {
    private static final long serialVersionUID = 5918765114938725275L;

    /**
     * 基础路径
     */
    @ApiModelProperty("基础路径")
    private String baseUrl;

    @ApiModelProperty("代理服务器账号")
    private String userName;

    @ApiModelProperty("代理服务器密码")
    private String password;

    @ApiModelProperty("push数据的对象名称")
    private List<String> pushDataApiNames;

    /**
     * 版本，默认1.0，格式：1.1.20230721,最后一部分为日期yyyyMMdd
     * 使用hutool的VersionComparator比较
     */
    @ApiModelProperty("代理服务版本")
    private String version = "1.0";
    @ApiModelProperty("db名称")
    private String dbName = "default";
    @ApiModelProperty("db类型")
    private String dbType = DbTypeEnum.ANSI.name();


    public String getBaseUrl() {
        //增加容错
        if (baseUrl != null && baseUrl.endsWith("/")) {
            baseUrl = baseUrl.substring(0, baseUrl.length() - 1);
        }
        return baseUrl;
    }

    @Override
    public String getSystemName() {
        return "DB-" + dbType;
    }
}
