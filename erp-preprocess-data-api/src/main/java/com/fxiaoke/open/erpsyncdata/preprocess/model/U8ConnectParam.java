package com.fxiaoke.open.erpsyncdata.preprocess.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/19
 */
@Data
@ApiModel
public class U8ConnectParam extends BaseLangConnectParam {

    private static final long serialVersionUID = -3892271931165465013L;
    /**
     * 基础路径
     */
    @ApiModelProperty("基础路径")
    private String baseUrl;

    @ApiModelProperty("调用方")
    private String fromAccount;

    @ApiModelProperty("提供方")
    private String toAccount;

    @ApiModelProperty("应用编码")
    private String appKey;

    @ApiModelProperty("密钥")
    private String appSecret;

    @ApiModelProperty("数据库")
    private Integer ds_sequence;

    @ApiModelProperty("获取header的脚本")
    private HeaderScript headerScript;

    /**
     * 获取header的函数
     */
    @ApiModelProperty(value = "获取header的函数",example = "return [:]")
    private String headerFunctionName;

    @ApiModelProperty("push数据的对象名称")
    private List<String> pushDataApiNames;

}
