package com.fxiaoke.open.erpsyncdata.preprocess.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.util.Date;

/**
 * 集成流统计信息，聚合数据
 *
 * <AUTHOR> (^_−)☆
 */
@Data
@Builder
@FieldNameConstants
@NoArgsConstructor
@AllArgsConstructor
public class UpGroupStreamStat {
    private Integer streamCount = 0;
    private Integer enableStreamCount = 0;


    /**
     * 下游企业数量，聚合时返回值，不实际存储
     */
    private Integer downstreamCount = 0;

    /**
     * 告警中企业数量
     */
    private Integer alertingDownstreamCount = 0;

    /**
     * 失败企业数量
     */
    private Integer failedDownstreamCount = 0;

    private Long lastStatTime = 0L;
}
