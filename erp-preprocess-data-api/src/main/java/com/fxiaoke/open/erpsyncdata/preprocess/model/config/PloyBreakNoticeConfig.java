package com.fxiaoke.open.erpsyncdata.preprocess.model.config;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 熔断配置
 * <AUTHOR> (^_−)☆
 * @date 2022/8/5
 */
@Getter
@Setter
@ToString(callSuper = true)
public class PloyBreakNoticeConfig extends NoticeConfig implements Serializable {

    /**
     * 失败增量熔断阈值，单位：条
     */
    private Integer failedIncrementBreakThresholds;

    /**
     * getByIds的熔断接口阈值
     */
    private Long getByIdsFailCount;
}
