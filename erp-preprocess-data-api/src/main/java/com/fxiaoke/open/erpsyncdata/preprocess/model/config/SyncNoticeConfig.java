package com.fxiaoke.open.erpsyncdata.preprocess.model.config;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/8/17
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SyncNoticeConfig extends NoticeConfig implements Serializable {

    /**
     * 状态，1 开启  2 关闭
     */
    @ApiModelProperty("状态，1 开启  2 关闭")
    private Integer status;
    /**
     * 通知人类型，1 固定人  2 数据负责人
     */
    @ApiModelProperty("通知人类型，1 固定人  2 数据负责人")
    private Integer type;
}
