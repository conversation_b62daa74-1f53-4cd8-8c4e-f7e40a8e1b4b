package com.fxiaoke.open.erpsyncdata.preprocess.model.connector;

import com.fxiaoke.open.erpsyncdata.i18n.I18nBase;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorHandlerType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;

/**
 * <AUTHOR> (^_−)☆
 */
public interface Connector extends I18nBase {

    String getDefaultName();

    String getNameI18nKey();

    ErpChannelEnum getChannel();

    String getKey();

    default String getI18nName() {
        return getNameByTraceLocale();
    }

    String getModuleCode();

    Integer getConnectorId();

    default String getIconUrl(){
        return null;
    }

    @Override
    default String getI18nKey(){
        return getNameI18nKey();
    }

    @Override
    default String getI18nValue(){
        return getDefaultName();
    }

    ConnectorHandlerType getConnectorHandlerType();
}
