package com.fxiaoke.open.erpsyncdata.preprocess.model.node;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeNameEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR> (^_−)☆
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
public class NodeStatRecord {
    private String tenantId;
    private String dcId;
    private String streamId;
    private Integer sourceTenantType;
    private DataNodeNameEnum dataNodeName;

    private long startTime = System.currentTimeMillis();

    /**
     * 次数，不代表数据量
     */
    private AtomicInteger totalCount = new AtomicInteger(0);
    /**
     * 总耗时
     */
    private AtomicLong totalCost = new AtomicLong(0);

    public void add(long cost) {
        totalCount.incrementAndGet();
        totalCost.addAndGet(cost);
    }
}
