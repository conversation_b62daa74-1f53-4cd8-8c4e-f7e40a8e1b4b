package com.fxiaoke.open.erpsyncdata.preprocess.model.node;

import com.fxiaoke.open.erpsyncdata.common.constant.NodeDataStatus;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> (^_−)☆
 */
@Getter
@Setter
@Accessors(chain = true)
public class NodeStatusRecord {
    /**
     * 按主对象id聚合
     */
    private String mainDataId;
    private NodeDataStatus status;
    //主从计数，在进入临时库时，统一为1，因为还无法感知多少条从对象数据
    private int count;
    /**
     * 每分钟限制，企业级别
     */
    private long limitTpm;
}
