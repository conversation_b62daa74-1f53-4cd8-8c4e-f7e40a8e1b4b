package com.fxiaoke.open.erpsyncdata.preprocess.result;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.SimpleEmployeeMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 10:35 2020/8/18
 * @Desc:
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
@ToString(callSuper = true)
public class EmployeeMappingResult extends SimpleEmployeeMapping implements Serializable {
    @ApiModelProperty("渠道，ERP_K3CLOUD,ERP_SAP,ERP_U8")
    public ErpChannelEnum channel; //渠道，k3,sap,u8，其他
    @ApiModelProperty("erp员工手机号")
    public String erpEmployeePhone ;
    @ApiModelProperty("erp员工账号")
    public String erpEmployeeAccount;
    @ApiModelProperty("fs职员状态")
    private Integer fsEmployeeStatus;
    @ApiModelProperty("fs职员电话")
    private String fsEmployeePhone;
    @ApiModelProperty("ERP系统用户帐号")
    private String erpUserAccount;
    @ApiModelProperty("ERP系统用户ID")
    private String erpUserId;
    @ApiModelProperty("ERP系统用户帐号描述")
    private String erpUserAccountMsg;
}
