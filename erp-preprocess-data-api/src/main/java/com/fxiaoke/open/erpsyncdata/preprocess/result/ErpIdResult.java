package com.fxiaoke.open.erpsyncdata.preprocess.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/26
 */
@Data
@ApiModel(value = "erp标准id结果",description = "新建或保存接口返回的erp数据平台标准id结果")
public class ErpIdResult implements Serializable {

    private static final long serialVersionUID = 4479110907108687306L;
    @ApiModelProperty("主对象id")
    private String masterDataId;
    @ApiModelProperty("主对象主属性")
    private String masterDataName;
    @ApiModelProperty("从对象dataId,key:对象apiName，value:数据id列表")
    private Map<String, List<String>> detailDataIds = new HashMap<>(0);
    @ApiModelProperty("主对象返回数据信息")
    private Map<String, Object> masterReturnData;
    @ApiModelProperty("明细对象返回数据信息：key是对象apiName，value:（key是明细id，value是返回明细对象数据）")
    private Map<String, Map<String, Map<String, Object>>> detailReturnData;
}
