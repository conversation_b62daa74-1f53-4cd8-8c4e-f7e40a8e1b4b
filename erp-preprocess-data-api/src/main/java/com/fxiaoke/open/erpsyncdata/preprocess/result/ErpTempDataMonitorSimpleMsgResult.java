package com.fxiaoke.open.erpsyncdata.preprocess.result;


import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 16:35 2021/8/11
 * @Desc:
 */
@Data
@ApiModel
public class ErpTempDataMonitorSimpleMsgResult implements Serializable {
    @ApiModelProperty("策略明细id")
    public String ployDetailId;
    @ApiModelProperty("源对象")
    public ObjApiNameAndName sourceObj;
    @ApiModelProperty("所有erp对象")
    public List<ObjApiNameAndName> allErpObj= Lists.newArrayList();
    @ApiModelProperty("所有crm对象")
    public List<ObjApiNameAndName> allCrmObj= Lists.newArrayList();;
    @ApiModelProperty("crm主对象")
    public ObjApiNameAndName crmMasterObj;
    @ApiModelProperty("erp主对象")
    public ObjApiNameAndName erpMasterObj;
    @ApiModelProperty("erp主对象")
    public ObjApiNameAndName erpRealObj;

    @ApiModelProperty("备注")
    public String remark;

    @Data
    @ApiModel
    public static class ObjApiNameAndName implements Serializable {
        @ApiModelProperty("对象名称")
        public String objName;
        @ApiModelProperty("对象apiName")
        public String objApiName;
    }
}
