package com.fxiaoke.open.erpsyncdata.preprocess.result;

import com.fxiaoke.open.erpsyncdata.preprocess.model.ErpObjTreeNode;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> (^_−)☆
 */
@Data
@Accessors(chain = true)
public class InitDcResult {
    private List<ErpObjTreeNode> presetObjs;

    private Map<String, Result<?>> presetStreams;
}
