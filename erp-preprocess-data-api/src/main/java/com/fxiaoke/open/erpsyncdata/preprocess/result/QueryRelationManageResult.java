package com.fxiaoke.open.erpsyncdata.preprocess.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/3/2 15:32
 * @Version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel
@Builder
public class QueryRelationManageResult implements Serializable {
    @ApiModelProperty("分组id")
    public String id;
    @ApiModelProperty("分组名称")
    public String name;
    @ApiModelProperty("模板企业Id")
    public String templateId;
    @ApiModelProperty("模板企业名称")
    public String templateName;
    @ApiModelProperty("连接器id")
    public String dataCenterId;
    @ApiModelProperty("连接器名称")
    public String dataCenterName;

    /**
     * 创建中的时候,下面三个给null
     */
    @ApiModelProperty("代管企业数量")
    private Integer downstreamCount;
    @ApiModelProperty("数据异常企业数量")
    private Integer errorCount;
    @ApiModelProperty("告警中企业数量")
    private Integer warningCount;
    @ApiModelProperty("集成流数量")
    private Integer streamCount;
    @ApiModelProperty("集成流数量")
    private Integer enableStreamCount;
    @ApiModelProperty("最近统计时间")
    private Long lastStatTime;
}
