package com.fxiaoke.open.erpsyncdata.preprocess.result.base;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/22
 */
@Getter
@Setter
@ToString
public abstract class BaseResult {
    @ApiModelProperty("状态码")
    protected String errCode;
    @ApiModelProperty("状态描述")
    protected String errMsg;
    @ApiModelProperty("国际化词条key")
    @JsonIgnore
    @JSONField(serialize = false)
    protected String i18nKey;
    @ApiModelProperty("国际化需要的附加信息")
    @JsonIgnore
    @JSONField(serialize = false)
    protected List<String> i18nExtra;
    @ApiModelProperty("进程信息")
    protected String traceMsg;

    public boolean isSuccess() {
        return ResultCodeEnum.SUCCESS.getErrCode().equals(this.errCode);
    }

    public BaseResult copyFrom(BaseResult baseResult) {
        this.errCode = baseResult.errCode;
        this.errMsg = baseResult.errMsg;
        this.i18nKey = baseResult.i18nKey;
        this.i18nExtra = baseResult.i18nExtra;
        this.traceMsg = baseResult.traceMsg;
        return this;
    }
}
