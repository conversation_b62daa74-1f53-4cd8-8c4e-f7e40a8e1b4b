package com.fxiaoke.open.erpsyncdata.preprocess.service;

import com.fxiaoke.open.erpsyncdata.preprocess.annotation.ContextEi;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.BatchSendEventDataArg;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.model.AutoBindEmployeeMapping;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ObjectDataSyncMsg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;

import java.util.List;

public interface AllModelDubboService {
    /**
     * 发送数据到分发框架
     * @param arg
     * @return
     */
    @Deprecated
    Result2<Void> batchSendEventData2DispatcherMq(@ContextEi("?.getEventDatas()?.get(0)?.getSourceData()?.getTenantId()") BatchSendEventDataArg arg);

    /**
     * 发送数据到分发框架
     * @param syncDataContextEvents
     * @return
     */
    Result2<Void> batchSendEventData2DispatcherMqByContext(@ContextEi("?.get(0).getSourceData()?.getTenantId()") List<SyncDataContextEvent> syncDataContextEvents);

    Result2<ObjectDataSyncMsg> syncDataMain(@ContextEi("?.getSourceData()?.getTenantId()") SyncDataContextEvent eventData);

    /**
     * SyncDepartmentOrPersonnelService 没有dubbo接口,task无法调用,在这里做下兼容
     */
    void autoBindEmployeeMapping(@ContextEi("?.getTenantId()") AutoBindEmployeeMapping.Arg arg);
}
