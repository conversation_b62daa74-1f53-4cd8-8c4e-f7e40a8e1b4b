package com.fxiaoke.open.erpsyncdata.preprocess.service;

import com.fxiaoke.open.erpsyncdata.preprocess.annotation.ContextEi;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.AttachmentsArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ConvertFile;
import com.fxiaoke.open.erpsyncdata.preprocess.model.CrmFileModel;
import com.fxiaoke.open.erpsyncdata.preprocess.model.OutFile;
import com.fxiaoke.open.erpsyncdata.preprocess.result.AttachmentsResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 15:06 2023/4/19
 * @Desc: 附件
 */
public interface AttachmentsService {
    Result2<AttachmentsResult> downAndUploadAttachments(@ContextEi("?.getTenantId()") AttachmentsArg attachmentsArg);

    Result<List<OutFile>> convertCrmFile2Out(@ContextEi("?.getTenantId()") ConvertFile.Crm2OutArg arg);

    Result<List<CrmFileModel>> convertOutFile2Crm(@ContextEi("?.getTenantId()") ConvertFile.Out2CrmArg arg);
}
