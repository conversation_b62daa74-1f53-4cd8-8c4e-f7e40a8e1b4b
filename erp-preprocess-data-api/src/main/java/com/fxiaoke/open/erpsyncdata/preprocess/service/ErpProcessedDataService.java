package com.fxiaoke.open.erpsyncdata.preprocess.service;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjDataResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 15:26 2021/4/20
 * @Desc:
 */
public interface ErpProcessedDataService {


    Result<List<SyncDataContextEvent>> getErpInvalidDetailList(String tenantId, String dataCenterId, List<SyncDataContextEvent> erpDataList);

    Result<Boolean> needCompareDetail(String tenantId, String dcId, ErpChannelEnum channel, String splitObjApiName);
}
