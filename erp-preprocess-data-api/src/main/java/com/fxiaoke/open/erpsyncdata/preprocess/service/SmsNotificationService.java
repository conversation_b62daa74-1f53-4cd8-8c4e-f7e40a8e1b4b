package com.fxiaoke.open.erpsyncdata.preprocess.service;

import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.otherrestapi.marketingsms.arg.CheckSmsStatusArg;
import com.fxiaoke.otherrestapi.marketingsms.arg.SendSmsArg;
import com.fxiaoke.otherrestapi.marketingsms.data.SendSmsInfoData;

import java.util.List;

/**
 * 短信通知服务
 * <AUTHOR>
 * @Date: 10:27 2024/10/08
 * @Desc:
 */

public interface SmsNotificationService {

    /**
     * 发送短信
     * 文本消息
     *
     * @param arg
     * @return
     */
    Result<SendSmsInfoData> sendSmsNotice(String tenantId, SendSmsArg arg);

    Result<Boolean> checkSmsStatus(String tenantId, CheckSmsStatusArg arg);

    Result<SendSmsInfoData> sendSmsNotice(String tenantId, List<Integer> userIdList,List<String> roleIdList,String templateContent);
}
