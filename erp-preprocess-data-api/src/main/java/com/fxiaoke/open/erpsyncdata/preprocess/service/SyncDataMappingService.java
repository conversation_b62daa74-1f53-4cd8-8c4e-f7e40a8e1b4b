package com.fxiaoke.open.erpsyncdata.preprocess.service;

import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataMappingData;
import java.util.List;

public interface SyncDataMappingService {
    Result2<SyncDataMappingData> getSyncDataMapping(String tenantId, String sourceTenantId, String sourceObjectApiName, String sourceObjectId, String destTenantId, String destObjectApiName);

    /**
     * 判断是否存在同步对象数据映射
     *
     * @param tenantId
     * @param sourceTenantId 源企业id
     * @param sourceApiName 源企业对象apiName
     * @param sourceDataId 源企业数据id
     * @param destTenantId 目标企业id
     * @param destApiName 目标企业对象apiName
     * @return
     */
    Result2<Boolean> existByTwoWay(String tenantId, String sourceTenantId, String sourceApiName, String sourceDataId, String destTenantId, String destApiName);

    /**
     * 注：企业不一致会有问题
     */
    Result2<Void> updateDataIdByMergeInfo(String tenantId, String objectApiName, List<String> oldMergeObjectIds, String newMergeObjectId);


    Result2<List<SyncDataMappingData>> getDataByMasterId(String tenantId, String sourceTenantId, String sourceApiName,String destApiName, String masterId);

    Result2<SyncDataMappingData> getSyncDataMappingByDest(String tenantId, String sourceTenantId, String sourceObjectApiName, String destObjectId, String destTenantId, String destObjectApiName);

    Result2<List<SyncDataMappingData>> getMappingByDestObjApiNameAndId(String tenantId, String destObjectApiName, String destObjectId);

}
