package com.fxiaoke.open.erpsyncdata.preprocess.service;

import com.fxiaoke.open.erpsyncdata.preprocess.data.DoWriteResultData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/11/24
 */
public interface SyncStockService {

    SyncDataContextEvent beforeWriteBatchStock2Crm(SyncDataContextEvent message);
    /**
     * 写CRM库存数据前置动作
     * @param message
     * @return
     */
    void afterWriteBatchStock2Crm(SyncDataContextEvent message);
}
