package com.fxiaoke.open.erpsyncdata.preprocess.service;

import cn.hutool.core.lang.Dict;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.CopyTenantConfigurationArg;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ErpTenantConfiguration;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 17:43 2020/12/22
 * @Desc:
 */
public interface TenantConfigurationService {
    /**
     * 查询配置
     * @param type 配置的类型
     * @return
     */
    Result<ErpTenantConfiguration> queryConfig(String tenantId,String dataCenterId,String channel,String type);

    /**
     * 查询全局通用配置
     * @param type 配置的类型
     * @return
     */
    Result<ErpTenantConfiguration> queryConfig(String type);

    /**
     * 更新配置
     * @param type 配置的类型
     * @param configuration 配置的值
     * @return
     */
    Result<Void> updateConfig(String tenantId,String dataCenterId,String channel,String type,String configuration);

    /**
     * 更新全局通用配置
     * @param type 配置的类型
     * @param configuration 配置的值
     * @return
     */
    Result<Void> updateConfig(String type,String configuration);

    Result<Dict> getAllConfigByKey(String tenantId, String dataCenterId);
}
