package com.fxiaoke.open.erpsyncdata.preprocess.constant

import spock.lang.Specification
import spock.lang.Unroll

/**
 *
 * <AUTHOR> (^_−)☆
 */
class ErpFieldTypeEnumTest extends Specification {

    @Unroll("default value for #fieldType,#fieldValue,#channel,#crm2Erp")
    def "DefaultValue"(ErpFieldTypeEnum fieldType, String fieldValue, boolean crm2Erp, ErpChannelEnum channel, String value,boolean needConvert) {
        expect:
        value == fieldType.defaultValue(fieldValue, crm2Erp, channel, needConvert)

        where:
        fieldType                        | fieldValue | crm2Erp | channel                         | value|needConvert
        ErpFieldTypeEnum.employee        | null       | false   | ErpChannelEnum.ERP_K3CLOUD      | "-10000"|true
        ErpFieldTypeEnum.employee        | null       | false   | ErpChannelEnum.STANDARD_CHANNEL | "-10000"|false
        ErpFieldTypeEnum.employee        | "1000"     | false   | ErpChannelEnum.ERP_K3CLOUD      | "-10000"|false
        //其他渠道，返回原值
        ErpFieldTypeEnum.employee        | "1000"     | false   | ErpChannelEnum.STANDARD_CHANNEL | "1000"|false

        ErpFieldTypeEnum.employee_many   | null       | false   | ErpChannelEnum.ERP_K3CLOUD      | "-10000"|false
        ErpFieldTypeEnum.employee_many   | null       | false   | ErpChannelEnum.STANDARD_CHANNEL | "-10000"|false
        ErpFieldTypeEnum.employee_many   | "1000"     | false   | ErpChannelEnum.ERP_K3CLOUD      | "-10000"|false
        //其他渠道，返回原值
        ErpFieldTypeEnum.employee_many   | "1000"     | false   | ErpChannelEnum.STANDARD_CHANNEL | "1000"|false

        ErpFieldTypeEnum.department      | null       | false   | ErpChannelEnum.ERP_K3CLOUD      | "999999"|false
        ErpFieldTypeEnum.department      | null       | false   | ErpChannelEnum.STANDARD_CHANNEL | "999999"|false
        ErpFieldTypeEnum.department      | "1000"     | false   | ErpChannelEnum.ERP_K3CLOUD      | "999999"|false
        //其他渠道，返回原值
        ErpFieldTypeEnum.department      | "1000"     | false   | ErpChannelEnum.STANDARD_CHANNEL | "1000"|false

        ErpFieldTypeEnum.department_many | null       | false   | ErpChannelEnum.ERP_K3CLOUD      | "999999"|false
        ErpFieldTypeEnum.department_many | null       | false   | ErpChannelEnum.STANDARD_CHANNEL | "999999"|false
        ErpFieldTypeEnum.department_many | "1000"     | false   | ErpChannelEnum.ERP_K3CLOUD      | "999999"|false
        //其他渠道，返回原值
        ErpFieldTypeEnum.department_many | "1000"     | false   | ErpChannelEnum.STANDARD_CHANNEL | "1000"|false


        ErpFieldTypeEnum.text            | null       | false   | ErpChannelEnum.ERP_K3CLOUD      | fieldValue|false
        ErpFieldTypeEnum.text            | null       | false   | ErpChannelEnum.STANDARD_CHANNEL | fieldValue|false
        ErpFieldTypeEnum.text            | "1000"     | false   | ErpChannelEnum.ERP_K3CLOUD      | fieldValue|false
        ErpFieldTypeEnum.text            | "1000"     | false   | ErpChannelEnum.STANDARD_CHANNEL | fieldValue|false

        ErpFieldTypeEnum.text            | null       | true    | ErpChannelEnum.ERP_K3CLOUD      | fieldValue|false
        ErpFieldTypeEnum.text            | null       | true    | ErpChannelEnum.STANDARD_CHANNEL | fieldValue|false
        ErpFieldTypeEnum.text            | "1000"     | true    | ErpChannelEnum.ERP_K3CLOUD      | fieldValue|false
        ErpFieldTypeEnum.text            | "1000"     | true    | ErpChannelEnum.STANDARD_CHANNEL | fieldValue|false
    }
}
