package com.fxiaoke.open.erpsyncdata.preprocess.impl

import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpHistoryDataTaskDao
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncPloyDetailSnapshotDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpHistoryDataTaskEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpHistoryTaskLogDao
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpHistoryDataTaskTypeEnum
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent
import com.fxiaoke.open.erpsyncdata.preprocess.impl.task.IdsErpHistoryTaskServiceImpl
import com.fxiaoke.open.erpsyncdata.preprocess.manager.ProbeErpDataManager
import com.fxiaoke.open.erpsyncdata.preprocess.manager.Send2DispatcherSpeedLimitManager
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpHistoryDataTaskResult
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result
import com.fxiaoke.open.erpsyncdata.preprocess.service.ErpDataPreprocessService
import com.fxiaoke.open.erpsyncdata.preprocess.util.PollingDataSpeedRateLimitManager
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
class IdsErpHistoryTaskServiceImplTest extends Specification {

    def tenantId = "123456"
    IdsErpHistoryTaskServiceImpl service

    def setup() {
        def erpHistoryDataTaskDao = Mock(ErpHistoryDataTaskDao) {
            setTenantId(*_) >> it
            findById(*_) >> new ErpHistoryDataTaskEntity(needStop: false)
            // 其余方法为空实现
        }
        def erpHistoryTaskLogDao = Mock(ErpHistoryTaskLogDao) // do nothing
        def syncLogManager = Mock(SyncLogManager) {
            initLogId(*_) >> "log_id"
        }
        def send2DispatcherSpeedLimitManager = Mock(Send2DispatcherSpeedLimitManager) {
            getNumPerInterval(*_) >> 10L
            needSendDispatcherNum(*_) >> 10L
        }
        def syncPloyDetailSnapshotDao = Mock(SyncPloyDetailSnapshotDao) {
            setTenantId(*_) >> it
            listNewestBySourceTenantIdAndSrouceObjectApiName(*_) >> {
                def entity = new SyncPloyDetailSnapshotEntity(destObjectApiName: "api_name")
                def entity1 = new SyncPloyDetailSnapshotEntity(destObjectApiName: "api_name_1")
                return [entity, entity1]
            }
        }
        def pollingDataSpeedRateLimitManager = Mock(PollingDataSpeedRateLimitManager) // do nothing
        def erpDataPreprocessService = Mock(ErpDataPreprocessService) {
            getReSyncObjDataById(*_) >>> [Result.newSuccess([new SyncDataContextEvent()]), Result.newError(ResultCodeEnum.GET_BY_ID_BREAK.getErrCode(), "第二次遍历到即失败")]
        }
        def probeErpDataManager = Mock(ProbeErpDataManager) // do nothing
        def i18NStringManager = new I18NStringManager()
        ConfigCenterConfig conf = Mock(ConfigCenterConfig) {
            getNO_SEND_DETAIL_EVENT_CRM_OBJ_SET() >> new HashSet()
        }
        service = new IdsErpHistoryTaskServiceImpl(
                // 父类的private字段不可见，会注入失败
//                erpHistoryTaskLogDao: erpHistoryTaskLogDao,
                erpHistoryDataTaskDao: erpHistoryDataTaskDao,
                syncLogManager: syncLogManager, send2DispatcherSpeedLimitManager: send2DispatcherSpeedLimitManager,
                syncPloyDetailSnapshotDao: syncPloyDetailSnapshotDao, pollingDataSpeedRateLimitManager: pollingDataSpeedRateLimitManager,
                erpDataPreprocessService: erpDataPreprocessService, probeErpDataManager: probeErpDataManager,
                i18NStringManager: i18NStringManager, configCenterConfig: conf)
    }

    def "test taskType"() {
        expect:
        ErpHistoryDataTaskTypeEnum.TYPE_IDS == service.taskType()
    }

    def "test checkParamsError - #name"() {
        given:
        ErpHistoryDataTaskResult task = new ErpHistoryDataTaskResult(dataIds: list)
        expect:
        res == service.checkParamsError(task)
        where:
        name      | list          || res
        "空列表"   | []            || true
        "正常长度" | ["0", "1"]     || false
    }

    def "test checkTaskValid - #name"() {
        given:
        ErpHistoryDataTaskEntity task = new ErpHistoryDataTaskEntity(dataIds: ids)
        expect:
        res == service.checkTaskValid(task)
        where:
        name      | ids     || res
        "空任务"   | ""      || true
        "正常任务" | "test"   || false
    }

    def "test editTaskSuccess"() {
        given:
        def task = new ErpHistoryDataTaskResult()
        def entity = new ErpHistoryDataTaskEntity()
        when:
        service.editTaskSuccess(tenantId, task, entity)
        then:
        // 因为父类的字段无法正常注入，会报异常
        thrown(NullPointerException)
    }

    def "test saveTaskSuccess"() {
        given:
        def task = new ErpHistoryDataTaskResult()
        def entity = new ErpHistoryDataTaskEntity()
        when:
        service.saveTaskSuccess(tenantId, task, entity)
        then:
        // 因为父类的字段无法正常注入，会报异常
        thrown(NullPointerException)
    }

    def "test doTask"() {
        given:
        def ids = ["00", "01"]
        ErpHistoryDataTaskEntity entity = new ErpHistoryDataTaskEntity(realObjApiName: "test_obj_api_name", dataIds: JacksonUtil.toJson(ids),
                )
        when:
        def res = service.doTask(tenantId, entity)
        then:
        res.isSuccess()
    }
}
