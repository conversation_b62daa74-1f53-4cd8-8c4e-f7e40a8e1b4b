package com.fxiaoke.open.erpsyncdata.preprocess.manager

import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent
import spock.lang.Specification

class ErpDataPreprocessManagerTest extends Specification {

    ErpDataPreprocessManager manager
    def tenantId = "123456"
    def apiName = "api_name"

    def "test checkListResult"() {
        given:

        expect:
        true
    }

    def "test tryRemoveNotNeedSync"() {
        given:
        // TODO
        manager = new ErpDataPreprocessManager()
        expect:
        manager.tryRemoveNotNeedSync(null, tenantId, "", ErpChannelEnum.ALL, "")
    }

    def "test tryRemoveNotNeedSync - 异常"() {
        given:
        manager = new ErpDataPreprocessManager()
        expect:
        manager.tryRemoveNotNeedSync(null, tenantId, "", null, "")
    }

    def "test removeOutOfLimitDetail"() {
        given:
        TenantConfigurationManager tenantConfigurationManager = Mock(TenantConfigurationManager) {
            inGroupWhiteList(*_) >> true
        }
        ConfigCenterConfig conf = Mock(ConfigCenterConfig) {
            getSPECIAL_LIMIT_DETAIL_SIZE(*_) >> ["api_name": 1]
        }
        manager = new ErpDataPreprocessManager(tenantConfigurationManager: tenantConfigurationManager, configCenterConfig: conf)
        and:
        ErpIdArg arg = new ErpIdArg(tenantId: tenantId)
        def obj0 = new ObjectData()
        def obj1 = new ObjectData()
        def obj2 = new ObjectData()
        def obj3 = new ObjectData()
        SyncDataContextEvent event = new SyncDataContextEvent(detailData: ["api_name": [obj0, obj1, obj2, obj3]])
        expect:
        manager.removeOutOfLimitDetail(apiName, arg, event)
        event.getDetailData().get(apiName).size() == 1
    }
}
