package com.fxiaoke.open.erpsyncdata.admin.arg;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@ApiModel
public interface BatchExportInterfaceFormat {
    @Data
    class Arg implements Serializable {
        @ApiModelProperty("渠道")
        private ErpChannelEnum channel;
        @ApiModelProperty("中间对象apiName")
        private List<String> splitObjectApiName;
        /**
         * @see ErpObjInterfaceUrlEnum
         */
        @ApiModelProperty("导出的Api,没有值代表所有;见接口的apiType")
        private List<String> apiTypes;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result implements Serializable {
        private String downloadUrl;

    }
}
