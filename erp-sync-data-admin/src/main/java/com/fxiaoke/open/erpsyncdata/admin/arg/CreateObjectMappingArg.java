package com.fxiaoke.open.erpsyncdata.admin.arg;


import com.fxiaoke.open.erpsyncdata.admin.data.SyncRulesWebData;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CreateObjectMappingArg implements Serializable {
    @ApiModelProperty("集成流id")
    private String id;
    @ApiModelProperty("集成流名称")
    private String integrationStreamName;
    @ApiModelProperty("源数据中心id")
    private String sourceDataCenterId;
    @ApiModelProperty("目标数据中心id")
    private String destDataCenterId;
    @ApiModelProperty("同步规则")
    private SyncRulesWebData syncRules;
    @ApiModelProperty("源主对象")
    private String sourceObjectApiName;
    @ApiModelProperty("目标主对象")
    private String destObjectApiName;
    @ApiModelProperty("从对象映射")
    private List<ObjectMapping> detailObjectMappings = Lists.newArrayList();
    @ApiModelProperty("备注")
    private String remark;

    @Data
    @ApiModel
    public static class ObjectMapping implements Serializable {
        @ApiModelProperty("源企业对象apiName")
        private String sourceObjectApiName;
        @ApiModelProperty("目标企业对象apiName")
        private String destObjectApiName;
    }
}