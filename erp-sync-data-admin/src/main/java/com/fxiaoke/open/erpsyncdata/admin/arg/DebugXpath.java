package com.fxiaoke.open.erpsyncdata.admin.arg;

import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.MapStringStringData;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

@ApiModel
public interface DebugXpath {
    @Data
    class Arg implements Serializable {
        /**
         * 真实对象ApiName
         */
        private String realObjApiName;
        /**
         * 接口类型
         */
        private ErpObjInterfaceUrlEnum type;
        /**
         * xpath
         */
        private MapStringStringData data;
        /**
         * 响应示例
         */
        private String responseDemo;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result implements Serializable {
        /**
         * xpath对应的转换值
         */
        private Map<String, Object> data;
    }
}
