package com.fxiaoke.open.erpsyncdata.admin.arg;

import com.fxiaoke.open.erpsyncdata.admin.result.ApiFormatResult2;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@ApiModel
public interface ExportInterfaceFormat {
    @Data
    class Arg implements Serializable {
        @ApiModelProperty("渠道")
        private ErpChannelEnum channel;
        @ApiModelProperty("中间对象apiName")
        private String splitObjectApiName;
        /**
         * @see ErpObjInterfaceUrlEnum
         */
        @ApiModelProperty("导出的Api,没有值代表所有;见接口的apiType")
        public List<String> apiTypes;
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result implements Serializable {
        private String downloadUrl;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class MdParams implements Serializable {
        private String apiName;
        private List<ApiFormatResult2> apis;
    }
}
