package com.fxiaoke.open.erpsyncdata.admin.arg;

import com.google.common.collect.Lists;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2020/7/30.
 */
@Data
public class MigrateDDSSyncDatasArg extends MigrateEasArg implements Serializable {
    private Integer pageNum;
    private Integer pageSize;
    private List<String> ddsObjectApiNames = Lists.newArrayList("ProductObj" , "SPUObj" , "AccountObj" , "other");
    private Long timestamp;
    private Boolean migrateOnce = true;
    private String id;
    /**
     * 1-成功  2-失败
     */
    private List<Integer> types = Lists.newArrayList(1, 2);
}
