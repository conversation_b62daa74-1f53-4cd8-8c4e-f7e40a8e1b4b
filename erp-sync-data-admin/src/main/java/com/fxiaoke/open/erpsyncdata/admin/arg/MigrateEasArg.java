package com.fxiaoke.open.erpsyncdata.admin.arg;

import lombok.Data;
import org.apache.commons.lang3.BooleanUtils;

import java.io.Serializable;
import java.util.List;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2020/7/6.
 */
@Data
public class MigrateEasArg implements Serializable {
    private List<String> eas;
    private List<String> tenantIds;
    private Boolean initAll = false;

    public boolean verify() {
        if ((BooleanUtils.isNotTrue(initAll) && ((eas != null && !eas.isEmpty()) || (tenantIds != null && !tenantIds.isEmpty()))) || BooleanUtils.isTrue(initAll)) {
            return true;
        }
        return false;
    }
}
