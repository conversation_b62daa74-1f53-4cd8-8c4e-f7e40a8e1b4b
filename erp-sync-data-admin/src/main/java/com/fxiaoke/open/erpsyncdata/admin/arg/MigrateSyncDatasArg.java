package com.fxiaoke.open.erpsyncdata.admin.arg;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2020/8/18.
 */
@Data
public class MigrateSyncDatasArg implements Serializable {
    private String tenantId;
    private String apiName;
    private String ployDetailId;
    /**
     * 1采集  2下发
     */
    private Integer type;
    @ApiModelProperty("启用状态筛选 1启用 2停用")
    private Integer status;
    @ApiModelProperty("搜索内容")
    private String searchText;
    @ApiModelProperty("页码")
    private Integer pageNumber;
    @ApiModelProperty("每页大小")
    private Integer pageSize;
}
