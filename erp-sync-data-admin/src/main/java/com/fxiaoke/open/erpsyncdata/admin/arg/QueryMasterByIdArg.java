package com.fxiaoke.open.erpsyncdata.admin.arg;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class QueryMasterByIdArg implements Serializable {
    @ApiModelProperty("企业ei")
    private String tenantId;
    @ApiModelProperty("源中间对象apiName")
    private String sourceObjApiName;
    @ApiModelProperty("数据id")
    private String dataId;
    @ApiModelProperty("是否包含明细数据")
    private boolean includeDetail = true;
    /** 集成流ID */
    @ApiModelProperty("集成流ID")
    private String integrationStreamId;
    /** 源数据的数据事件类型 1、新增 2、修改 3、作废  */
    @ApiModelProperty("源数据的数据事件类型")
    private Integer sourceEventType = 2;
    @ApiModelProperty("是否触发数据同步")
    private boolean triggerSync;
}
