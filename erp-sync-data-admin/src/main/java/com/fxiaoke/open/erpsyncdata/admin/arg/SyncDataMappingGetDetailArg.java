package com.fxiaoke.open.erpsyncdata.admin.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel
public class SyncDataMappingGetDetailArg implements Serializable {
    @ApiModelProperty("syncDataMappingId， 数据映射表中的id")
    private String id;
    @ApiModelProperty("syncDataId 数据Id")
    private String syncDataId;
}
