package com.fxiaoke.open.erpsyncdata.admin.manager;

import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.PlusTenantConfigManager;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.InetAddress;
import java.net.URL;
import java.util.regex.Pattern;

/**
 * <AUTHOR> (^_−)☆
 */
@Service
@Slf4j
public class UrlCheckManager {
    @Autowired
    private PlusTenantConfigManager plusTenantConfigManager;

    // 私有IP地址范围的正则表达式
    private static final Pattern PRIVATE_IP_PATTERN = Pattern.compile(
            "^10\\..*|^172\\.(1[6-9]|2[0-9]|3[0-1])\\..*|^192\\.168\\..*");


    // 验证URL的合法性
    public void checkUrl(String urlString) {
        if (StrUtil.isBlank(urlString)) {
            return;
        }
        //先检查白名单
        boolean notCheck = plusTenantConfigManager.inWhiteList(urlString, TenantConfigurationTypeEnum.BASE_URL_WHITE_LIST);
        if (notCheck) {
            return;
        }
        String ip;
        try {
            URL url = new URL(urlString);
            String host = url.getHost();
            // 解析主机名到IP地址
            InetAddress address = InetAddress.getByName(host);
            ip = address.getHostAddress();
        } catch (Exception e) {
            log.info("url is invalid,url:{}", urlString);
            throw new ErpSyncDataException(I18NStringEnum.kUrlIsInvalid.getText());
        }
        if (ReUtil.isMatch(PRIVATE_IP_PATTERN, ip)) {
            log.info("url is private,url:{},ip:{}", urlString, ip);
            //私有ip
            throw new ErpSyncDataException(I18NStringEnum.kNotAllowPrivateIp.getText());
        }
    }

}
