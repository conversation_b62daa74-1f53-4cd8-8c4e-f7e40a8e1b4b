package com.fxiaoke.open.erpsyncdata.admin.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.FieldDataMappingExcelVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/6/16 15:17:42
 */
public interface ExportDistrictMapping {
    @Data
    class Arg {
        @ApiModelProperty("国家编码")
        private String country;
        @ApiModelProperty("省编码")
        private String province;
        @ApiModelProperty("城市编码")
        private String city;
        @ApiModelProperty("区县编码")
        private String county;
        @ApiModelProperty("乡镇编码")
        private String town;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        @ApiModelProperty("npath")
        private String path;
        @ApiModelProperty("下载地址")
        private String downloadUrl;
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel("sheet页")
    class DataFlowDiagram extends FieldDataMappingExcelVo {
        /**
         * 完全匹配、部分匹配、未匹配
         */
        @ExcelProperty(value = "erpdss.global.global.s2054", index = 4)
        private String matching;

        public DataFlowDiagram(String fsDataName, String fsDataId, String erpDataName, String erpDataId, String matching) {
            super(fsDataName, fsDataId, erpDataName, erpDataId);
            this.matching = matching;
        }
    }
}
