package com.fxiaoke.open.erpsyncdata.admin.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/27 10:28:40
 */
public interface GetFacebookFormByPage {

    @Data
    class Arg implements Serializable {
        private String pageId;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result implements Serializable {
        private List<IdAndName> formNames;
    }
}
