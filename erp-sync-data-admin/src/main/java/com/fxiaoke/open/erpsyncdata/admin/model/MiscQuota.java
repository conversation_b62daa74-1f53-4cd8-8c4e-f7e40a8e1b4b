package com.fxiaoke.open.erpsyncdata.admin.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 单项配额
 */
@Data
@Accessors(chain = true)
public class MiscQuota {
    /**
     * 标识code，前端转换为中文
     */
    private String code;
    /**
     * 数量
     */
    private Long count = 1L;
    /**
     * 集成流配额
     */
    private Long streamQuota;
    /**
     * 同步数量配额
     */
    private Long mappingQuota;
}
