package com.fxiaoke.open.erpsyncdata.admin.model;

import com.fxiaoke.open.erpsyncdata.admin.data.SyncRulesWebData;
import com.fxiaoke.open.erpsyncdata.admin.result.ObjectMappingResult;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectFieldResult;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;

import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/3/21
 */
@Accessors(chain = true)
public class Template {


    @Data
    @NoArgsConstructor(staticName = "of")
    public static class Filters {
        /**
         * 渠道
         */
        private List<Option> channels;
        /**
         * 场景
         */
        private List<Option> scenes;
        /**
         * 标签
         */
        private List<Option> tags;
    }

    @Getter
    @Setter
    @ToString
    @Accessors(chain = true)
    public static class TemplateInfo {
        /**
         * 模板id，唯一标识
         */
        private String id;
        /**
         * 标题
         */
        private String title;

        /**
         * 描述信息，可能不使用
         */
        private String description;

        /**
         * 标签id,逗号分割
         */
        private List<String> tagIds;

        /**
         * 题头图
         */
        private String headerImg;

        /**
         * 源系统,默认K3先
         */
        private ErpChannelEnum channel = ErpChannelEnum.ERP_K3CLOUD;
        /**
         * 源系统,默认K3先
         */
        private String connectorKey = ErpChannelEnum.ERP_K3CLOUD.name();

        /**
         * 场景id
         */
        private List<String> sceneIds;

        /**
         * 版本
         */
        private String version;

        /**
         * 排序值
         */
        private Integer order = 65536;

        /**
         * 是否启用
         */
        private boolean enable;
        /**
         * 关联的前置条件
         */
        private List<String> preconditionIds;

        /**
         * 详情
         */
        private Detail detail;
    }


    /**
     * 模板详情
     */
    @Data
    @FieldNameConstants
    public static class Detail {
        /**
         * 详情的markdown文本
         */
        private String detailMd;
        /**
         * 对象字段列表，同字段列表页结构
         */
        private List<TemplateErpObjInfo> erpObjInfos;
        /**
         * 对象字段映射，主从都在这，erp对象储存的是真实对象ApiName
         */
        private List<StreamInfo> streamInfos;
    }


    /**
     * 前置条件
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class Precondition extends Option {
        /**
         * 状态,是否合法
         */
        private boolean valid;
    }

    /**
     * 校验对象，主从结构
     */
    @Data
    @Accessors(chain = true)
    public static class TemplateErpObjInfo {
        /**
         * 主对象真实编码
         */
        private String mainRealObjApiName;
        /**
         * 主对象
         */
        private TemplateErpSplitObjInfo main;
        /**
         * 明细对象列表
         */
        private List<TemplateErpSplitObjInfo> details;
        /**
         * 模板无此信息，主对象中间对象apiName，当exist为true时有值。
         */
        private String mainSplitObjApiName;
        /**
         * 模板无此信息，拆分批次，K3基本为1，当exist为true时有值。
         */
        private Integer splitSeq;
    }

    /**
     * 校验字段
     */
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    @Data
    public static class TemplateFieldInfo extends ErpObjectFieldResult {
        /**
         * 主键需要是该字段
         */
        private boolean needBeIdType;
        /**
         * 需要检查类型，暂时只检查查找关联
         */
        private boolean needCheckType;
    }

    /**
     * 校验中间对象
     */
    @Data
    @Accessors(chain = true)
    public static class TemplateErpSplitObjInfo {

        /**
         * 真实对象编码
         */
        private String realObjApiName;
        /**
         * 对象名称
         */
        private String objName;
        /**
         * 字段列表
         */
        public List<TemplateFieldInfo> fields;

        /**
         * 模板无此信息，中间对象apiName，当exist为true时有值。
         */
        private String splitObjApiName;
        /**
         * 模板无此信息，是否存在对象
         */
        private boolean exist = true;

        /**
         * 模板无此信息，缺失的字段
         */
        private List<String> missingFields;
        /**
         * 模板无此信息，是否合法，false时会有错误信息
         */
        private boolean valid = true;
        /**
         * 模板无此信息，不合法内容的提示信息
         */
        private String invalidMessage;

        public TemplateErpSplitObjInfo invalid(String msg) {
            this.valid = false;
            if (invalidMessage == null) {
                invalidMessage = msg;
            } else {
                invalidMessage = invalidMessage + "," + msg;
            }
            return this;
        }

        /**
         * 字段是否完整
         *
         * @return
         */
        public boolean isFieldsComplete() {
            return missingFields == null || missingFields.isEmpty();
        }
    }

    @Getter
    @Setter
    @ToString
    public static class PreCheckArg {
        /**
         * 模板Id
         */
        private String templateId;
    }


    @Getter
    @Setter
    @ToString
    public static class ListStreamsArg {
        /**
         * 模板Id
         */
        private String templateId;
        /**
         * 不同步的对象apiName，真实对象apiName
         */
        private LinkedHashSet<String> notSyncRealObjApiNames = new LinkedHashSet<>();
    }


    @Getter
    @Setter
    @ToString
    @Accessors(chain = true)
    public static class QueryBaseInfoArg {
        /**
         * 渠道，传空查所有
         */
        private ErpChannelEnum channel;
        /**
         * 场景名称
         */
        private String sceneId;

        /**
         * 标签筛选
         */
        private Set<String> tagIds;

        /**
         * 是否包含停用数据
         */
        private boolean includeDisable = false;
    }


    /**
     * 选项
     */
    @Data
    @Accessors(chain = true)
    public static class Option {
        /**
         * id
         */
        private String value;
        /**
         * 名称
         */
        private String label;
        /**
         * 扩展信息
         */
        private String extend;
        /**
         * 用于
         */
        private Integer order = 65536;

        public static Option of(String id, String name) {
            return new Option().setValue(id).setLabel(name);
        }
    }
    /**
     * 选项
     */
    @Data
    @Accessors(chain = true)
    public static class OptionGroup{
        private String label;
        private List<Option> children;
    }

    /**
     * 模板集成流信息
     */
    @Data
    @FieldNameConstants
    public static class StreamInfo {
        /**
         * 集成流名称
         */
        private String streamName;
        /**
         * 源企业类型
         */
        private Integer sourceTenantType;
        /**
         * 主对象映射
         */
        private ObjectMappingResult mainMapping;
        /**
         * 明细映射
         */
        private List<ObjectMappingResult> detailMappings;
        /**
         * 同步规则
         */
        private SyncRulesWebData syncRules;
        /**
         * 集成流id,模板中没有
         */
        private String id;
        /**
         * 是否已经存在,模板中没有
         */
        private boolean exists = false;
    }
}
