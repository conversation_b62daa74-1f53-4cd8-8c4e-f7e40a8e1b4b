package com.fxiaoke.open.erpsyncdata.admin.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023.4.23
 */
@Data
public class EmpFieldDataMappingExcelVo implements Serializable {

    /**
     * 员工名称
     */
    @ExcelProperty(value = "erpdss.global.global.s1037",index = 0)
    private String fsDataName;

    /**
     * 员工名称id
     */
    @ExcelProperty(value = "erpdss.global.global.s1038",index = 1)
    private String fsDataId;

    /**
     * 用户名称
     */
    @ExcelProperty(value = "erpdss.global.global.s1064",index = 2)
    private String erpDataName;

    /**
     * erp用户id
     */
    @ExcelProperty(value = "erpdss.global.global.s1065",index = 3)
    private String erpDataId;

}
