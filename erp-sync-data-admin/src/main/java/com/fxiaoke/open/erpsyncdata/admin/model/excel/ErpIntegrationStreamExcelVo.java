package com.fxiaoke.open.erpsyncdata.admin.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-05-27
 * ERP集成流导入模板
 * 导出也使用此格式
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ErpIntegrationStreamExcelVo {
    /**
     * 对象名称
     */
    @ExcelProperty(value = {"erpdss.global.global.s1051", "erpdss.global.global.s4023"}, index = 0)
    private String crmObjectLabel;

    /**
     * 对象编码
     */
    @ExcelProperty(value = {"erpdss.global.global.s1051", "erpdss.global.global.s4024"}, index = 1)
    private String crmObjectApiName;

    /**
     * 字段名称
     */
    @ExcelProperty(value = {"erpdss.global.global.s1051", "erpdss.global.global.s1090"}, index = 2)
    private String crmFileName;

    /**
     * 查看接口字段编码（CRM对象）
     */
    @ExcelProperty(value = {"erpdss.global.global.s1051", "erpdss.global.global.s4025"}, index = 3)
    private String crmFileApiName;

    /**
     * 第三方对象名称
     */
    @ExcelProperty(value = {"erpdss.global.global.s4022", "erpdss.global.global.s4026"}, index = 4)
    private String thirdPartyObjectLabel;

    /**
     * 第三方对象编码
     */
    @ExcelProperty(value = {"erpdss.global.global.s4022", "erpdss.global.global.s4027"}, index = 5)
    private String thirdPartyObjectApiName;

    /**
     * 第三方字段名称
     */
    @ExcelProperty(value = {"erpdss.global.global.s4022", "erpdss.global.global.s1056"}, index = 6)
    private String thirdPartyFieldLabel;

    /**
     * 第三方字段编码
     */
    @ExcelProperty(value = {"erpdss.global.global.s4022", "erpdss.global.global.s1057"}, index = 7)
    private String thirdPartyFieldApiName;

    /**
     * 第三方字段类型
     */
    @ExcelProperty(value = {"erpdss.global.global.s4022", "erpdss.global.global.s1058"}, index = 8)
    private String thirdPartyFieldType;

    /**
     * 第三方字段是否必填
     */
    @ExcelProperty(value = {"erpdss.global.global.s4022", "erpdss.global.global.s1063"}, index = 9)
    private boolean thirdPartyFieldRequired = false;

    /**
     * 第三方字段扩展信息
     */
    @ExcelProperty(value = {"erpdss.global.global.s4022", "erpdss.global.global.s4028"}, index = 10)
    private String thirdPartyFieldExtendInfo;

}

