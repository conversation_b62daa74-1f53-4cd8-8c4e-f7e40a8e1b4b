package com.fxiaoke.open.erpsyncdata.admin.model.excel;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Data
public class ExportDataMappingExcelVo {

    /**
     * Erp中间对象
     */
    @ExcelProperty("erpdss.global.global.s1047")
    private String erpObjApiName;

    /**
     * 数据状态
     */
    @ExcelProperty("erpdss.global.global.s1048")
    private String statuts;

    /**
     * 状态详情
     */
    @ExcelProperty("erpdss.global.global.s66")
    private String remark;

    /**
     * ERP对象数据ID
     */
    @ExcelProperty("erpdss.global.global.s1049")
    private String erpDataId;

    /**
     * ERP对象主属性
     */
    @ExcelProperty("erpdss.global.global.s1050")
    private String erpDataName;

    /**
     * CRM对象
     */
    @ExcelProperty("erpdss.global.global.s1051")
    private String crmObjApiName;

    /**
     * CRM对象主属性
     */
    @ExcelProperty("erpdss.global.global.s1052")
    private String fsDataName;

    /**
     * CRM对象ID
     */
    @ExcelProperty("erpdss.global.global.s1053")
    private String fsDataId;

    /**
     * 最近同步时间
     */
    @ExcelProperty("erpdss.global.global.s1054")
    private String lastSyncDataTime;

    /**
     * 主对象源数据id
     */
    @ApiModelProperty("erpdss.global.global.s1036")
    private String masterDataId;



    public static ExportDataMappingExcelVo buildDataMapingExcelVo(String erpObjApiName,
                                                                  String statuts,
                                                                  String remark,
                                                                  String erpDataId,
                                                                  String erpDataName,
                                                                  String crmObjApiName,
                                                                  String fsDataName,
                                                                  String fsDataId,
                                                                  Long lastSyncDataTime,
                                                                  String masterDataId) {

        ExportDataMappingExcelVo exportDataMappingExcelVo = new ExportDataMappingExcelVo();
        exportDataMappingExcelVo.setErpObjApiName(erpObjApiName);
        exportDataMappingExcelVo.setStatuts(statuts);
        exportDataMappingExcelVo.setRemark(remark);
        exportDataMappingExcelVo.setErpDataId(erpDataId);
        exportDataMappingExcelVo.setErpDataName(erpDataName);
        exportDataMappingExcelVo.setCrmObjApiName(crmObjApiName);
        exportDataMappingExcelVo.setFsDataName(fsDataName);
        exportDataMappingExcelVo.setFsDataId(fsDataId);
        exportDataMappingExcelVo.setRemark(remark);
        exportDataMappingExcelVo.setLastSyncDataTime(DateUtil.formatDateTime(new Date(lastSyncDataTime)));
        exportDataMappingExcelVo.setMasterDataId(masterDataId);

        return exportDataMappingExcelVo;
    }


    public static List<ExportDataMappingExcelVo> convert2ExcelVo(List<SyncDataMappingsEntity> dtos,
                                                                 boolean sourceCrm,
                                                                 Map<Integer, String> statusMap,
                                                                 String erpObjName,
                                                                 String crmObjName,
                                                                 Map<String, SyncDataEntity> id2DataEntity,
                                                                 Map<Integer, String> statusTypeMap) {
        Function<SyncDataMappingsEntity, String> erpDataId;
        Function<SyncDataMappingsEntity, String> erpDataName;
        Function<SyncDataMappingsEntity, String> fsDataName;
        Function<SyncDataMappingsEntity, String> fsDataId;
        if (sourceCrm) {
            erpDataId = t -> t.getDestDataId();
            erpDataName = t -> t.getDestDataName();
            fsDataName = t -> t.getSourceDataName();
            fsDataId = t -> t.getSourceDataId();
        } else {
            erpDataId = t -> t.getSourceDataId();
            erpDataName = t -> t.getSourceDataName();
            fsDataName = t -> t.getDestDataName();
            fsDataId = t -> t.getDestDataId();
        }

        return dtos.stream().map(t -> ExportDataMappingExcelVo.buildDataMapingExcelVo(
                erpObjName,
                getStatus(t, id2DataEntity, statusTypeMap, statusMap),
                getRemark(t, id2DataEntity),
                erpDataId.apply(t),
                erpDataName.apply(t),
                crmObjName,
                fsDataName.apply(t),
                fsDataId.apply(t),
                t.getUpdateTime(),
                t.getMasterDataId())).collect(Collectors.toList());
    }

    private static String getRemark(SyncDataMappingsEntity entity, Map<String, SyncDataEntity> id2DataEntity) {
        if (entity.getLastSyncDataId() != null && id2DataEntity.get(entity.getLastSyncDataId()) != null) {
            SyncDataEntity syncDataEntity = id2DataEntity.get(entity.getLastSyncDataId());
            entity.setRemark(syncDataEntity.getRemark());
        }
        return entity.getRemark();
    }

    private static String getStatus(SyncDataMappingsEntity entity,
                                    Map<String, SyncDataEntity> id2DataEntity,
                                    Map<Integer, String> statusTypeMap,
                                    Map<Integer, String> statusMap) {
        String status = statusMap.get(entity.getLastSyncStatus());
        if (entity.getLastSyncDataId() != null && id2DataEntity.get(entity.getLastSyncDataId()) != null) {
            SyncDataEntity syncDataEntity = id2DataEntity.get(entity.getLastSyncDataId());
            if (syncDataEntity.getDestEventType() != null) {
                status = statusTypeMap.get(syncDataEntity.getDestEventType()) + " " + statusMap.get(entity.getLastSyncStatus());
            } else {
                status = statusTypeMap.get(syncDataEntity.getSourceEventType()) + " " + statusMap.get(entity.getLastSyncStatus());
            }
        }
        return status;
    }
}
