package com.fxiaoke.open.erpsyncdata.admin.model.k3ultimate;

import com.fxiaoke.open.erpsyncdata.dbproxy.model.K3UltimateEventConfigModel;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 云星空旗舰版事件订阅Model
 * <AUTHOR>
 * @date 2023-11-14
 */
@Data
public class K3UltimateSubscribeEventModel implements Serializable {
    private String token;
    private String url;
    private List<K3UltimateEventConfigModel> eventConfigList = new ArrayList<>();
}
