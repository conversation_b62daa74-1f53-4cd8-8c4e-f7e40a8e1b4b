package com.fxiaoke.open.erpsyncdata.admin.preset;

import com.fxiaoke.open.erpsyncdata.admin.arg.SyncPloyDetailCreateArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PresetInvoiceApplicationObject extends AbstractPresetObject {
    @Override
    protected List<String> getFormId() {
        return Lists.newArrayList(K3CloudForm.IV_SALESOC,K3CloudForm.IV_SALESIC);
    }

    @Override
    public List<String> getFieldMappingJson() {
        //销售普票字段映射
        String json1 = "{\"id\":\"\",\"masterObjectMapping\":{\"sourceObjectApiName\":\"IV_SALESOC.BillHead\",\"destObjectApiName\":\"InvoiceApplicationObj\",\"fieldMappings\":[{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FINVOICEDATE\",\"sourceType\":\"date\",\"destApiName\":\"invoice_date\",\"destType\":\"date\"},{\"valueType\":1,\"defaultValue\":null,\"mappingType\":3,\"value\":\"default__c\",\"optionMappings\":[],\"destApiName\":\"record_type\",\"destType\":\"record_type\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FCUSTOMERID.FNumber\",\"sourceType\":\"object_reference\",\"sourceTargetApiName\":\"BD_Customer.BillHead\",\"destApiName\":\"account_id\",\"destType\":\"object_reference\",\"destTargetApiName\":\"AccountObj\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":2,\"value\":\"FNumber\",\"optionMappings\":[],\"sourceApiName\":\"FSALEERID.FNumber\",\"sourceType\":\"employee\",\"destApiName\":\"owner\",\"destType\":\"employee\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FINVOICETITLE\",\"sourceType\":\"text\",\"destApiName\":\"invoice_title\",\"destType\":\"text\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FOPENBANKNAME\",\"sourceType\":\"text\",\"destApiName\":\"account_bank\",\"destType\":\"text\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FADDRESS\",\"sourceType\":\"long_text\",\"destApiName\":\"contact_add\",\"destType\":\"text\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FTAXREGISTERCODE\",\"sourceType\":\"text\",\"destApiName\":\"tax_id\",\"destType\":\"text\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FDRAWER\",\"sourceType\":\"text\",\"destApiName\":\"recipient\",\"destType\":\"text\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FBANKCODE\",\"sourceType\":\"text\",\"destApiName\":\"account_bank_no\",\"destType\":\"text\"},{\"valueType\":1,\"defaultValue\":null,\"mappingType\":3,\"value\":\"2\",\"optionMappings\":[],\"destApiName\":\"invoice_type\",\"destType\":\"select_one\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FIVNUMBER\",\"sourceType\":\"text\",\"destApiName\":\"invoice_no\",\"destType\":\"text\"}]},\"detailObjectMappings\":[{\"sourceObjectApiName\":\"IV_SALESOC.SALESICENTRY\",\"destObjectApiName\":\"InvoiceApplicationLinesObj\",\"fieldMappings\":[{\"mappingType\":1,\"sourceApiName\":\"fake_master_detail\",\"sourceType\":\"master_detail\",\"sourceTargetApiName\":\"IV_SALESOC.BillHead\",\"destApiName\":\"invoice_id\",\"destType\":\"master_detail\",\"destTargetApiName\":\"InvoiceApplicationObj\"},{\"valueType\":1,\"defaultValue\":null,\"mappingType\":3,\"value\":\"default__c\",\"optionMappings\":[],\"destApiName\":\"record_type\",\"destType\":\"record_type\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"SalesOrderComId\",\"sourceType\":\"object_reference\",\"sourceTargetApiName\":\"SAL_SaleOrder.BillHead\",\"destApiName\":\"order_id\",\"destType\":\"object_reference\",\"destTargetApiName\":\"SalesOrderObj\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FTAXRATE\",\"sourceType\":\"number\",\"destApiName\":\"tax_rate\",\"destType\":\"percentile\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FAMOUNTFOR\",\"sourceType\":\"currency\",\"destApiName\":\"invoiced_amount\",\"destType\":\"currency\"},{\"valueType\":1,\"defaultValue\":null,\"mappingType\":3,\"value\":\"-10000\",\"optionMappings\":[],\"destApiName\":\"owner\",\"destType\":\"employee\"}]}]}";
        //销售专票字段映射
        String json2 = "{\"id\":\"\",\"masterObjectMapping\":{\"sourceObjectApiName\":\"IV_SALESIC.BillHead\",\"destObjectApiName\":\"InvoiceApplicationObj\",\"fieldMappings\":[{\"valueType\":1,\"defaultValue\":null,\"mappingType\":3,\"value\":\"1\",\"optionMappings\":[],\"destApiName\":\"invoice_type\",\"destType\":\"select_one\"},{\"valueType\":1,\"defaultValue\":null,\"mappingType\":3,\"value\":\"default__c\",\"optionMappings\":[],\"destApiName\":\"record_type\",\"destType\":\"record_type\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FINVOICEDATE\",\"sourceType\":\"date\",\"destApiName\":\"invoice_date\",\"destType\":\"date\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FCUSTOMERID.FNumber\",\"sourceType\":\"object_reference\",\"sourceTargetApiName\":\"BD_Customer.BillHead\",\"destApiName\":\"account_id\",\"destType\":\"object_reference\",\"destTargetApiName\":\"AccountObj\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":2,\"value\":\"FNumber\",\"optionMappings\":[],\"sourceApiName\":\"FSALEERID.FNumber\",\"sourceType\":\"employee\",\"destApiName\":\"owner\",\"destType\":\"employee\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FINVOICETITLE\",\"sourceType\":\"text\",\"destApiName\":\"invoice_title\",\"destType\":\"text\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FOPENBANKNAME\",\"sourceType\":\"text\",\"destApiName\":\"account_bank\",\"destType\":\"text\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FADDRESS\",\"sourceType\":\"long_text\",\"destApiName\":\"contact_add\",\"destType\":\"text\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FTAXREGISTERCODE\",\"sourceType\":\"text\",\"destApiName\":\"tax_id\",\"destType\":\"text\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FDRAWER\",\"sourceType\":\"text\",\"destApiName\":\"recipient\",\"destType\":\"text\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FBANKCODE\",\"sourceType\":\"text\",\"destApiName\":\"account_bank_no\",\"destType\":\"text\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FIVNUMBER\",\"sourceType\":\"text\",\"destApiName\":\"invoice_no\",\"destType\":\"text\"}]},\"detailObjectMappings\":[{\"sourceObjectApiName\":\"IV_SALESIC.SALESICENTRY\",\"destObjectApiName\":\"InvoiceApplicationLinesObj\",\"fieldMappings\":[{\"mappingType\":1,\"sourceApiName\":\"fake_master_detail\",\"sourceType\":\"master_detail\",\"sourceTargetApiName\":\"IV_SALESIC.BillHead\",\"destApiName\":\"invoice_id\",\"destType\":\"master_detail\",\"destTargetApiName\":\"InvoiceApplicationObj\"},{\"valueType\":1,\"defaultValue\":null,\"mappingType\":3,\"value\":\"default__c\",\"optionMappings\":[],\"destApiName\":\"record_type\",\"destType\":\"record_type\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"SalesOrderComId\",\"sourceType\":\"object_reference\",\"sourceTargetApiName\":\"SAL_SaleOrder.BillHead\",\"destApiName\":\"order_id\",\"destType\":\"object_reference\",\"destTargetApiName\":\"SalesOrderObj\"},{\"valueType\":1,\"defaultValue\":null,\"mappingType\":3,\"value\":\"-10000\",\"optionMappings\":[],\"destApiName\":\"owner\",\"destType\":\"employee\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FTAXRATE\",\"sourceType\":\"number\",\"destApiName\":\"tax_rate\",\"destType\":\"percentile\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FAMOUNTFOR\",\"sourceType\":\"currency\",\"destApiName\":\"invoiced_amount\",\"destType\":\"currency\"}]}]}";
        return Lists.newArrayList(json1,json2);
    }

    @Override
    public List<SyncPloyDetailCreateArg> getSyncPloyDetailCreateArg() {
        SyncPloyDetailCreateArg arg = getSyncPloyDetailCreateArg(tenantId,
                "IV_SALESOC.BillHead",
                "InvoiceApplicationObj",
                new SyncPloyDetailCreateArg.DetailObjectMappingCreateArg("IV_SALESOC.SALESICENTRY",
                        "InvoiceApplicationLinesObj"));

        SyncPloyDetailCreateArg arg2 = getSyncPloyDetailCreateArg(tenantId,
                "IV_SALESIC.BillHead",
                "InvoiceApplicationObj",
                new SyncPloyDetailCreateArg.DetailObjectMappingCreateArg("IV_SALESIC.SALESICENTRY",
                        "InvoiceApplicationLinesObj"));

        return Lists.newArrayList(arg,arg2);
    }

    private SyncPloyDetailCreateArg getSyncPloyDetailCreateArg(String ployId,
                                                               String sourceObjectApiName,
                                                               String destObjectApiName,
                                                               SyncPloyDetailCreateArg.DetailObjectMappingCreateArg detailObjectMappingCreateArg) {
        SyncPloyDetailCreateArg arg = new SyncPloyDetailCreateArg();
        arg.setPloyId(ployId);
        arg.setSourceTenantIds(Lists.newArrayList(tenantId));
        arg.setSourceTenantType(TenantType.ERP);
        arg.setSourceObjectApiName(sourceObjectApiName);
        arg.setDestTenantIds(Lists.newArrayList(tenantId));
        arg.setDestTenantType(TenantType.CRM);
        arg.setDestObjectApiName(destObjectApiName);

        arg.setDetailObjectMappings(Lists.newArrayList(
                detailObjectMappingCreateArg
        ));
        arg.setDcId(dataCenterId);
        return arg;
    }
}
