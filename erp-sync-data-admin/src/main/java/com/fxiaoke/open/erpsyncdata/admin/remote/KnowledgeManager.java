package com.fxiaoke.open.erpsyncdata.admin.remote;

import com.facishare.eservice.base.result.EserviceResult;
import com.facishare.eservice.rest.common.Arg1;
import com.facishare.eservice.rest.common.HeaderObj;
import com.facishare.eservice.rest.online.model.SearchSceneKnowledgeModel;
import com.facishare.eservice.rest.online.service.KnowledgeCloudToFoneshareService;
import com.fxiaoke.open.erpsyncdata.admin.model.RecommendSolution;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/5/18 17:57:15
 */
@Slf4j
@Component
public class KnowledgeManager {
    @Autowired(required = false)
    private KnowledgeCloudToFoneshareService knowledgeService;

    public List<RecommendSolution> searchKnowledge(String key, final Integer size) {
        if (ConfigCenter.notKnowledgeService) {
            return new ArrayList<>();
        }

        for (String knowledgeStopWord : ConfigCenter.KnowledgeStopWords) {
            key = key.replace(knowledgeStopWord, "");
        }

            final SearchSceneKnowledgeModel.SearchArg build = SearchSceneKnowledgeModel.SearchArg.builder()
                    .fsEa(ConfigCenter.fsSearchKnowledgeEa)
                    .scene(ConfigCenter.erpdssSearchKnowledgeScene)
                    .content(key)
                    .build();
            final Arg1<SearchSceneKnowledgeModel.SearchArg> arg1 = new Arg1<>();
            arg1.setArg1(build);
        try {
            final EserviceResult<SearchSceneKnowledgeModel.SearchResult> result = knowledgeService.searchKnowledgeResult(HeaderObj.newInstance(ConfigCenter.fsSearchKnowledgeEi), arg1);

            if (!result.isSuccess()) {
                log.warn("convert2RecommendSolution failed, arg:{} result:{}", arg1, result);
                return Lists.newArrayList();
            }
            List<SearchSceneKnowledgeModel.Result> data = result.getData().getSearchResult();
            if (Objects.nonNull(size) && CollectionUtils.isNotEmpty(data) && data.size() > size) {
                data = data.subList(0, size);
            }
            return RecommendSolution.convert2RecommendSolution(data);
        } catch (Exception e) {
            log.warn("searchKnowledge error, arg:{}", arg1, e);
            return Lists.newArrayList();
        }
    }
}
