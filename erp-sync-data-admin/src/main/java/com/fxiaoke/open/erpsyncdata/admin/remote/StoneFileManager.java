package com.fxiaoke.open.erpsyncdata.admin.remote;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.converter.EIEAConverter;
import com.facishare.stone.sdk.StoneProxyApi;
import com.facishare.stone.sdk.request.StoneFileDownloadRequest;
import com.facishare.stone.sdk.request.StoneSaveFileFromTempFileRequest;
import com.facishare.stone.sdk.request.StoneSaveImageFromTempFilesRequest;
import com.facishare.stone.sdk.response.StoneSaveFileFromTempFileResponse;
import com.facishare.stone.sdk.response.StoneSaveImageFromTempFileResponse;
import com.facishare.stone.sdk.response.StoneSaveImageFromTempFilesResponse;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/5 15:05:23
 */
@Component
@Slf4j
public class StoneFileManager {

    @Autowired
    private StoneProxyApi stoneProxyApi;
    @Autowired
    EIEAConverter eieaConverter;

    @SneakyThrows
    public InputStream downloadByPath(String tenantId, String path, String suffix) {
        final String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));
        StoneFileDownloadRequest downloadRequest = new StoneFileDownloadRequest();
        downloadRequest.setFileType(suffix);
        downloadRequest.setCancelRemoteThumb(false);
        downloadRequest.setPath(path);
        downloadRequest.setSecurityGroup("");
        downloadRequest.setEa(ea);
        downloadRequest.setEmployeeId(1000);
        downloadRequest.setBusiness("FMCG-SALES");
        return stoneProxyApi.downloadStream(downloadRequest);
    }

    @SneakyThrows
    public String saveTempFile(String ea, String npath) {
        StoneSaveFileFromTempFileRequest request = StoneSaveFileFromTempFileRequest.builder()
                .ea(ea)
                .employeeId(-10000)
                .business(CommonConstant.ERP_SYNC_DATA_BUSINESS)
                .tempFileName(npath)
                .namedPath(npath).build();
        StoneSaveFileFromTempFileResponse response = stoneProxyApi.saveFileFromTempFile(request);
        return response.getPath();
    }

    public String saveImageFromTempImagePathByTenantId(String tenantId,
                                                       Integer employeeId,
                                                       String tempImagePath) {
        final Map<String, String> pathMap = saveImageFromTempImagePath(eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId)), employeeId, Lists.newArrayList(tempImagePath));
        return pathMap.get(tempImagePath);
    }

    @SneakyThrows
    public Map<String, String> saveImageFromTempImagePath(String ea,
                                                          Integer employeeId,
                                                          List<String> tempImagePathList) {
        if (CollectionUtils.isEmpty(tempImagePathList)) {
            return new HashMap<>();
        }

        final List<String> pathList = tempImagePathList.stream()
                .filter(path -> StringUtils.isNotBlank(path) && path.startsWith("T"))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pathList)) {
            return new HashMap<>();
        }

        StoneSaveImageFromTempFilesRequest stoneSaveImageFromTempFilesRequest = new StoneSaveImageFromTempFilesRequest();
        stoneSaveImageFromTempFilesRequest.setTempFileNames(pathList);
        stoneSaveImageFromTempFilesRequest.setEa(ea);
        stoneSaveImageFromTempFilesRequest.setEmployeeId(employeeId);
        stoneSaveImageFromTempFilesRequest.setBusiness(CommonConstant.ERP_SYNC_DATA_BUSINESS);

        StoneSaveImageFromTempFilesResponse stoneSaveImageFromTempFilesResponse = stoneProxyApi.saveImageFromTempFiles(stoneSaveImageFromTempFilesRequest);
        return stoneSaveImageFromTempFilesResponse.getResponseList().stream().collect(Collectors.toMap(StoneSaveImageFromTempFileResponse::getTempFileName, StoneSaveImageFromTempFileResponse::getPath));
    }
}
