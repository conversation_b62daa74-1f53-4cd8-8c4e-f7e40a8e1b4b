package com.fxiaoke.open.erpsyncdata.admin.result;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.constant.DataVerificationTaskStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 15:41 2022/11/3
 * @Desc:
 */
@Data
@ApiModel("数据核对任务")
public class DataVerificationResult implements Serializable {
    @ApiModelProperty(value = "唯一标识")
    private String id;
    @ApiModelProperty(value = "企业id")
    private String tenantId;
    @ApiModelProperty(value = "集成流id")
    private String streamId;
    @ApiModelProperty(value = "任务id")
    private String taskId;
    @ApiModelProperty(value = "文件nPath")
    private String fileNPath;
    @ApiModelProperty(value = "文件名称")
    private String fileName;
    @ApiModelProperty(value = "导入id数量")
    private Integer allIdListSize;
    @ApiModelProperty(value = "未读取数量")
    private Integer notTempIdListSize;
    @ApiModelProperty(value = "接口返回数量")
    private Integer pollingListSize;
    @ApiModelProperty(value = "入临时库数量")
    private Integer tempIdListSize;
    @ApiModelProperty(value = "多读取数量")
    private Long tempButNotCheckIdListSize;
    @ApiModelProperty(value = "未触发同步数量")
    private Integer notMappingIdListSize;
    @ApiModelProperty(value = "触发同步数量")
    private Integer mappingIdListSize;
    @ApiModelProperty(value = "未创建成功数量")
    private Integer notCreatedIdListSize;
    @ApiModelProperty(value = "创建成功数量")
    private Integer createdIdListSize;
    @ApiModelProperty(value = "状态")
    private DataVerificationTaskStatusEnum status;
    @ApiModelProperty(value = "id详情")
    private List<DataVerificationIdStatusResult> dataList;
    @ApiModelProperty(value = "未核对id详情")
    private List<DataVerificationIdStatusResult> notCheckDataList;
    @ApiModelProperty(value = "是否需要停止核对")
    private Boolean needStop;
    @ApiModelProperty(value = "创建时间")
    private Long createTime;
    @ApiModelProperty(value = "更新时间")
    private Long updateTime;

}
