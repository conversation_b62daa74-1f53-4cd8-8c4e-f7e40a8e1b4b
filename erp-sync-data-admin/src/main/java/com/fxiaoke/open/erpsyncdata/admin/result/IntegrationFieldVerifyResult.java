package com.fxiaoke.open.erpsyncdata.admin.result;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/3/11 19:45  字段检查的返回结果
 * @Version 1.0
 *
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel
public class IntegrationFieldVerifyResult implements Serializable {
    @ApiModelProperty("字段的错误码")
    private ResultCodeEnum errorCode;
    @ApiModelProperty("对象名字")
    private String objectApiName;
    @ApiModelProperty("字段名字")
    private String fieldApiName;

    public static IntegrationFieldVerifyResult newInstanceErrCode(ResultCodeEnum codeEnum){
        IntegrationFieldVerifyResult integrationFieldVerifyResult=new IntegrationFieldVerifyResult();
        integrationFieldVerifyResult.setErrorCode(codeEnum);
        return integrationFieldVerifyResult;
    }
}
