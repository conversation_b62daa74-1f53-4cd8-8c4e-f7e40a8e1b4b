package com.fxiaoke.open.erpsyncdata.admin.result;


import com.fxiaoke.open.erpsyncdata.admin.data.DataCenterData;
import com.fxiaoke.open.erpsyncdata.admin.data.SyncRulesWebData;
import com.fxiaoke.open.erpsyncdata.admin.data.TenantData;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/2 18:28
 * @Version 1.0
 */
@Data
public class IntegrationViewResult implements Serializable {

    @ApiModelProperty("集成流id")
    private String id;
    @ApiModelProperty("集成流名称")
    private String integrationStreamName;
    @ApiModelProperty("源企业id")
    private List<TenantData> sourceTenantDatas = Lists.newArrayList();
    @ApiModelProperty("源企业类型")
    private Integer sourceTenantType;
    @ApiModelProperty("源数据中心")
    private DataCenterData sourceDc;
    @ApiModelProperty("源企业主对象apiName")
    private String sourceObjectApiName;
    @ApiModelProperty("源企业主对象名称")
    private String sourceObjectName;
    @ApiModelProperty("源企业主对象真实名称")
    private String sourceObjectActualApiName;
    @ApiModelProperty("目标企业Id")
    private List<TenantData> destTenantDatas = Lists.newArrayList();
    @ApiModelProperty("目标企业类型")
    private Integer destTenantType;
    @ApiModelProperty("目标数据中心")
    private DataCenterData destDc;
    @ApiModelProperty("目标企业主对象apiName")
    private String destObjectApiName;
    @ApiModelProperty("目标企业主对象名称")
    private String destObjectName;
    @ApiModelProperty("启用状态，1启用 2停用")
    private Integer status;
    @ApiModelProperty("状态名称，启用，停用")
    private String statusName;
    @ApiModelProperty("同步规则")
    private SyncRulesWebData syncRules;
    @ApiModelProperty("策略是否被熔断的")
    private boolean broken;
    @ApiModelProperty("集成流停用时间，仅当集成流是停用时存在")
    private Long stopTime;

    @ApiModelProperty("反向集成流id")
    private String reverseStreamId;
    @ApiModelProperty("反向集成流名称")
    private String reverseStreamName;



    @ApiModelProperty("从对象相关信息")
    private List<IntegrationStreamResult.ObjectMappingInfo> detailObjectMappings = Lists.newArrayList();


    @Data
    public static class InvalidInfoResult implements Serializable{
        @ApiModelProperty("集成流id")
        private String id;
        @ApiModelProperty("异常状态显示，当对象被禁用，或者字段，函数被禁用，提示流异常")
        private Boolean invalid;
//        @ApiModelProperty("数据同步失败的数量")
//        private Integer syncFailedDataCount = 0;
        @ApiModelProperty("最后执行时间")
        private Long lastSyncTime;
    }
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SyncFailResult implements Serializable{
        @ApiModelProperty("集成流id")
        private String id;
        @ApiModelProperty("数据同步失败的数量")
        private Integer syncFailedDataCount = 0;
        @ApiModelProperty("是否是超时返回的默认结果")
        private Boolean hasTimeOutResult=false;

    }

    @Data
    public static class CalculateCountResult implements Serializable{
        @ApiModelProperty("集成流id")
        private String id;
        @ApiModelProperty("数据总数")
        private Integer syncAccount = 0;
    }
}
