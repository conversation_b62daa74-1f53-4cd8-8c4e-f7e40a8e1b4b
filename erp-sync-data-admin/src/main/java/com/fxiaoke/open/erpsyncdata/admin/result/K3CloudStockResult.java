package com.fxiaoke.open.erpsyncdata.admin.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * K3CLOUD库存查询结果
 * <AUTHOR>
 * @date 2021/08/31
 */
@Data
@ApiModel
public class K3CloudStockResult implements Serializable {
    @ApiModelProperty(value = "仓库编码")
    //仓库编码类似：CK009.1||001.03
    private String warehouseNumber;

    @ApiModelProperty(value = "物料编码")
    private String productNumber;

    @ApiModelProperty(value = "产品名称,必填")
    private String productName;

    @ApiModelProperty(value = "批次编码")
    //批次编码类似：CH566z/zsl2021bbhh/1
    private String batchNumber;

//    @ApiModelProperty("基本单位名称")
//    private String baseUnitName;
//
//    @ApiModelProperty("基本单位编码")
//    private String baseUnitNumber;

    @ApiModelProperty("库存量（基本单位）")
    private double baseQty;

    @ApiModelProperty("可用量（基本单位）")
    private double baseAvbQty;

//    @ApiModelProperty("库存主单位名称")
//    private String stockUnitName;
//
//    @ApiModelProperty("库存主单位编码")
//    private String stockUnitNumber;

//    @ApiModelProperty("库存（主单位）")
//    private Double qty;
//
//    @ApiModelProperty("可用量（主单位）")
//    private Double avbQty;
}
