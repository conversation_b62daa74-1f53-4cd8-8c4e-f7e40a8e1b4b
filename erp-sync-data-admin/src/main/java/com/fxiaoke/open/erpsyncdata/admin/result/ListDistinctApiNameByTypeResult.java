package com.fxiaoke.open.erpsyncdata.admin.result;


import com.fxiaoke.open.erpsyncdata.admin.data.ObjectApiNameData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2020/7/9.
 */
@Data
public class ListDistinctApiNameByTypeResult implements Serializable {
    private List<ObjectApiNameData> objectApiNameDataList;
    @ApiModelProperty("总条数")
    private int totalCount;
    @ApiModelProperty("页长")
    private int pageSize;
    @ApiModelProperty("页码")
    private int pageNumber;

}
