package com.fxiaoke.open.erpsyncdata.admin.result;

import lombok.Data;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/7/2 20:41
 * @desc
 */
@Data
public class PollingTempRecordResult  implements Serializable {

    private String id;

    private String tenantId;

    private String timeFilterArg;

    private List<Integer> theObjOperateType;

    private boolean needSendDetailEvent;

    private String syncPloyDetailId;

    private String traceId;
    /**
     * 1.未执行 2.执行中 3.成功 4.失败满重试次数
     */

    private Integer status;

    /**
     * 最后一次错误的错误信息
     * 成功后会不置空
     */

    private String errMsg;
    /**
     * 已重试次数
     */

    private int tryTime;
    /**
     * 下次重试时间
     */

    private Long nextRetryTime;

    private Long createTime;

    private Long updateTime;
}
