package com.fxiaoke.open.erpsyncdata.admin.result;


import com.fxiaoke.open.erpsyncdata.admin.data.SyncConditionsData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class SyncConditionsResult implements Serializable {
    @ApiModelProperty("主对象数据范围")
    private SyncConditionsData syncConditions;
    @ApiModelProperty("从对象数据范围")
    private List<SyncConditionsData> detailObjectSyncConditions;
}
