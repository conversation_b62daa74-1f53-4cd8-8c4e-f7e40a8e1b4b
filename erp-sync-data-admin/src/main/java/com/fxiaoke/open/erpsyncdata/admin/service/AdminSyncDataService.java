package com.fxiaoke.open.erpsyncdata.admin.service;


import com.fxiaoke.open.erpsyncdata.admin.arg.ListSyncDataHistoryArg;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncDataHistoryListResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;

public interface AdminSyncDataService {
    Result<List<SyncDataHistoryListResult>> listSyncDataHistory(String tenantId, ListSyncDataHistoryArg arg, String lang);

    Result<List<SyncDataHistoryListResult>> listSyncDataHistoryBySource(String tenantId, ListSyncDataHistoryArg arg, String lang);
}
