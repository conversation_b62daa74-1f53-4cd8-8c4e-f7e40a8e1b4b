package com.fxiaoke.open.erpsyncdata.admin.service;

import com.fxiaoke.open.erpsyncdata.admin.model.PriorityConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ObjDispatchPriorityConfig;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

/**
 * <AUTHOR> (^_−)☆
 */
public interface AllObjConfigService {
    /**
     * 获取数据优先级配置，对象级别
     */
    Result<ObjDispatchPriorityConfig> getObjDispatchPriorityConfig(String tenantId, PriorityConfig.GetArg arg);

    /**
     * 修改数据优先级配置，对象级别
     */
    Result<Void> setObjDispatchPriorityConfig(String tenantId, PriorityConfig.SetArg arg);
}
