package com.fxiaoke.open.erpsyncdata.admin.service;

import cn.hutool.core.lang.Dict;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;

/**
 * 清理资源服务
 * <AUTHOR> (^_−)☆
 * @date 2023/5/30
 */
public interface CleanResourceService {
    /**
     * 清理已标记删除的表
     * @param needDeleteBeforeDay 删除多少天前标记的表
     */
    Result<Dict> cleanDeletedTables(int needDeleteBeforeDay);

    /**
     * 清理已删除的数据
     * @return
     */
    Result<Dict> cleanDeletedData(List<String> tenantIds);
}
