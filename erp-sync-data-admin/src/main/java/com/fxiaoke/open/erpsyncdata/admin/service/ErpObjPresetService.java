package com.fxiaoke.open.erpsyncdata.admin.service;

import com.fxiaoke.open.erpsyncdata.admin.model.ErpAnalysisField;
import com.fxiaoke.open.erpsyncdata.admin.model.InitK3Obj;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.ObjectDescArg;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ErpObjTreeNode;
import com.fxiaoke.open.erpsyncdata.preprocess.result.InitDcResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/2/20
 */
public interface ErpObjPresetService {

    /**
     * 传入参数初始化K3对象
     * 客户参数失败后尝试使用默认的。
     *
     * @return 新的对象信息
     */
    Result<String> preSetK3Obj(String tenantId, String dcId, InitK3Obj.PresetObjArg erpObj,String lang);

    /**
     * 分析K3对象
     *
     * @param tenantId
     * @param dcId
     * @param analyzeObjArg
     * @return
     */
    Result<ErpObjTreeNode> analyzeK3Obj(String tenantId, String dcId, InitK3Obj.AnalyzeObjArg analyzeObjArg,String lang);

    /**
     * 分析K3对象字段
     */
    Result<ErpAnalysisField> analyzeK3ObjField(String tenantId, String dcId, InitK3Obj.AnalysisFieldArg arg,String lang);

    /**
     * 解析对象列表
     *
     * @param tenantId
     * @param dcId
     * @return
     */
    Result<List<ErpObjTreeNode>> parseObjectList(String tenantId, String dcId);

    /**
     * 解析某对象
     *
     * @param connectInfo
     * @param arg
     * @return
     */
    Result<ErpObjTreeNode> parseObject(String tenantId, String dcId, ObjectDescArg.ParseObjTree arg);

    /**
     * 解析某对象字段
     *
     * @param connectInfo
     * @param arg
     * @return
     */
    Result<ErpAnalysisField> parseObjectField(String tenantId, String dcId, ObjectDescArg.ParseObjFieldBySplit arg);

    /**
     * 预置Obj，使用parseObjectDesc的数据
     *
     * @return 新的对象信息
     */
    Result<String> presetObject(String tenantId, String dcId, ObjectDescArg.ParseObjField arg);

    /**
     * 异步初始化数据中心
     */
    Result<InitDcResult> initDcPresetObjAndStream(String tenantId, String dcId);
}
