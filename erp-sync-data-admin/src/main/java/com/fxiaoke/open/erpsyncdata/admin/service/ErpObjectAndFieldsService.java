package com.fxiaoke.open.erpsyncdata.admin.service;

import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.InitErpObjectFieldsArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 15:24 2020/9/9
 * @Desc:
 */
public interface ErpObjectAndFieldsService {
    List<ObjectDescribe> getErpObjAndFields(String loginUserTenantId,String dataCenterId, List<String> sourceObjectApiNames);
    /**
     * 初始化对象和字段
     * @param initErpObjectFieldsArg
     * @return
     */
    Result<Void> initObjAndFields(InitErpObjectFieldsArg initErpObjectFieldsArg);
}
