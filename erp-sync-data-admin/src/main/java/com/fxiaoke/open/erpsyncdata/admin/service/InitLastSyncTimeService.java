package com.fxiaoke.open.erpsyncdata.admin.service;

import com.fxiaoke.open.erpsyncdata.preprocess.model.PollingIntervalApiDto;

import java.util.Collection;

/**
 * <AUTHOR>
 * @Date: 18:50 2020/9/3
 * @Desc:
 */
public interface InitLastSyncTimeService {
    /**
     * 初始化最后同步时间
     * @param tenantId
     * @param objectApiName
     * @param operationTypes
     * @param time
     * @param pollingInterval
     */
    void initLastSyncTime(String tenantId,
                          String objectApiName,
                          Collection<Integer> operationTypes,
                          Long time,
                          PollingIntervalApiDto pollingInterval);
}
