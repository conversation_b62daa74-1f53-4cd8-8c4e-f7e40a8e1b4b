package com.fxiaoke.open.erpsyncdata.admin.service;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.admin.model.NPathModel;
import com.fxiaoke.open.erpsyncdata.admin.model.lexiang.LeXiangDoc;
import com.fxiaoke.open.erpsyncdata.admin.model.lexiang.UploadAssetData;
import com.fxiaoke.open.erpsyncdata.admin.model.lexiang.result.GetSuperAdminListResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import okhttp3.MediaType;

import java.util.List;

/**
 * 腾讯乐享相关服务接口封装
 * <AUTHOR>
 * @date 2023.05.25
 */
public interface LeXiangService {
    /**
     * 获取access token
     * @param appKey
     * @param appSecret
     * @return
     */
    Result<String> getAccessToken(String appKey, String appSecret);

    /**
     * 获取超级管理员列表
     * @param appKey
     * @param appSecret
     * @return
     */
    GetSuperAdminListResult getSuperAdminList(String appKey, String appSecret);

    /**
     * 上传附件
     * @param appKey
     * @param appSecret
     * @param data 需要上传的文件数据
     * @param type image 表示图片，audio 表示音频
     * @param isPublic 0：表示不需要公共地址，1：表示要获取公共地址，默认0
     * @return
     */
    Result<UploadAssetData> uploadAsset(String appKey,
                                        String appSecret,
                                        String type,
                                        Integer isPublic,
                                        String fileName,
                                        MediaType mediaType,
                                        byte[] data);

    /**
     * 上传NPATH到乐享
     * @param appKey
     * @param appSecret
     * @param type
     * @param isPublic
     * @param fileName
     * @param fsEa
     * @param nPath
     * @return
     */
    Result<UploadAssetData> uploadAsset(String appKey,
                                        String appSecret,
                                        String type,
                                        Integer isPublic,
                                        String fileName,
                                        MediaType mediaType,
                                        String fsEa,
                                        String nPath);
    /**
     * 转发到乐享
     * @param doc
     */
    Result<JSONObject> sendDoc(String appKey, String appSecret, LeXiangDoc doc);

    /**
     * 转发工单到乐享，html里面{npath}需要被替换成乐享的图片资源URL
     * <p>富文本内容</p><p>test only</p><p><img src=\"{e7c078d0fb8211edbe12ba6cd17b5567}\" /></p>
     * @param html
     */
    Result<JSONObject> sendHtmlWorkOrder(String appKey,
                                         String appSecret,
                                         String fsEa,
                                         String title,
                                         String html,
                                         List<NPathModel> nPathList);
}
