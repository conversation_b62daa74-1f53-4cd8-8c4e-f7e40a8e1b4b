package com.fxiaoke.open.erpsyncdata.admin.service;

import com.fxiaoke.open.erpsyncdata.dbproxy.model.ExcelSheetArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;

/**
 * 系统字段服务
 * <AUTHOR>
 * @date 2023.03.20
 */
public interface SystemFieldService {
    /**
     * 批量删除系统字段映射
     * @param tenantId
     * @param userId
     * @param dataCenterId
     * @param fieldTypeEnum
     * @param idList
     * @return
     */
    Result<Long> batchDeleteFieldMapping(String tenantId,
                                         int userId,
                                         String dataCenterId,
                                         ErpFieldTypeEnum fieldTypeEnum,
                                         List<String> idList,
                                         String lang);

    /**
     * 导出系统映射数据，如果idList为空，导出所有，如果不为空，只导出idList指定的数据
     * @param tenantId
     * @param userId
     * @param dataCenterId
     * @param fieldTypeEnum
     * @param idList
     * @return
     */
    Result<Void> exportSystemFieldMapping(String tenantId,
                                          Integer userId,
                                          String dataCenterId,
                                          ErpFieldTypeEnum fieldTypeEnum,
                                          List<String> idList,
                                          String lang);

    Result<List<ExcelSheetArg>> batchExportSystemFieldMapping(String tenantId, Integer userId, String dataCenterId, List<ErpFieldTypeEnum> fieldType, List<String> idList, String lang);

    /**
     * 暂时只支持K3c
     */
    Result<List<ExcelSheetArg>> autoMatchDistrictMapping(String tenantId, Integer userId, String lang, String dataCenterId, String country, String province, String city, String county, String town);

    Result<List<ExcelSheetArg>> getSystemFieldMappingExcelTemplate(String tenantId, String dataCenterId, List<ErpFieldTypeEnum> fieldType, String lang);
}
