package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.open.erpsyncdata.admin.service.DoTaskUrlService;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ProxyHttpClient;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 14:15 2021/1/13
 * @Desc:
 */
@Service
public class DoTaskUrlServiceImpl implements DoTaskUrlService {
    @Autowired
    private ProxyHttpClient proxyHttpClient;

    @Override
    public Result<String> manualExecutePloys(String tenantId,String objectApiName){
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "UTF-8");
        String manualExecutePloysUrl = ConfigCenter.MANUAL_EXECUTE_PLOYS_URL + "?tenantId=" + tenantId + "&objApiName=" + objectApiName;
        return postTaskUrl(manualExecutePloysUrl, header,Collections.emptyMap());
    }

    private Result<String> postTaskUrl(String url, Map<String, String> header, Map<String, String> params) {
        if (header == null) {
            header = new HashMap<>();
        }
        header.putIfAbsent("Content-Type", "UTF-8");
        ProxyHttpClient.SimpleHttpResult result1 = proxyHttpClient.postUrlResult(url, params, header);
        if (!result1.isSuccess()) {
            return result1;
        }
        Result<String> result = JSONObject.parseObject(result1.getData(), new TypeReference<Result<String>>() {
        });
        return result;
    }

}
