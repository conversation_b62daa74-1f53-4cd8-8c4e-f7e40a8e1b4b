package com.fxiaoke.open.erpsyncdata.admin.service.impl;


import com.fxiaoke.open.erpsyncdata.admin.service.ConnectInfoService;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjCustomFunctionService;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjGroovyDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjGroovyEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.BaseArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ConnectInfoResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjCustomFunctionResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum.SYSTEM_ERROR;

/**
 * <AUTHOR>
 * @Date: 13:36 2020/8/19
 * @Desc:
 */
@Service
@Data
@Slf4j
public class ErpObjCustomFunctionServiceImpl implements ErpObjCustomFunctionService {
    @Autowired
    private ErpObjGroovyDao erpObjGroovyDao;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private ConnectInfoService connectInfoService;

    @Override
    public List<String> queryAllFunctionApiNameByObjectApiName(final String tenantId, final String dataCenterId, final List<String> objApiNameList) {
        if (CollectionUtils.isEmpty(objApiNameList)) {
            return new ArrayList<>();
        }

        List<ErpObjGroovyEntity> erpObjGroovyEntities = erpObjGroovyDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryByObjectApiName(tenantId, dataCenterId, objApiNameList);

        return erpObjGroovyEntities.stream()
                .map(ErpObjGroovyEntity::getFuncApiName)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public Result<String> update(String tenantId, int userId, ErpObjCustomFunctionResult erpObjCustomFunctionResult,String lang) {
        ErpObjGroovyEntity erpObjGroovyEntity = new ErpObjGroovyEntity();
        BeanUtils.copyProperties(erpObjCustomFunctionResult, erpObjGroovyEntity);
        erpObjGroovyEntity.setTenantId(tenantId);
        if(ErpObjInterfaceUrlEnum.push.name().equals(erpObjCustomFunctionResult.getUrl().name())){//如果是push的
            Result<ConnectInfoResult> connectInfo = connectInfoService
                    .getConnectInfoByDataCenterId(tenantId, userId,erpObjCustomFunctionResult.getDataCenterId());
            ConnectInfoResult connectInfoResult=connectInfo.getData();
            if(connectInfoResult.getPushDataApiNames()!=null&&!connectInfoResult.getPushDataApiNames().contains(erpObjCustomFunctionResult.getObjApiName())){
                connectInfoResult.getPushDataApiNames().add(erpObjCustomFunctionResult.getObjApiName());//增加apiName
                connectInfoService.updateConnectInfo(tenantId,userId,connectInfoResult,lang, false);
            }
        }
        if (StringUtils.isEmpty(erpObjCustomFunctionResult.getId())) {//插入
            ErpObjGroovyEntity oldEntry = erpObjGroovyDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .getByTenantIdAndApiNameAndUrl(tenantId,erpObjCustomFunctionResult.getDataCenterId(), erpObjCustomFunctionResult.getObjApiName(), erpObjCustomFunctionResult.getUrl().name());
            if(oldEntry!=null){
                return Result.newError(ResultCodeEnum.THE_ENTERPRISE_CON_EXIST);
            }
            erpObjGroovyEntity.setId(idGenerator.get());
            erpObjGroovyEntity.setCreateTime(System.currentTimeMillis());
            erpObjGroovyEntity.setUpdateTime(System.currentTimeMillis());
            int insertResult = erpObjGroovyDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(erpObjGroovyEntity);
            if (insertResult == 1) {
                return Result.newSuccess(erpObjGroovyEntity.getId());
            } else {
                return Result.newError(SYSTEM_ERROR);
            }
        } else {//更新
            erpObjGroovyEntity.setUpdateTime(System.currentTimeMillis());
            int updateResult = erpObjGroovyDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateById(erpObjGroovyEntity);
            if (updateResult == 1) {
                return Result.newSuccess(erpObjGroovyEntity.getId());
            } else {
                return Result.newError(SYSTEM_ERROR);
            }
        }
    }

    @Override
    public Result<String> delete(String tenantId, int userId, String dcId, BaseArg deleteArg){
        if(!StringUtils.isNotBlank(deleteArg.getId())){
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        ErpObjGroovyEntity entity = erpObjGroovyDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findById(deleteArg.getId());
        if(entity==null){
            return Result.newError(SYSTEM_ERROR);
        }
//        if(ErpObjInterfaceUrlEnum.push.name().equals(entity.getUrl().name())){//如果是push的
//            Result<ConnectInfoResult> connectInfo = connectInfoService.getConnectInfoByDataCenterId(tenantId, userId,dcId);
//            ConnectInfoResult connectInfoResult=connectInfo.getData();
//            if(connectInfoResult.getPushDataApiNames()!=null&&connectInfoResult.getPushDataApiNames().contains(entity.getObjApiName())){
//                connectInfoResult.getPushDataApiNames().remove(entity.getObjApiName());//去掉apiName
//                connectInfoService.updateConnectInfo(tenantId,userId,connectInfoResult);
//            }
//        }
        int deleteResult = erpObjGroovyDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteByEiAndId(tenantId, deleteArg.getId());
        if (deleteResult == 1) {
            return Result.newSuccess();
        } else {
            return Result.newError(SYSTEM_ERROR);
        }
    }
}
