package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.crmrestapi.common.data.FieldDescribe;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.open.erpsyncdata.admin.manager.CopySettingManager;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjectAndFieldsService;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectFieldDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.DataCenterManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.InitErpObjectFieldsArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 15:24 2020/9/9
 * @Desc:
 */
@Service
@Slf4j
@Data
public class ErpObjectAndFieldsServiceImpl implements ErpObjectAndFieldsService {
    @Autowired
    private ErpObjectFieldDao erpObjectFieldDao;
    @Autowired
    private ErpObjectDao erpObjectDao;
    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private ErpFieldManager erpFieldManager;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private DataCenterManager dataCenterManager;
    @Autowired
    private CopySettingManager copySettingManager;

    @Override
    public List<ObjectDescribe> getErpObjAndFields(String tenantId,String dataCenterId, List<String> objectApiNames) {
        List<ObjectDescribe> objectDescribeList = Lists.newArrayList();
        for (String apiName : objectApiNames) {
            ErpObjectEntity query = new ErpObjectEntity();
            query.setTenantId(tenantId);
            query.setDataCenterId(dataCenterId);
            query.setErpObjectApiName(apiName);
            List<ErpObjectEntity> erpObjectEntities = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(query);
            for (ErpObjectEntity erpObjectEntity : erpObjectEntities) {
                ObjectDescribe objectDescribe = new ObjectDescribe();
                objectDescribe.setApiName(erpObjectEntity.getErpObjectApiName());
                objectDescribe.setDisplayName(erpObjectEntity.getErpObjectName());
                ErpObjectFieldEntity fieldQuery = new ErpObjectFieldEntity();
                fieldQuery.setTenantId(tenantId);
                fieldQuery.setDataCenterId(dataCenterId);
                fieldQuery.setErpObjectApiName(erpObjectEntity.getErpObjectApiName());
                List<ErpObjectFieldEntity> erpObjectFieldEntities = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(fieldQuery);
                HashMap<String, FieldDescribe> fieldDescribeMap = Maps.newHashMap();
                for (ErpObjectFieldEntity erpObjectFieldEntity : erpObjectFieldEntities) {
                    FieldDescribe fieldDescribe = new FieldDescribe();
                    fieldDescribe.put(FieldDescribe.API_NAME, erpObjectFieldEntity.getFieldApiName());
                    fieldDescribe.put(FieldDescribe.TYPE, erpObjectFieldEntity.getFieldDefineType());
                    fieldDescribe.put(FieldDescribe.LABEL, erpObjectFieldEntity.getFieldLabel());
                    fieldDescribe.put(FieldDescribe.IS_REQUIRED, erpObjectFieldEntity.getRequired());
                    if (ErpFieldTypeEnum.object_reference == erpObjectFieldEntity.getFieldDefineType()
                            || ErpFieldTypeEnum.master_detail == erpObjectFieldEntity.getFieldDefineType()) {
                        fieldDescribe.put(FieldDescribe.TARGET_API_NAME, erpObjectFieldEntity.getFieldExtendValue());
                    }
                    fieldDescribeMap.put(erpObjectFieldEntity.getFieldApiName(), fieldDescribe);
                }
                objectDescribe.setFields(fieldDescribeMap);
                objectDescribeList.add(objectDescribe);
            }
        }
        return objectDescribeList;
    }

    /**
     * 初始化对象和字段
     *
     * @param initErpObjectFieldsArg
     * @return
     */
    @Override
    @Transactional
    public Result<Void> initObjAndFields(InitErpObjectFieldsArg initErpObjectFieldsArg) {
        switch (initErpObjectFieldsArg.getChannel()) {
            case ERP_K3CLOUD:
                initK3CloudObjAndFields(initErpObjectFieldsArg);
                return Result.newSuccess();
            case ERP_JDY:
                initJdyCloudObjAndFields(initErpObjectFieldsArg);
                copySettingManager.initSyncPloyDetail(initErpObjectFieldsArg.getTargetTenantId());
                return Result.newSuccess();
            default:
                return Result.newError(ResultCodeEnum.UNSUPPORTED_CHANNEL);

        }
    }

    /**
     * 初始化K3字段配置，对象名不增加ei了。
     * 此处可优化为批量插入！
     *
     * @param initErpObjectFieldsArg
     */
    private void initK3CloudObjAndFields(InitErpObjectFieldsArg initErpObjectFieldsArg) {
        String defaultTenantId = StringUtils.defaultIfBlank(initErpObjectFieldsArg.getDefaultTenantId(), ConfigCenter.STORE_DEFAULT_K3_OBJ_TENANT_ID);
        String targetTenantId = initErpObjectFieldsArg.getTargetTenantId();
        String objApiName = initErpObjectFieldsArg.getObjApiName();
        String targetDcId=initErpObjectFieldsArg.getDataCenterId();//目标数据中心id
        ErpConnectInfoEntity connQuery=new ErpConnectInfoEntity();
        connQuery.setTenantId(targetTenantId);
        connQuery.setChannel(ErpChannelEnum.ERP_K3CLOUD);
        int dataCenterSeq = dataCenterManager.getDataCenterSeq(targetTenantId, targetDcId);
        String suffixName = "";
        if (dataCenterSeq != 0) {
            suffixName = "_" + dataCenterSeq;
        }
        if (!initErpObjectFieldsArg.getOnlyInitFields()){
            //复制渠道对应的默认配置对象
            copyObjs(defaultTenantId, targetTenantId,targetDcId, objApiName,suffixName,ErpChannelEnum.ERP_K3CLOUD);
            //复制渠道对应的默认配置对象对应关系
            copyObjRelation(defaultTenantId, targetTenantId,targetDcId, objApiName,suffixName,ErpChannelEnum.ERP_K3CLOUD);
        }
        //复制字段和字段扩展
        erpFieldManager.copyByEi(initErpObjectFieldsArg,suffixName);
    }
    /**
     * 初始化JDY字段配置，对象名不增加ei了。
     * 此处可优化为批量插入！
     *
     * @param initErpObjectFieldsArg
     */
    private void initJdyCloudObjAndFields(InitErpObjectFieldsArg initErpObjectFieldsArg) {
        String defaultTenantId = StringUtils.defaultIfBlank(initErpObjectFieldsArg.getDefaultTenantId(), ConfigCenter.STORE_DEFAULT_K3_OBJ_TENANT_ID);
        String targetTenantId = initErpObjectFieldsArg.getTargetTenantId();
        String objApiName = initErpObjectFieldsArg.getObjApiName();
        String targetDcId=initErpObjectFieldsArg.getDataCenterId();//目标数据中心id
        ErpConnectInfoEntity connQuery=new ErpConnectInfoEntity();
        connQuery.setTenantId(targetTenantId);
        connQuery.setChannel(initErpObjectFieldsArg.getChannel());
        int dataCenterSeq = dataCenterManager.getDataCenterSeq(targetTenantId, targetDcId);
        String suffixName = "";
        if (dataCenterSeq != 0) {
            suffixName = "_" + dataCenterSeq;
        }
        if (!initErpObjectFieldsArg.getOnlyInitFields()) {
            //复制渠道对应的默认配置对象
            copyObjs(defaultTenantId, targetTenantId, targetDcId, objApiName, suffixName, initErpObjectFieldsArg.getChannel());
            //复制渠道对应的默认配置对象对应关系
            copyObjRelation(defaultTenantId, targetTenantId, targetDcId, objApiName, suffixName, initErpObjectFieldsArg.getChannel());
        }
        //复制字段
        erpFieldManager.copyFieldByEi(defaultTenantId,targetTenantId,targetDcId,objApiName,suffixName,initErpObjectFieldsArg.getChannel());
    }
    private void copyObjRelation(String defaultTenantId, String targetTenantId,String targetDcId,String objApiName,String suffixName,ErpChannelEnum erpChannelEnum) {
        Long now = System.currentTimeMillis();
        ErpObjectRelationshipEntity erpObjectRelationshipEntity = new ErpObjectRelationshipEntity();
        erpObjectRelationshipEntity.setTenantId(defaultTenantId);
        erpObjectRelationshipEntity.setChannel(erpChannelEnum);
        if (objApiName != null) {
            erpObjectRelationshipEntity.setErpSplitObjectApiname(objApiName);
        }
        List<ErpObjectRelationshipEntity> erpObjectRelationshipEntities = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(defaultTenantId)).queryList(erpObjectRelationshipEntity);
        if (!erpObjectRelationshipEntities.isEmpty() && objApiName != null) {
            ErpObjectRelationshipEntity relation = erpObjectRelationshipEntities.get(0);
            if (relation.getSplitType().equals(ErpObjSplitTypeEnum.NOT_SPLIT)){
                //指定objApiName时还需要检查是否需要复制原始对象
                copyObjs(defaultTenantId, targetTenantId,targetDcId, relation.getErpRealObjectApiname(),suffixName,erpChannelEnum);
            }
        }
        List<ErpObjectRelationshipEntity> newRelations = BeanUtil.deepCopyList(erpObjectRelationshipEntities, ErpObjectRelationshipEntity.class);
        for (ErpObjectRelationshipEntity objectRelationshipEntity : newRelations) {
            objectRelationshipEntity.setTenantId(targetTenantId);
            objectRelationshipEntity.setDataCenterId(targetDcId);
            objectRelationshipEntity.setErpSplitObjectApiname(objectRelationshipEntity.getErpSplitObjectApiname()+suffixName);
            objectRelationshipEntity.setId(idGenerator.get());
            objectRelationshipEntity.setCreateTime(now);
            objectRelationshipEntity.setUpdateTime(now);
            erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(targetTenantId)).insert(objectRelationshipEntity);
        }
        erpObjectRelationshipDao.invalidCacheErpObj(targetTenantId,targetDcId);
    }

    private void copyObjs(String defaultTenantId, String targetTenantId,String targetDcId,String objApiName,String suffixName,ErpChannelEnum erpChannelEnum) {
        Long now = System.currentTimeMillis();
        ErpObjectEntity erpObjectEntity = new ErpObjectEntity();
        erpObjectEntity.setTenantId(defaultTenantId);
        erpObjectEntity.setChannel(erpChannelEnum);
        if (objApiName != null) {
            erpObjectEntity.setErpObjectApiName(objApiName);
        }
        List<ErpObjectEntity> erpObjectEntities = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(defaultTenantId)).queryList(erpObjectEntity);
        List<ErpObjectEntity> newErpObjectEntities = BeanUtil.deepCopyList(erpObjectEntities, ErpObjectEntity.class);
        for (ErpObjectEntity objectEntity : newErpObjectEntities) {
            objectEntity.setTenantId(targetTenantId);
            objectEntity.setDataCenterId(targetDcId);
            if(ErpObjectTypeEnum.SPLIT_OBJECT.name().equals(objectEntity.getErpObjectType().name())){
                objectEntity.setErpObjectApiName(objectEntity.getErpObjectApiName()+suffixName);
            }
            objectEntity.setId(idGenerator.get());
            objectEntity.setCreateTime(now);
            objectEntity.setUpdateTime(now);
            erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(targetTenantId)).insert(objectEntity);
        }
    }
}
