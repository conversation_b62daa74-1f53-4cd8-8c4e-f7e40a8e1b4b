package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NDownloadFile;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.fxiaoke.open.erpsyncdata.admin.arg.Send2LeXiangArg;
import com.fxiaoke.open.erpsyncdata.admin.model.NPathModel;
import com.fxiaoke.open.erpsyncdata.admin.model.lexiang.LeXiangDoc;
import com.fxiaoke.open.erpsyncdata.admin.model.lexiang.UploadAssetData;
import com.fxiaoke.open.erpsyncdata.admin.model.lexiang.result.GetAccessTokenResult;
import com.fxiaoke.open.erpsyncdata.admin.model.lexiang.result.GetSuperAdminListResult;
import com.fxiaoke.open.erpsyncdata.admin.service.LeXiangService;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ProxyHttpClient;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisCacheManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class LeXiangServiceImpl implements LeXiangService {
    @Autowired
    private I18NStringManager i18NStringManager;

    @Autowired
    private ProxyHttpClient proxyHttpClient;
    @Autowired
    private RedisCacheManager redisCacheManager;
    @Autowired
    private NFileStorageService nFileStorageService;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;

    @Override
    public Result<String> getAccessToken(String appKey, String appSecret) {
        String key = "lexiang_access_token_"+appKey;
        String accessToken = redisCacheManager.getCache(key,this.getClass().getSimpleName());
        if(StringUtils.isNotEmpty(accessToken)) {
            return Result.newSuccess(accessToken);
        }
        String url = "https://lxapi.lexiangla.com/cgi-bin/token";

        Map<String,String> body = new HashMap<>();
        body.put("grant_type","client_credentials");
        body.put("app_key",appKey);
        body.put("app_secret",appSecret);

        GetAccessTokenResult result = proxyHttpClient.postUrl(url,body,new HashMap<>(),new TypeReference<GetAccessTokenResult>(){
        });
        if(result!=null && StringUtils.isNotEmpty(result.getAccessToken())) {
            redisCacheManager.setCache(key,result.getAccessToken(),100 * 60L,this.getClass().getSimpleName());
        }
        return Result.newSuccess(result.getAccessToken());
    }

    @Override
    public GetSuperAdminListResult getSuperAdminList(String appKey, String appSecret) {
        String accessToken = getAccessToken(appKey, appSecret).getData();

        String url = "https://lxapi.lexiangla.com/cgi-bin/v1/contact/user/managers?access_token="+accessToken;

        GetSuperAdminListResult result = proxyHttpClient.getUrl(url,new HashMap<>(),new TypeReference<GetSuperAdminListResult>(){
        });

        return result;
    }

    @Override
    public Result<UploadAssetData> uploadAsset(String appKey,
                                               String appSecret,
                                               String type,
                                               Integer isPublic,
                                               String fileName,
                                               MediaType mediaType,
                                               byte[] data) {
        String accessToken = getAccessToken(appKey, appSecret).getData();

        String url = "https://lxapi.lexiangla.com/cgi-bin/v1/assets";

        Map<String,String> headerMap = new HashMap<>();
        headerMap.put("Authorization","Bearer "+accessToken);
        headerMap.put("x-staff-id","WuBeiBei");

        List<MultipartBody.Part> partList = new ArrayList<>();
        partList.add(MultipartBody.Part.createFormData("type",type));
        partList.add(MultipartBody.Part.createFormData("is_public",isPublic+""));
        partList.add(MultipartBody.Part.createFormData("file",fileName, RequestBody.create(mediaType,data)));

        UploadAssetData result = proxyHttpClient.postMultipartData(url,partList,headerMap,new TypeReference<UploadAssetData>(){
        });
        return Result.newSuccess(result);
    }

    @Override
    public Result<UploadAssetData> uploadAsset(String appKey,
                                               String appSecret,
                                               String type,
                                               Integer isPublic,
                                               String fileName,
                                               MediaType mediaType,
                                               String fsEa,
                                               String nPath) {
        NDownloadFile.Arg arg = new NDownloadFile.Arg();
        arg.setEa(fsEa);
        arg.setDownloadUser("E." + fsEa + ".1000");
        arg.setnPath(nPath);
        byte[] data = nFileStorageService.nDownloadFile(arg,fsEa).getData();
        return uploadAsset(appKey,appSecret,type,isPublic,fileName,mediaType,data);
    }

    @Override
    public Result<JSONObject> sendDoc(String appKey, String appSecret,LeXiangDoc doc) {
        log.info("LeXiangServiceImpl.sendDoc,doc={}",JSONObject.toJSONString(doc));

        String accessToken = getAccessToken(appKey, appSecret).getData();

        String url = "https://lxapi.lexiangla.com/cgi-bin/v1/docs?access_token="+accessToken;

        Map<String,String> headerMap = new HashMap<>();
        headerMap.put("x-staff-id","WuBeiBei");

        JSONObject result = proxyHttpClient.postUrl(url,doc,headerMap,new TypeReference<JSONObject>(){
        });

        return Result.newSuccess(result);
    }

    @Override
    public Result<JSONObject> sendHtmlWorkOrder(String appKey,
                                                String appSecret,
                                                String fsEa,
                                                String title,
                                                String html,
                                                List<NPathModel> nPathList) {
        html = process(html,nPathList);

        for(NPathModel nPath : nPathList) {
            Result<UploadAssetData> result = uploadAsset(appKey,
                    appSecret,
                    "image",
                    1,
                    nPath.getFilename(),
                    MediaType.parse("image/jpeg"),
                    fsEa,
                    nPath.getPath());
            if(result.getData()==null || StringUtils.isEmpty(result.getData().getUrl())) {
                log.info("LeXiangServiceImpl.sendWorkOrder,uploadAsset failed,nPath={}",nPath);
                //return Result.newError(ResultCodeEnum.UPLOAD_ASSET_2_LEXIANG_FAILED);
                continue;
            }
            html = html.replace("{"+nPath.getPath()+"}",result.getData().getUrl());
        }

        LeXiangDoc.LeXiangData.Attributes attributes = new LeXiangDoc.LeXiangData.Attributes();
        attributes.setTitle(title);
        attributes.setIs_markdown(0);
        attributes.setContent(html);
        attributes.setSource("original");
        attributes.setSignature(LeXiangDoc.LeXiangData.Attributes.DefaultSignature);
        attributes.setOnly_team(0);
        attributes.setAllow_comment(1);
        attributes.setPrivilege_type(0);

        LeXiangDoc.LeXiangData.Relationships relationships = new LeXiangDoc.LeXiangData.Relationships();
        relationships.setCategory(new LeXiangDoc.LeXiangData.Category());
        relationships.getCategory().setData(new LeXiangDoc.LeXiangData.DataItem());
        relationships.getCategory().getData().setType("category");
        relationships.getCategory().getData().setId("07b01178ab3e11ec912ba2819bba1252");

        relationships.setTeam(new LeXiangDoc.LeXiangData.Team());
        relationships.getTeam().setData(new LeXiangDoc.LeXiangData.DataItem());
        relationships.getTeam().getData().setType("team");
        relationships.getTeam().getData().setId("38288e426fc011ed942066b2030f1ad7");


        relationships.setDirectory(new LeXiangDoc.LeXiangData.Directory());
        relationships.getDirectory().setData(new LeXiangDoc.LeXiangData.DataItem());
        relationships.getDirectory().getData().setType("directory");
        relationships.getDirectory().getData().setId("ef2c556cfae211ed91eeb2bb2aa0e678");


        LeXiangDoc doc = new LeXiangDoc();
        doc.setData(new LeXiangDoc.LeXiangData());
        doc.getData().setType("doc");
        doc.getData().setAttributes(attributes);
        doc.getData().setRelationships(relationships);

        return sendDoc(appKey,appSecret,doc);
    }

    private String process(String html,List<NPathModel> nAllPathList) {

        List<String> nPathList = new ArrayList<>();

        int pos = html.indexOf("N_");
        while (pos!=-1) {
            String npath = html.substring(pos,pos+44);
            String endChar = html.substring(pos+44,pos+45);
            if(StringUtils.equalsIgnoreCase(endChar,"}")) {
                pos+=44;
                pos = html.indexOf("N_",pos);
                continue;
            }
            String strDate = npath.substring("N_".length(),8);
            try {
                Integer date = Integer.valueOf(strDate);
            } catch (Exception e) {
                pos+=44;
                pos = html.indexOf("N_",pos);
                continue;
            }
            nPathList.add(npath);
            pos+=44;
            pos = html.indexOf("N_",pos);
        }

        List<String> nPathUrlFormatList = tenantConfigurationManager.getNPathUrlFormat();
        log.info("LeXiangServiceImpl.process,nPathUrlFormatList={}",nPathUrlFormatList);

        if(CollectionUtils.isNotEmpty(nPathList)) {
            for(String nPath : nPathList) {
                NPathModel nPathModel = new NPathModel();
                nPathModel.setPath(nPath);
                nPathModel.setFilename(nPath+".jpg");
                nPathModel.setExt("jpg");
                nPathModel.setSize(0);
                nAllPathList.add(nPathModel);

                for(String nPathUrlFormat : nPathUrlFormatList) {
                    String nPathUrl = nPathUrlFormat.replace("{npath}",nPath);
                    log.info("LeXiangServiceImpl.process,nPathUrl={}",nPathUrl);
                    if(html.indexOf(nPathUrl)>=0) {
                        log.info("LeXiangServiceImpl.process,nPath matched,nPathUrl={}",nPathUrl);
                        html = html.replace(nPathUrl,"{"+nPath+"}");
                        log.info("LeXiangServiceImpl.process,html={}",html);
                    }
                }
            }
        }
        return html;
    }

//    public static void main(String[] args) {
//        String html = "<h2>【问题现象和操作步骤】</h2><p>1.客户在crm新建预测单，已经传到金蝶之后，返回值提示更新中间记录表失败；写接口调用中是有成功返回数据的；\\n2.预测单同步到ERP，状态按已审核状态</p><br/><h2>【问题图片】</h2><img src='{N_202303_07_ef1dc3cb046140f896241389d0521f33}'/><img src='{N_202303_07_74cb9ab93a5f4b7384645d4094da74db}'/><img src='{N_202303_07_96d7cff1b04d42aab98653b85ea3b3c4}'/><h2>【区域排查问题原因】</h2><p>问题1：金蝶接口已调用成功，但记录中间表时报错。排查中间表并没有重复的ID，需研发排查。\\n问题2：需研发调整</p><h2>【总部排查问题原因】</h2><p>问题1：Detail: Key (tenant_id, source_object_api_name, dest_object_api_name, dest_data_id)=(714544, object_OV16a__c, PLN_FORECAST.PLN_FORECASTENTRY, ) already exists。客户之前建立了PLN_FORECAST，后来删掉。又用工具刷了一下该对象，判断是数据库中没有删除干净。需要研发排查。\\n问题2请研发配置。</p><h2>【研发填写问题原因】</h2><p>从对象id字段的saveCode大小写问题</p><h2>【研发排查问题原因】</h2><p><p style=\\\"margin: 0px;\\\"><img src=\\\"https://www.fxiaoke.com/FSC/EM/File/GetByPath?path=N_202303_10_a8bb6dc55249438eae7217fb7aacf401.jpg&amp;filetype=webp\\\" width=\\\"485\\\" height=\\\"316\\\" style=\\\"width: 485px; height: 316px;\\\"></p>\\n<p style=\\\"margin: 0px;\\\"><img src=\\\"https://www.fxiaoke.com/FSC/EM/File/GetByPath?path=N_202303_10_e352f86261474357a1b51ee4123c8309.jpg&amp;filetype=webp\\\" width=\\\"534\\\" height=\\\"320\\\" style=\\\"width: 534px; height: 320px;\\\"></p>\\n<p style=\\\"margin: 0px;\\\"><br></p></p><h2>【来源】</h2><p>工单编码：GD-20230307-10818</p>";
//        if(html.indexOf("https://www.fxiaoke.com/FSC/EM/File/GetByPath?path=N_202303_10_e352f86261474357a1b51ee4123c8309.jpg&amp;filetype=webp")>=0) {
//            System.out.println("ok");
//        }
//        System.out.println(html);
//    }
}
