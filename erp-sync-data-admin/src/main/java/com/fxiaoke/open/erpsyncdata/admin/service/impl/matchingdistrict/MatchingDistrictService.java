package com.fxiaoke.open.erpsyncdata.admin.service.impl.matchingdistrict;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ExcelSystemFieldTypeEnum;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/6/28 14:12:17
 */ // 暂时不用crm接口,要求必须要有"parentCode",中国只能查到省级,省只能查到市级
public abstract class MatchingDistrictService {

    private I18NStringManager i18NStringManager;
    private String tenantId;
    private String lang;

    public MatchingDistrictService(I18NStringManager i18NStringManager, String tenantId, String lang) {
        this.i18NStringManager = i18NStringManager;
        this.tenantId = tenantId;
        this.lang = lang;
    }

    protected abstract I18NStringEnum matchingI18nKey();

    public abstract Pair<String, String> matchingAndRemoveCrmData(final String erpDataName, Map<String, String> crmData);


    public void init(final ExcelSystemFieldTypeEnum excelSystemFieldTypeEnum, final Map<String, String> crmData) {
    }


    public Triple<String, String, String> matchingWithRemoveCrmData(final String erpDataName, Map<String, String> crmData) {
        final Pair<String, String> pair = matchingAndRemoveCrmData(erpDataName, crmData);
        if (Objects.isNull(pair)) {
            return null;
        }
        final String matching = i18NStringManager.get(matchingI18nKey(), lang, tenantId);
        return Triple.of(pair.getKey(), pair.getValue(), matching);
    }


}
