package com.fxiaoke.open.erpsyncdata.admin.service.superadmin;

import com.fxiaoke.open.erpsyncdata.admin.model.SendMsgHelper;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

/**
 * 运维工具，供实施使用
 *
 * <AUTHOR> (^_−)☆
 * @date 2022/4/24
 */
public interface OpsToolService {

    /**
     * 全量同步临时库未同步或未成功的数据
     *
     * @param ltId
     * @param gtId 最小id
     */
    Result<Void> syncAllNoTriggerErpTemp(String tenantId, String dcId, String realObjApiName,String ltId, String gtId, SendMsgHelper sendMsgHelper);


    /**
     * 扫描临时库未触发的数据
     *
     * @param tenantId
     * @param startTime
     * @param endTime
     * @return 是否发送告警
     */
    Result<Integer> scanErpTempBetweenId(String tenantId, Long startTime, Long endTime);
}
