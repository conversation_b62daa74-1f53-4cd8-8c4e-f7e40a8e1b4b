package com.fxiaoke.open.erpsyncdata.admin.service.superadmin;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.FileManager;
import com.fxiaoke.open.erpsyncdata.admin.manager.analyse.AllConnectorInfoManager;
import com.fxiaoke.open.erpsyncdata.admin.manager.analyse.AllStreamInfoManager;
import com.fxiaoke.open.erpsyncdata.admin.manager.analyse.AllTenantInfoManager;
import com.fxiaoke.open.erpsyncdata.admin.model.amis.Amis;
import com.fxiaoke.open.erpsyncdata.admin.model.fstool.SystemSearch;
import com.fxiaoke.open.erpsyncdata.admin.model.superadmin.AdminTenantInfo;
import com.fxiaoke.open.erpsyncdata.admin.utils.ExcelUtils;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpFieldExtendDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ConfigRouteManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.AdminConnectorInfo;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.AdminStreamQuery;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.StreamSimpleInfo;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.DelayDataNodeMsgDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.SyncFailedStatDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.DataNodeMsgDoc;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.dbproxy.remote.service.UserCenterService;
import com.fxiaoke.open.erpsyncdata.dbproxy.table.dao.ErpTableDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ParallelUtils;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TenantEnvUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendTextNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.model.config.SyncFailedStat;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.ScanParams;
import redis.clients.jedis.ScanResult;

import java.io.ByteArrayOutputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.fxiaoke.open.erpsyncdata.admin.model.superadmin.AdminTenantInfo.Fields.*;
import static com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpSyncDataBackStageEnvironmentEnum.*;

@Service
@Slf4j
public class SuperAdminServiceImpl implements SuperAdminService {
    @Autowired
    private ErpFieldExtendDao erpFieldExtendDao;
    @Autowired
    private ErpTableDao erpTableDao;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private AllTenantInfoManager allTenantInfoManager;
    @Autowired
    private AllConnectorInfoManager allConnectorInfoManager;
    @Autowired
    private AllStreamInfoManager allStreamInfoManager;
    @Autowired
    private SyncFailedStatDao syncFailedStatDao;
    @Autowired
    private ConfigRouteManager configRouteManager;
    @Autowired
    private DelayDataNodeMsgDao delayDataNodeMsgDao;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private FileManager fileManager;
    @Autowired
    private UserCenterService userCenterService;

    private static SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");


    @Override
    public Result<List<Map<String, Object>>> redoIndex(String tenantId) {
        String sql = "SELECT indexname ,indexdef,tablename  FROM pg_indexes WHERE indexname like '%s_d_source_name' and indexdef like '%UNIQUE%' limit 10000;";
        List<Map<String, Object>> results = erpFieldExtendDao.setTenantId(tenantId).superQuerySql(sql, tenantId);//设置tenantId路由到某个具体库
        String dropIndex = "drop index CONCURRENTLY %s";
        for (Map<String, Object> result : results) {
            try {
                String dropIndexSql = String.format(dropIndex, result.get("indexname"));
                String tableName = result.get("tablename").toString();
                String indexName = result.get("indexname").toString();
                erpTableDao.setTenantId(tenantId).superUpdateSql(dropIndexSql);
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                //重新创建索引
                //String createFormat="create  index concurrently   %s on %s (tenant_id, source_object_api_name, dest_object_api_name, dest_data_name,update_time desc) where is_deleted = false;";
                String createFormatSql = result.get("indexdef").toString();
                createFormatSql = createFormatSql.replace("UNIQUE INDEX", "index concurrently");
                erpTableDao.setTenantId(tenantId).superUpdateSql(createFormatSql);
                log.info("createFormatSql{}", createFormatSql);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        redoIndex2(tenantId);
        return null;
    }

    public Result<List<Map<String, Object>>> redoIndex2(String tenantId) {
        String sql = "SELECT indexname ,indexdef,tablename  FROM pg_indexes WHERE indexname like '%s_d_dest_name' and indexdef like '%UNIQUE%' limit 10000;";
        List<Map<String, Object>> results = erpFieldExtendDao.setTenantId(tenantId).superQuerySql(sql, tenantId);//设置tenantId路由到某个具体库
        String dropIndex = "drop index CONCURRENTLY %s";
        for (Map<String, Object> result : results) {
            try {
                String dropIndexSql = String.format(dropIndex, result.get("indexname"));
                String tableName = result.get("tablename").toString();
                String indexName = result.get("indexname").toString();
                erpTableDao.setTenantId(tenantId).superUpdateSql(dropIndexSql);
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                //重新创建索引
                //String createFormat="create  index concurrently   %s on %s (tenant_id, source_object_api_name, dest_object_api_name, dest_data_name,update_time desc) where is_deleted = false;";
                String createFormatSql = result.get("indexdef").toString();
                createFormatSql = createFormatSql.replace("UNIQUE INDEX", "index concurrently");
                erpTableDao.setTenantId(tenantId).superUpdateSql(createFormatSql);
                log.info("createFormatSql{}", createFormatSql);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    @Override
    public Result<Void> superUpdate(String tenantId, String sql) {
        erpFieldExtendDao.setTenantId(tenantId).superUpdateSql(sql);//设置tenantId路由到某个具体库
        return Result.newSuccess();
    }

    /**
     * @param requestParam 精确
     * @return
     */
    @Override
    public Result<Amis.Crud<AdminTenantInfo>> tenantInfoQuery(Map<String, String> requestParam) {
        //不能做修改操作，否则缓存数据会同时移除，导致异常！！！！
        List<AdminTenantInfo> allInfoList = allTenantInfoManager.getAllAdminTenantInfoList();
        //实时修正环境信息
        fixEnv(allInfoList);
        List<AdminTenantInfo> filteredList = new ArrayList<>(allInfoList);
        //筛选
        filterTenantInfos(requestParam, filteredList);
        int total = filteredList.size();
        //全量，用于导出
        Boolean all = Convert.toBool(requestParam.get("all"), false);
        if (!all) {
            //分页
            int perPage = Convert.toInt(requestParam.get("perPage"), 100);
            int page = Convert.toInt(requestParam.get("page"), 1);
            filteredList = ListUtil.page(page - 1, perPage, filteredList);
        }
        //构建columns
        Amis.ColHelper<AdminTenantInfo> colHelper = Amis.ColHelper.parse(AdminTenantInfo.class, filteredList, new Amis.Col().sortable(false));
        Map<String, List<String>> db2Tenants = configRouteManager.getDb2Tenants();
        SystemSearch.InfoHolder infoHolder = allTenantInfoManager.getInfoHolder(false);
        colHelper.get(enterpriseAccount)
                .order(1)
                .fixed("left")
                .filterable2(Lists.newArrayList("普通企业", "测试企业", "沙盒企业", "notFound"), true);   // ignoreI18n   实施和开发自用
        colHelper.get(systemNames).toggled(false).filterable2(infoHolder.getSystemNameKey2DcId().keySet(), true);
        colHelper.get(connectorKeys).toggled(false).filterable2(infoHolder.getConnectorKey2DcId().keySet(), true);
        colHelper.get(tenantEnv).filterable2(CollUtil.newArrayList(ErpSyncDataBackStageEnvironmentEnum.values()), true);
        colHelper.get(pgRoute).toggled(false).filterable2(db2Tenants.keySet(), true);
        colHelper.get(runStatus).toggled(false).filterable2(Lists.newArrayList(-1, 1, 2, 3, 4, 5), true);
        colHelper.get(enterpriseLevel).filterable2(allInfoList.stream().flatMap(v -> CollUtil.emptyIfNull(v.getEnterpriseLevel()).stream())
                .collect(Collectors.toSet()), true);
        Amis.Crud<AdminTenantInfo> crud = colHelper.getCrud();
        crud.setTotal(total);
        return Result.newSuccess(crud);
    }

    private static void fixEnv(List<AdminTenantInfo> allInfoList) {
        for (AdminTenantInfo adminTenantInfo : allInfoList) {
            ErpSyncDataBackStageEnvironmentEnum env = TenantEnvUtil.getTenantAllModelEnv(adminTenantInfo.getTenantId());
            if (!env.equals(adminTenantInfo.getTenantEnv())) {
                adminTenantInfo.setTenantEnv(env);
            }
        }
    }

    private static void filterTenantInfos(Map<String, String> requestParam, List<AdminTenantInfo> filteredList) {
        //环境
        String tenantEnvFilter = requestParam.get(tenantEnv);
        if (StrUtil.isNotEmpty(tenantEnvFilter)) {
            Set<ErpSyncDataBackStageEnvironmentEnum> filterEnvs = StrUtil.split(tenantEnvFilter, ",")
                    .stream()
                    .map(v -> ErpSyncDataBackStageEnvironmentEnum.valueOf(v))
                    .collect(Collectors.toSet());
            filteredList.removeIf(v -> !filterEnvs.contains(v.getTenantEnv()));
        }
        // 企业名称
        String eaFilter = requestParam.get(enterpriseAccount);
        if (StrUtil.isNotBlank(eaFilter)) {
            Set<String> eaFilters = new HashSet(StrUtil.split(eaFilter, ","));
            if (!eaFilters.contains("普通企业")) {   // ignoreI18n   实施和开发自用
                filteredList.removeIf(v -> !(StrUtil.endWithIgnoreCase(v.getEnterpriseName(), "_sandbox")
                        || StrUtil.startWith(v.getEnterpriseName(), "fktest") || "notFound".equals(v.getEnterpriseName())));
            }
            if (!eaFilters.contains("沙盒企业")) {   // ignoreI18n   实施和开发自用
                filteredList.removeIf(v -> StrUtil.endWithIgnoreCase(v.getEnterpriseName(), "_sandbox"));
            }
            if (!eaFilters.contains("测试企业")) {   // ignoreI18n   实施和开发自用
                filteredList.removeIf(v -> StrUtil.startWith(v.getEnterpriseName(), "fktest"));
            }
            if (!eaFilters.contains("notFound")) {
                filteredList.removeIf(v -> "notFound".equals(v.getEnterpriseName()));
            }
        }
        //企业级别
        String levelFilter = requestParam.get(enterpriseLevel);
        if (StrUtil.isNotEmpty(levelFilter)) {
            filteredList.removeIf(v -> !CollUtil.containsAny(v.getEnterpriseLevel(), StrUtil.split(levelFilter, ",")));
        }
        //运行状态
        String runStatusFilter = requestParam.get(runStatus);
        if (StrUtil.isNotEmpty(runStatusFilter)) {
            Set<Integer> runStatusFilters = StrUtil.split(runStatusFilter, ",")
                    .stream().map(v -> Integer.valueOf(v)).collect(Collectors.toSet());
            filteredList.removeIf(v -> !runStatusFilters.contains(v.getRunStatus()));
        }
        //企业路由
        String pgRouteFilter = requestParam.get(pgRoute);
        if (StrUtil.isNotEmpty(pgRouteFilter)) {
            filteredList.removeIf(v -> !CollUtil.contains(StrUtil.split(pgRouteFilter, ","), v.getPgRoute()));
        }

        //系统名称
        String systemNamesFilter = requestParam.get(systemNames);
        if (StrUtil.isNotEmpty(systemNamesFilter)) {
            filteredList.removeIf(v -> !CollUtil.containsAny(v.getSystemNames(), StrUtil.split(systemNamesFilter, ",")));
        }

        //连接器
        String connectorKeysFilter = requestParam.get(connectorKeys);
        if (StrUtil.isNotEmpty(connectorKeysFilter)) {
            filteredList.removeIf(v -> !CollUtil.containsAny(v.getConnectorKeys(), StrUtil.split(connectorKeysFilter, ",")));
        }

        //搜索
        String keywords = requestParam.get("keywords");
        if (StrUtil.isNotEmpty(keywords)) {
            Set<String> keywordsSet = new HashSet<>(StrUtil.splitTrim(keywords, ","));
            //正则匹配
            filteredList.removeIf(v -> {
                if (keywordsSet.contains(v.getTenantId())) {
                    return false;
                }
                for (String keywords1 : keywordsSet) {
                    if (StrUtil.containsIgnoreCase(v.getEnterpriseAccount(), keywords1)) {
                        return false;
                    }
                    if (StrUtil.containsIgnoreCase(v.getEnterpriseName(), keywords1)) {
                        return false;
                    }
                }
                return true;
            });
        }
    }

    @Override
    public Result<Amis.Crud<AdminConnectorInfo>> connectorInfoQuery(Map<String, String> requestParam) {
        int total;
        if (Convert.toBool(requestParam.get("refresh"), false)) {
            // 刷新缓存会初始化页面配置，故不进行筛选，仅分页
            total = allConnectorInfoManager.refreshCache();
        } else {
            total = allConnectorInfoManager.getTotal(requestParam);
        }
        List<AdminConnectorInfo> allInfoList;
        if (Convert.toBool(requestParam.get("all"), false)) {
            allInfoList = allConnectorInfoManager.getAllAdminConnectorInfoList();
        } else {
            allInfoList = allConnectorInfoManager.getAllAdminConnectorInfoList(requestParam);
        }
        //构建columns
        SystemSearch.InfoHolder infoHolder = allTenantInfoManager.getInfoHolder(false);
        Amis.ColHelper<AdminConnectorInfo> colHelper = Amis.ColHelper.parse(AdminConnectorInfo.class, allInfoList, new Amis.Col().sortable(false));
        colHelper.get(AdminConnectorInfo.Fields.mongoLastSyncTime).hidden();
        colHelper.get(AdminConnectorInfo.Fields.connectorKey).filterable2(infoHolder.getConnectorKey2DcId().keySet(), true);
        colHelper.get(AdminConnectorInfo.Fields.enterpriseType).filterable2(Lists.newArrayList("sanbox", "normal", "test"), true);
        colHelper.get(AdminConnectorInfo.Fields.connectorCreateTime).unixTimeStyle();
        Amis.Crud<AdminConnectorInfo> crud = colHelper.getCrud();
        crud.setTotal(total);
        return Result.newSuccess(crud);
    }

    /**
     * 筛选分页，返回筛选后的元素总数，再修改infos进行分页
     * 因为不再返回全量数据，故已弃用
     * @param infos
     * @param filters
     * @return
     */
    @Deprecated
    private int filterConnectorInfos(List<AdminConnectorInfo> infos, Map<String, String> filters) {
        // 筛选过滤，多个条件用逗号分隔
        String eaFilter = filters.get("enterpriseAccount");
        if (StrUtil.isNotEmpty(eaFilter)) {
            Set<String> eaFilters = new HashSet<>(StrUtil.split(eaFilter, ","));
            infos.removeIf(v -> !eaFilters.contains(v.getEnterpriseAccount()));
        }
        String connectorKeyFilter = filters.get("connectorKey");
        if (StrUtil.isNotEmpty(eaFilter)) {
            Set<String> connectorKeyFilters = new HashSet<>(StrUtil.split(connectorKeyFilter, ","));
            infos.removeIf(v -> !connectorKeyFilters.contains(v.getConnectorKey()));
        }
        int total = infos.size();
        //分页
        int perPage = Convert.toInt(filters.get("perPage"), 100);
        int page = Convert.toInt(filters.get("page"), 1);
        infos = ListUtil.page(page - 1, perPage, infos);
        return total;
    }

    @Override
    public Result<Amis.Crud<StreamSimpleInfo>> streamInfoQuery(Map<String,String> requestParam) {
        List<StreamSimpleInfo> allInfoList = new ArrayList<>();
        int total = 0;
        if (Convert.toBool(requestParam.get("refresh"), false)) {
            // 刷新缓存会初始化页面配置，故不进行筛选，仅分页
            ParallelUtils.createBackgroundTask().submit(() -> {
                // 因为处理太慢，异步处理刷缓存的操作
                allStreamInfoManager.refreshCache();
            }).run();
        } else {
            AdminStreamQuery query = obtainQuery(requestParam);
            allInfoList = allStreamInfoManager.getAllAdminStreamInfoList(query);
            total = allStreamInfoManager.getTotal(query);
        }
        //构建columns
        SystemSearch.InfoHolder infoHolder = allTenantInfoManager.getInfoHolder(false);
        Amis.ColHelper<StreamSimpleInfo> colHelper = Amis.ColHelper.parse(StreamSimpleInfo.class, allInfoList, new Amis.Col().sortable(false));
        colHelper.get(StreamSimpleInfo.Fields.mongoLastSyncTime).hidden();
        colHelper.get(StreamSimpleInfo.Fields.erpDcId).hidden();
        colHelper.get(StreamSimpleInfo.Fields.connectorKey).filterable2(infoHolder.getConnectorKey2DcId().keySet(), true);
        colHelper.get(StreamSimpleInfo.Fields.status).filterable2(Lists.newArrayList(-1, 1, 2), true);
        Amis.Crud<StreamSimpleInfo> crud = colHelper.getCrud();
        crud.setTotal(total);
        return Result.newSuccess(crud);
    }

    /**
     * 将全量数据形成excel上传，发送企信通知
     * @return
     */
    @Override
    public Result<Void> exportToExcel(String tenantId, Integer userId, String ea) {
        List<StreamSimpleInfo> infoList = allStreamInfoManager.getAllAdminStreamInfoList();
        if (CollectionUtils.isEmpty(infoList)) {
            return Result.newSystemError(I18NStringEnum.s3669);
        }
        ParallelUtils.createBackgroundTask().submit(() -> {
            //写excel
            String tnPath = null;
            String errMsg = "";
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                HorizontalCellStyleStrategy styleStrategy = ExcelUtils.getDefaultStyle();
                ExcelWriter excelWriter = EasyExcel.write(outputStream, infoList.get(0).getClass())
                        .registerWriteHandler(styleStrategy)
                        .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                        .build();
                WriteSheet writeSheet = EasyExcel.writerSheet().build();
                excelWriter.write(infoList, writeSheet);
                excelWriter.finish();
                //上传文件系统
                tnPath = fileManager.uploadTnFile(ea, CommonConstant.SUPER_ADMIN_USER, outputStream.toByteArray(),null);
                log.info("upload excel, result:{}", tnPath);
            } catch (Exception e) {
                errMsg = e.getMessage();
                log.info("exportToExcel error:", e);
            }
            SendTextNoticeArg arg = new SendTextNoticeArg();
            arg.setTenantId(tenantId);
            //        arg.setDataCenterId(dataCenterId);
            arg.setReceivers(Collections.singletonList(userId));
            if (StrUtil.isNotBlank(tnPath)) {
                arg.setMsgTitle(i18NStringManager.get(I18NStringEnum.s209, null, tenantId));
                String previewUrl = String.format(userCenterService.getPreviewFilePathFormat(ea), tnPath+".txt");
                arg.setMsg(i18NStringManager.get(I18NStringEnum.s2401,null,tenantId) + previewUrl);
            } else {
                arg.setMsgTitle(i18NStringManager.get(I18NStringEnum.s208, null, tenantId));
                arg.setMsg(errMsg);
            }
            notificationService.sendErpSyncDataAppNotice(arg,
                                                         AlarmRuleType.OTHER,
                                                         AlarmRuleType.OTHER.getName(i18NStringManager,null,tenantId),
                                                         AlarmType.OTHER,
                                                         AlarmLevel.GENERAL);
        }).run();
        return Result.newSuccess();
    }

    /**
     * 处理全量查询集成流的requestParam
     * @param requestParam
     * @return
     */
    private AdminStreamQuery obtainQuery(Map<String, String> requestParam) {
        AdminStreamQuery filters = new AdminStreamQuery();
        filters.setPerPage(Convert.toInt(requestParam.get("perPage"), 50));
        filters.setPage(Convert.toInt(requestParam.get("page"), 1));
        filters.setEa(requestParam.get("enterpriseAccounts"));
        filters.setConnectorKey(requestParam.get("connectorKey"));
        filters.setCrmObjApiName(requestParam.get("crmObjApiNames"));
        filters.setStatus(requestParam.get("status"));
        return filters;
    }

    @Override
    public Result<List<Dict>> streamInfoQuery(String tenantId, String keywords) {
        if (StrUtil.isBlank(tenantId)) {
            return Result.newSystemError(I18NStringEnum.s3670);
        }
        List<Dict> infos = new ArrayList<>();
        List<SyncPloyDetailEntity> ployDetails = adminSyncPloyDetailDao.setGlobalTenant(tenantId).listByTenantId(tenantId);
        MergeJedisCmd jedisCmd = redisDataSource.get(this.getClass().getSimpleName());
        //获取连续异常轮询的redis key，不用keys避免阻塞redis
        Set<String> pollingErrorKeys = scanKeys(jedisCmd, String.format(CommonConstant.REDIS_KEY_POLLING_CONTINUE_FAILED_COUNT, tenantId + "*"));
        if (StrUtil.isNotBlank(keywords)) {
            String[] split = keywords.split("\\|\\|");
            ployDetails.removeIf(v ->
                    !StrUtil.containsAnyIgnoreCase(v.getIntegrationStreamName(), split) &&
                            !StrUtil.containsAnyIgnoreCase(v.getSourceObjectApiName(), split) &&
                            !StrUtil.containsAnyIgnoreCase(v.getDestObjectApiName(), split) &&
                            !StrUtil.containsAnyIgnoreCase(v.getId(), split));
        }
        //轮询任务执行中
        Set<String> rollingErpKeys = scanKeys(jedisCmd, String.format(CommonConstant.REDIS_LOCK_PROBE_ERP, tenantId, ""));
        for (SyncPloyDetailEntity ployDetail : ployDetails) {
            Dict info = new Dict();
            String erpDcId = ployDetail.getSourceTenantType().equals(TenantType.ERP) ? ployDetail.getSourceDataCenterId() : ployDetail.getDestDataCenterId();
            String ployDetailId = ployDetail.getId();
            infos.add(info);
            info.put("streamId", ployDetailId);
            info.put("erpDcId", erpDcId);
            info.put("streamName", ployDetail.getIntegrationStreamName());
            info.put("enable", SyncPloyDetailStatusEnum.ENABLE.getStatus().equals(ployDetail.getStatus()));
            info.put("sourceObjectApiName", ployDetail.getSourceObjectApiName());
            info.put("destObjectApiName", ployDetail.getDestObjectApiName());
            SyncFailedStat syncFailedStat = syncFailedStatDao.getSyncFailedStat(tenantId, erpDcId, ployDetailId);
            if (syncFailedStat != null) {
                info.put("lastBreakTime", syncFailedStat.getLastBreakTime());
                info.put("lastCountTime", syncFailedStat.getLastCountTime());
                info.put("lastCount", syncFailedStat.getLastCount());
            }
            //预估增量
            String incrFailedTopic = getIncrFailedTopic(tenantId, ployDetailId);
            String estimatedValue = jedisCmd.get(incrFailedTopic);
            if (estimatedValue != null) {
                info.put("currentCount", estimatedValue);
            }
            //连续异常轮询
            List<String> keys = pollingErrorKeys.stream().filter(v -> v.startsWith(String.format(CommonConstant.REDIS_KEY_POLLING_CONTINUE_FAILED_COUNT, tenantId + "-" + ployDetail.getSourceObjectApiName()))).collect(Collectors.toList());
            if (!keys.isEmpty()) {
                Dict pollingFailedInfo = new Dict();
                info.put("pollingFailedInfo", pollingFailedInfo);
                for (String key : keys) {
                    String value = jedisCmd.get(key);
                    pollingFailedInfo.put(key, value);
                }
            }
            //是否正在轮询
            String lockKey = String.format(CommonConstant.REDIS_LOCK_PROBE_ERP, tenantId, ployDetail.getSourceObjectApiName());
            info.put("isRollingErp", rollingErpKeys.contains(lockKey));
        }
        return Result.newSuccess(infos);
    }

    @Override
    public Result<List<Dict>> dataNodeQuery(Boolean isTop60, String tenantId, String objApiName, String dataId) {
        List<DataNodeMsgDoc> dataNodeMsgDocs = delayDataNodeMsgDao.listDataNodeMsg(isTop60, tenantId, objApiName, dataId, 500, 0);
        List<Dict> infos = Lists.newArrayList();
        for (DataNodeMsgDoc doc : dataNodeMsgDocs) {
            Dict info = new Dict();
            infos.add(info);
            info.put("id", doc.getId().toString());
            info.put("isTop60", doc.getIsTop60());
            info.put("tenantId", doc.getTenantId());
            info.put("objApiName", doc.getObjApiName());
            info.put("dataId", doc.getDataId());
            info.put("streamId", doc.getStreamId());
            info.put("nodeNames", doc.getNodeNames());
            info.put("nodeTimes", doc.getNodeTimes());
            info.put("createTime", dateFormat.format(doc.getCreateTime().getTime()));
        }
        return Result.newSuccess(infos);
    }

    public String getIncrFailedTopic(String tenantId, String ployDetailId) {
        Joiner joiner = Joiner.on("-").skipNulls();
        String name = joiner.join(tenantId, ployDetailId);
        String incrementTopic = String.format(CommonConstant.REDIS_KEY_FAILED_SYNC_INCREMENT, name);
        return incrementTopic;
    }

    private Set<String> scanKeys(MergeJedisCmd jedisCmd, String keyPattern) {
        ScanParams scanParams = new ScanParams();
        scanParams.match(keyPattern);
        scanParams.count(10000);
        String cursor = ScanParams.SCAN_POINTER_START;
        Set<String> keys = new HashSet<>();
        for (int i = 0; i < 20; i++) {
            //最多扫20次吧
            ScanResult<String> scanResult = jedisCmd.scan(cursor, scanParams);
            keys.addAll(scanResult.getResult());
            cursor = scanResult.getCursor();
            if (cursor.equals(ScanParams.SCAN_POINTER_START)) {
                break;
            }
        }
        return keys;
    }
}
