package com.fxiaoke.open.erpsyncdata.admin.task;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.common.concurrent.DynamicExecutors;
import com.fxiaoke.open.erpsyncdata.admin.manager.SwitchStreamStatusManager;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncPloyDetailResult;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncPloyDetailService;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.RelationErpShardStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpSyncTimeDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.RelationErpShardDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.RelationErpShardEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.TemplatePloyDetailChangeDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.TemplatePloyDetailChangeEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.util.Objects;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/3/11 14:05:26
 *
 * 处理集成代管变更后动作
 */
@Component
@Slf4j
public class TemplatePloyDetailChangeScheduleService implements InitializingBean {

    @Autowired
    private TemplatePloyDetailChangeDao templatePloyDetailChangeDao;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private MigrateTableManager migrateTableManager;
    @Autowired
    private SyncPloyDetailSnapshotManager syncPloyDetailSnapshotManager;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private SwitchStreamStatusManager switchStreamStatusManager;
    @Autowired
    private AdminSyncPloyDetailService adminSyncPloyDetailService;
    @Autowired
    private ErpSyncTimeDao erpSyncTimeDao;
    @Autowired
    private RelationErpShardDao relationErpShardDao;
    @Autowired
    private PlusTenantConfigManager plusTenantConfigManager;

    private Integer maxTemplatePloyRetryTime = 4;
    private Integer retryTemplatePloyIntervalSeconds = 10;
    private final ScheduledExecutorService scheduledThreadPool =
            new ScheduledThreadPoolExecutor(1, new ThreadFactoryBuilder()
                    .setNameFormat("template_ploy_detail_change_schedule-%d").build());
    private final ThreadPoolExecutor executorService = DynamicExecutors.newThreadPool(0, 10, 10 * 1000L, 100, new ThreadFactoryBuilder().setNameFormat("process-template-ploy-change-%d").build());

    @Override
    public void afterPropertiesSet() throws Exception {
        scheduledThreadPool.scheduleAtFixedRate(this::scheduleTask, 60, 10, TimeUnit.SECONDS);
        executorService.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        ConfigFactory.getConfig("erp-sync-data-all", "TemplatePloySchedule", config -> {
            executorService.setMaximumPoolSize(config.getInt("max_pool_size", 10));
            maxTemplatePloyRetryTime = config.getInt("maxTemplatePloyRetryTime", 4);
            retryTemplatePloyIntervalSeconds = config.getInt("retryTemplatePloyIntervalSeconds", 10);
        });
    }

    private void scheduleTask() {
        for (int i = 0; i < 10000; i++) {
            final TemplatePloyDetailChangeEntity entity = templatePloyDetailChangeDao.doFirst();
            if (Objects.isNull(entity)) {
                break;
            }

            submitEvent(entity);
        }

        // 检查是否有半小时还没有完成的任务,可能是因为缩容导致任务中断,没有修改状态
        for (int i = 0; i < 1000; i++) {
            final TemplatePloyDetailChangeEntity entity = templatePloyDetailChangeDao.doExecutingBefore30Min();
            if (Objects.isNull(entity)) {
                break;
            }

            submitEvent(entity);
        }
    }

    private void submitEvent(TemplatePloyDetailChangeEntity entity) {
        executorService.submit(() -> {
            // 初始化logId,traceId
            TraceUtil.initTrace(entity.getTraceId());
            try {
                process(entity);
                templatePloyDetailChangeDao.successful(entity.getId());
            } catch (Exception e) {
                if (entity.getTryTime() < maxTemplatePloyRetryTime) {
                    templatePloyDetailChangeDao.retry(entity.getId(), e.getMessage(), System.currentTimeMillis() + retryTemplatePloyIntervalSeconds * 1000L);
                } else {
                    templatePloyDetailChangeDao.fail(entity.getId(), e.getMessage());
                    log.error("TemplatePloyDetailChangeScheduleService error, entity:{}", entity, e);
                }
            }
        });
    }

    private void process(TemplatePloyDetailChangeEntity obj) throws SQLException {
        final String downstreamId = obj.getDownstreamId();
        final TemplatePloyDetailChangeEntity.Reason reason = TemplatePloyDetailChangeEntity.Reason.valueOf(obj.getReason());
        switch (reason) {
            case INIT:
                initTenant(downstreamId);
                break;
            case ENABLED:
                final TemplatePloyDetailChangeEntity.EnabledPloyDetailEvent enabledPloyDetailEvent = reason.getEvent(obj.getEvent());
                final SyncPloyDetailResult syncPloyDetailResult = JSON.parseObject(enabledPloyDetailEvent.getSyncPloyDetailResult(), SyncPloyDetailResult.class);
                switchStreamStatusManager.processAfterEnablePloy(downstreamId, enabledPloyDetailEvent.isNeedSyncDuringStop(), syncPloyDetailResult);
                break;
            case DISABLED:
                TemplatePloyDetailChangeEntity.DisabledPloyDetailEvent disabledPloyDetailEvent = reason.getEvent(obj.getEvent());
                syncPloyDetailSnapshotManager.setPloyDetailLastSyncTime(downstreamId, disabledPloyDetailEvent.getPloyDetailId(), disabledPloyDetailEvent.getErpObjectApiName(), obj.getUpdateTime());
                break;
            case UPDATED:
                TemplatePloyDetailChangeEntity.UpdatedPloyDetailEvent updatedPloyDetailEvent = reason.getEvent(obj.getEvent());
                adminSyncPloyDetailService.doLastSyncTime(downstreamId, updatedPloyDetailEvent.getDcId(), updatedPloyDetailEvent.getErpObjectApiName(), updatedPloyDetailEvent.getSyncRulesData());
                break;
            case DELETED_PLOY_DETAIL:
                TemplatePloyDetailChangeEntity.DeletedPloyDetailEvent deletedPloyDetailEvent = reason.getEvent(obj.getEvent());
                // 删除配置
                tenantConfigurationManager.deleteByTenantIdWithDataCenterId(downstreamId, deletedPloyDetailEvent.getPloyDetailId());
                break;
            case DELETED_DOWNSTREAM:
                TemplatePloyDetailChangeEntity.DeletedDownstreamEvent deletedDownstreamEvent = reason.getEvent(obj.getEvent());
                // 校验下,如果已经加回来就不处理了
                final String groupId = deletedDownstreamEvent.getGroupId();
                final RelationErpShardEntity relationErpShardEntity = relationErpShardDao.queryByGroupIdAndDownstreamId(groupId, downstreamId);
                if (Objects.nonNull(relationErpShardEntity) && Objects.equals(relationErpShardEntity.getStatus(), RelationErpShardStatusEnum.normal.getStatus())) {
                    break;
                }
                // 删除sync_time,配置
                erpSyncTimeDao.deleteByTenantIdAndObjectApiName(downstreamId, deletedDownstreamEvent.getErpObjects());
                tenantConfigurationManager.deleteByTenantIdWithDataCenterId(downstreamId, deletedDownstreamEvent.getPloyDetailIds());
                //检查是否没有模板了,是的话需要删除代管配置,需要直接查DB,防止缓存
                String group = relationErpShardDao.queryFirstGroupByDownstreamIdWithDB(downstreamId);
                if (Objects.isNull(group)) {
                    plusTenantConfigManager.deleteConvertConfig(downstreamId, "0", TenantConfigurationTypeEnum.MANAGED_ENTERPRISE);
                }
                break;
            default:
                break;
        }
    }

    private void initTenant(String tenantId) throws SQLException {
        erpConnectInfoManager.getOrCreateCrmDc(tenantId, null);
        String s = migrateTableManager.initTenantTable(tenantId);
        log.info("inner init tenantTable success,tenantId:{},result:{}", tenantId, s);

        // 将代管状态设置为true
        initManagedConfig(tenantId);
    }

    private void initManagedConfig(String tenantId) {
        final ErpTenantConfigurationEntity one = tenantConfigurationManager.findOne(tenantId, "0", "ALL", TenantConfigurationTypeEnum.MANAGED_ENTERPRISE.name());
        if (Objects.nonNull(one)) {
            return;
        }
        tenantConfigurationManager.updateConfig(tenantId, "0", "ALL", TenantConfigurationTypeEnum.MANAGED_ENTERPRISE.name(), "true");
    }
}
