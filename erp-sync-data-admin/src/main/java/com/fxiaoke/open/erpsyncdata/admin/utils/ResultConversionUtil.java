package com.fxiaoke.open.erpsyncdata.admin.utils;

import com.fxiaoke.open.erpsyncdata.admin.result.ListObjectFieldsResult;
import com.fxiaoke.open.erpsyncdata.admin.result.ObjectFieldResult;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.FieldMappingData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.OptionMappingData;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.CompositeIdExtend;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectFieldResult;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 14:58 2020/8/22
 * @Desc:
 */
public class ResultConversionUtil {

    public static ListObjectFieldsResult converseErpObjFelds(String tenantId,List<ErpObjectFieldResult> erpObjectFields) {
        ListObjectFieldsResult listObjectFieldsResult = new ListObjectFieldsResult();
        List<ObjectFieldResult> fields = Lists.newArrayList();
        listObjectFieldsResult.setFields(fields);
        for (ErpObjectFieldResult erpObjectFieldResult : erpObjectFields) {
            ObjectFieldResult objectFieldResult = new ObjectFieldResult();
            objectFieldResult.setApiName(erpObjectFieldResult.getFieldApiName());
            if(erpObjectFieldResult.getFieldDefineType() != null){
                objectFieldResult.setType(erpObjectFieldResult.getFieldDefineType().name());
                if(ErpFieldTypeEnum.select_one.name().equals(erpObjectFieldResult.getFieldDefineType().name())||ErpFieldTypeEnum.record_type.name().equals(erpObjectFieldResult.getFieldDefineType().name())
                ||ErpFieldTypeEnum.true_or_false.name().equals(erpObjectFieldResult.getFieldDefineType().name())||ErpFieldTypeEnum.select_many.name().equals(erpObjectFieldResult.getFieldDefineType().name())){
                    Set<ObjectFieldResult.Option> options=GsonUtil.fromJson(GsonUtil.toJson(erpObjectFieldResult.getFieldExtendValue()),new TypeToken<Set<ObjectFieldResult.Option>>(){}.getType());
                    objectFieldResult.setOptions(options);
                }
                if(ErpFieldTypeEnum.master_detail.name().equals(erpObjectFieldResult.getFieldDefineType().name())){
                    String targetApiName= GsonUtil.toJson(erpObjectFieldResult.getFieldExtendValue());
                    objectFieldResult.setTargetApiName(targetApiName);
                }
                if(ErpFieldTypeEnum.object_reference.name().equals(erpObjectFieldResult.getFieldDefineType().name())
                        || ErpFieldTypeEnum.object_reference_many.name().equals(erpObjectFieldResult.getFieldDefineType().name())){
                    CompositeIdExtend referenceExtend = CompositeIdExtend.getByReferenceField(tenantId,GsonUtil.toJson(erpObjectFieldResult.getFieldExtendValue()));
                    objectFieldResult.setTargetApiName(referenceExtend.getTargetApiName());
                }
            }
            objectFieldResult.setIsRequired(erpObjectFieldResult.isRequired());
            objectFieldResult.setLabel(erpObjectFieldResult.getFieldLabel());
            listObjectFieldsResult.getFields().add(objectFieldResult);
        }
        return listObjectFieldsResult;
    }


    public static List<com.fxiaoke.open.erpsyncdata.admin.data.FieldMappingData> convertFieldMappingData(List<FieldMappingData> sourceList){
        if (CollectionUtils.isEmpty(sourceList)){
            return null;
        }
        return sourceList.stream().map(ResultConversionUtil::convert).collect(Collectors.toList());
    }

    public static com.fxiaoke.open.erpsyncdata.admin.data.FieldMappingData convert(FieldMappingData source){
        if(source==null){
            return null;
        }
        com.fxiaoke.open.erpsyncdata.admin.data.FieldMappingData result = new com.fxiaoke.open.erpsyncdata.admin.data.FieldMappingData();
        result.setSourceApiName(source.getSourceApiName());
        result.setSourceType(source.getSourceType());
        result.setSourceTargetApiName(source.getSourceTargetApiName());
        result.setSourceQuoteFieldType(source.getSourceQuoteFieldType());
        result.setSourceQuoteRealField(source.getSourceQuoteRealField());
        result.setSourceQuoteFieldTargetObjectApiName(source.getSourceQuoteFieldTargetObjectApiName());
        result.setSourceQuoteFieldTargetObjectField(source.getSourceQuoteFieldTargetObjectField());
        result.setDestApiName(source.getDestApiName());
        result.setDestType(source.getDestType());
        result.setDestTargetApiName(source.getDestTargetApiName());
        result.setDestQuoteFieldType(source.getDestQuoteFieldType());
        result.setOptionMappings(ResultConversionUtil.convertOptionMappingData(source.getOptionMappings()));
        result.setMappingType(source.getMappingType());
        result.setFunction(source.getFunction());
        result.setValue(source.getValue());
        return result;

    }


    public static List<com.fxiaoke.open.erpsyncdata.admin.data.OptionMappingData> convertOptionMappingData(List<OptionMappingData> sourceList){
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(sourceList)){
            return null;
        }
        return sourceList.stream().map(ResultConversionUtil::convert).collect(Collectors.toList());
    }
    public static com.fxiaoke.open.erpsyncdata.admin.data.OptionMappingData convert(OptionMappingData source){
        if(source==null){
            return null;
        }
        com.fxiaoke.open.erpsyncdata.admin.data.OptionMappingData result = new com.fxiaoke.open.erpsyncdata.admin.data.OptionMappingData();
        result.setSourceOption(source.getSourceOption());
        result.setDestOption(source.getDestOption());
        return result;
    }

}
