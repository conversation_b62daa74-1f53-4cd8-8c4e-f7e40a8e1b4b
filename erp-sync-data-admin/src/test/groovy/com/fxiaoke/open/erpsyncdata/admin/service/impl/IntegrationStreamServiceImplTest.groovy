package com.fxiaoke.open.erpsyncdata.admin.service.impl

import com.fxiaoke.open.erpsyncdata.admin.arg.PaasDataSourceConfigStatus
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpTenantConfigurationDaoAccess
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.IntegrationStreamNodesData
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.QueryObjectMappingData
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.QueryObjectOrFilterFieldMappingsData
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import org.assertj.core.util.Lists
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
class IntegrationStreamServiceImplTest extends Specification {

    def "GetObjApiNameByPloyDetail"() {
        when:
        IntegrationStreamServiceImpl integrationStreamService = new IntegrationStreamServiceImpl() {};
        String erpDcId = "780777150699143168"
        Set<String> crmObjApiNames = Sets.newHashSet();
        Map<String, Set<String>> erpDc2ObjApiNames = Maps.newHashMap()
        SyncPloyDetailEntity syncPloyDetailEntity = JacksonUtil.fromJson(ployDetail, SyncPloyDetailEntity.class)
        println(syncPloyDetailEntity)

        integrationStreamService.getObjApiNameByPloyDetail(crmObjApiNames, erpDc2ObjApiNames, syncPloyDetailEntity)
        then:
        crmObjApiNames.containsAll(crmObjApiNamesResult) && erpDc2ObjApiNames.get(erpDcId).containsAll(erpObjApiNamesResult)

        where:
        crmObjApiNamesResult                                                   | erpObjApiNamesResult                                                      | ployDetail
        // TODO: 这里的json解析有问题，太复杂了，有空再改
        Sets.newHashSet("SalesOrderObj", "SalesOrderProductObj", /*"AccountObj"*/) | Sets.newHashSet("SAL_SaleOrder.BillHead", "SAL_SaleOrder.SaleOrderEntry") | "{\"destTenantType\": 2,\"destDataCenterId\": \"780777150699143168\", \"sourceTenantType\": 1, \"destObjectApiName\": \"SAL_SaleOrder.BillHead\", \"sourceDataCenterId\": \"780777210996457472\", \"sourceObjectApiName\": \"SalesOrderObj\", \"detailObjectMappings\": [{\"destObjectApiName\": \"SAL_SaleOrder.SaleOrderEntry\", \"sourceObjectApiName\": \"SalesOrderProductObj\"}], \"integrationStreamNodes\": {\"reverseWriteNode\": {\"destObjectApiName\": \"SalesOrderObj\", \"sourceObjectApiName\": \"SAL_SaleOrder.BillHead\", \"detailObjectMappings\": [{\"destObjectApiName\": \"SalesOrderProductObj\", \"sourceObjectApiName\": \"SAL_SaleOrder.SaleOrderEntry\"}]},\"checkSyncDataMappingNode\":{\"queryObjectMappingData\":{\"sourceObjectApiName\":\"SAL_SaleOrder.BillHead\",\"destObjectApiName\":\"SalesOrderObj\",\"queryFieldMappings\":[[{\"destApiName\":\"name\",\"sourceApiName\":\"FName\",\"filterOperator\":\"EQ\"}]]},\"source2SyncDataMapping\":[{\"sourceObjectApiName\":\"SalesOrderObj\",\"fieldMappings\":[{\"destApiName\":\"destDataId\",\"sourceApiName\":\"_id\"},{\"destApiName\":\"destDataName\",\"sourceApiName\":\"name\"},{\"destApiName\":\"sourceDataId\",\"sourceApiName\":\"erp_id\"},{\"destApiName\":\"sourceDataName\",\"sourceApiName\":\"name\"}]}],\"detailCheckSyncDataMappingData\":[{\"queryObjectMappingData\":{\"sourceObjectApiName\":\"SAL_SaleOrder.SaleOrderEntry\",\"destObjectApiName\":\"SalesOrderProductObj\",\"queryFieldMappings\":[[{\"destApiName\":\"name\",\"sourceApiName\":\"FName\",\"filterOperator\":\"EQ\"}]]},\"source2SyncDataMapping\":[{\"sourceObjectApiName\":\"SalesOrderProductObj\",\"fieldMappings\":[{\"destApiName\":\"destDataId\",\"sourceApiName\":\"_id\"},{\"destApiName\":\"destDataName\",\"sourceApiName\":\"name\"},{\"destApiName\":\"sourceDataId\",\"sourceApiName\":\"erp_id\"},{\"destApiName\":\"sourceDataName\",\"sourceApiName\":\"name\"}]}]}]},\"queryCrmObject2DestNodeBySource\":{\"queryObjectMappingData\":{\"sourceObjectApiName\":\"SAL_SaleOrder.BillHead\",\"destObjectApiName\":\"AccountObj\",\"queryFieldMappings\":[[{\"destApiName\":\"name\",\"sourceApiName\":\"FName\",\"filterOperator\":\"EQ\"}]]},\"source2SyncDataMapping\":{\"sourceObjectApiName\":\"AccountObj\",\"destObjectApiName\":\"SalesOrderObj\",\"fieldMappings\":[{\"destApiName\":\"destDataId\",\"sourceApiName\":\"_id\"},{\"destApiName\":\"destDataName\",\"sourceApiName\":\"name\"},{\"destApiName\":\"sourceDataId\",\"sourceApiName\":\"erp_id\"},{\"destApiName\":\"sourceDataName\",\"sourceApiName\":\"name\"}]},\"detailCheckSyncDataMappingData\":[{\"queryObjectMappingData\":{\"sourceObjectApiName\":\"SAL_SaleOrder.SaleOrderEntry\",\"destObjectApiName\":\"AccountObj\",\"queryFieldMappings\":[[{\"destApiName\":\"name\",\"sourceApiName\":\"FName\",\"filterOperator\":\"EQ\"}]]},\"source2SyncDataMapping\":{\"sourceObjectApiName\":\"AccountObj\",\"destObjectApiName\":\"SalesOrderProductObj\",\"fieldMappings\":[{\"destApiName\":\"destDataId\",\"sourceApiName\":\"_id\"},{\"destApiName\":\"destDataName\",\"sourceApiName\":\"name\"},{\"destApiName\":\"sourceDataId\",\"sourceApiName\":\"erp_id\"},{\"destApiName\":\"sourceDataName\",\"sourceApiName\":\"name\"}]}}]}}}"
        Sets.newHashSet("SalesOrderObj", "SalesOrderProductObj")               | Sets.newHashSet("SAL_SaleOrder.BillHead", "SAL_SaleOrder.SaleOrderEntry") | "{\"destTenantType\":2,\"destDataCenterId\":\"780777150699143168\",\"sourceTenantType\":1,\"destObjectApiName\":\"SAL_SaleOrder.BillHead\",\"sourceDataCenterId\":\"780777210996457472\",\"sourceObjectApiName\":\"SalesOrderObj\",\"detailObjectMappings\":[{\"destObjectApiName\":\"SAL_SaleOrder.SaleOrderEntry\",\"sourceObjectApiName\":\"SalesOrderProductObj\"}]}"


    }

    def "json test"() {
        expect:
        def str = '''
        {
            "destTenantType": 2,
            "destDataCenterId": "780777150699143168",
            "sourceTenantType": 1, 
            "destObjectApiName": "SAL_SaleOrder.BillHead", 
            "sourceDataCenterId": "780777210996457472", 
            "sourceObjectApiName": "SalesOrderObj", 
            "detailObjectMappings": 
            [
                {
                    "destObjectApiName": "SAL_SaleOrder.SaleOrderEntry", 
                    "sourceObjectApiName": "SalesOrderProductObj"
                }
            ], 
            "integrationStreamNodes": 
            {
                "reverseWriteNode": 
                    {
                        "destObjectApiName": "SalesOrderObj", 
                        "sourceObjectApiName": "SAL_SaleOrder.BillHead", 
                        "detailObjectMappings": 
                            [
                                {
                                    "destObjectApiName": "SalesOrderProductObj", 
                                    "sourceObjectApiName": "SAL_SaleOrder.SaleOrderEntry"
                                }
                            ]
                    },
                "checkSyncDataMappingNode":
                    {
                        "queryObjectMappingData":
                            {
                                "sourceObjectApiName":"SAL_SaleOrder.BillHead",
                                "destObjectApiName":"SalesOrderObj",
                                "queryFieldMappings":
                                    [
                                        {
                                            "destApiName":"name",
                                            "sourceApiName":"FName",
                                            "filterOperator":"EQ"
                                        }
                                    ]
                            },
                        "source2SyncDataMapping":
                            [
                                {
                                    "sourceObjectApiName":"SalesOrderObj",
                                    "fieldMappings":
                                        [
                                            {
                                                "destApiName":"destDataId",
                                                "sourceApiName":"_id"
                                            },
                                            {
                                                "destApiName":"destDataName",
                                                "sourceApiName":"name"
                                            },
                                            {
                                                "destApiName":"sourceDataId",
                                                "sourceApiName":"erp_id"
                                            },
                                            {
                                                "destApiName":"sourceDataName",
                                                "sourceApiName":"name"
                                            }
                                        ]
                                }
                            ],
                        "detailCheckSyncDataMappingData":
                            [
                                {
                                    "queryObjectMappingData":
                                        {
                                            "sourceObjectApiName":"SAL_SaleOrder.SaleOrderEntry",
                                            "destObjectApiName":"SalesOrderProductObj",
                                            "queryFieldMappings":
                                                [
                                                    {
                                                        "destApiName":"name",
                                                        "sourceApiName":"FName",
                                                        "filterOperator":"EQ"
                                                    }
                                                ]
                                        },
                                        "source2SyncDataMapping":
                                            [
                                                {
                                                    "sourceObjectApiName":"SalesOrderProductObj",
                                                    "fieldMappings":
                                                        [
                                                            {
                                                                "destApiName":"destDataId",
                                                                "sourceApiName":"_id"
                                                            },
                                                            {
                                                                "destApiName":"destDataName",
                                                                "sourceApiName":"name"
                                                            },
                                                            {
                                                                "destApiName":"sourceDataId",
                                                                "sourceApiName":"erp_id"
                                                            },
                                                            {
                                                                "destApiName":"sourceDataName",
                                                                "sourceApiName":"name"
                                                            }
                                                        ]
                                                }
                                            ]
                                }
                            ]
                    },
                "queryCrmObject2DestNodeBySource":
                    {
                        "queryObjectMappingData":
                            {
                                "sourceObjectApiName":"SAL_SaleOrder.BillHead",
                                "destObjectApiName":"AccountObj",
                                "queryFieldMappings":
                                    [
                                        {
                                            "destApiName":"name",
                                            "sourceApiName":"FName",
                                            "filterOperator":"EQ"
                                        }
                                    ]
                            },
                        "source2SyncDataMapping":
                            {
                                "sourceObjectApiName":"AccountObj",
                                "destObjectApiName":"SalesOrderObj",
                                "fieldMappings":
                                    [
                                        {
                                            "destApiName":"destDataId",
                                            "sourceApiName":"_id"
                                        },
                                        {
                                            "destApiName":"destDataName",
                                            "sourceApiName":"name"
                                        },
                                        {
                                            "destApiName":"sourceDataId",
                                            "sourceApiName":"erp_id"
                                        },
                                        {
                                            "destApiName":"sourceDataName",
                                            "sourceApiName":"name"
                                        }
                                    ]
                            },
                        "detailCheckSyncDataMappingData":
                            [
                                {
                                    "queryObjectMappingData":
                                        {
                                            "sourceObjectApiName":"SAL_SaleOrder.SaleOrderEntry",
                                            "destObjectApiName":"AccountObj",
                                            "queryFieldMappings":
                                                [
                                                    {
                                                        "destApiName":"name",
                                                        "sourceApiName":"FName",
                                                        "filterOperator":"EQ"
                                                    }
                                                ]
                                        },
                                    "source2SyncDataMapping":
                                        {
                                            "sourceObjectApiName":"AccountObj",
                                            "destObjectApiName":"SalesOrderProductObj",
                                            "fieldMappings":
                                                [
                                                    {
                                                        "destApiName":"destDataId",
                                                        "sourceApiName":"_id"
                                                    },
                                                    {
                                                        "destApiName":"destDataName",
                                                        "sourceApiName":"name"},
                                                        {
                                                            "destApiName":"sourceDataId",
                                                            "sourceApiName":"erp_id"
                                                        },
                                                        {
                                                            "destApiName":"sourceDataName",
                                                            "sourceApiName":"name"
                                                        }
                                                ]
                                        }
                                }
                            ]
                    }
            }
        }
        '''
        def str1 = '''
        {
            "integrationStreamNodes": 
            {
                "checkSyncDataMappingNode":
                {
                    "queryObjectMappingData":
                    {
                        "sourceObjectApiName":"SAL_SaleOrder.BillHead",
                        "destObjectApiName":"SalesOrderObj",
                        "queryFieldMappings":
                            [
                                {
                                    "destApiName":"name",
                                    "sourceApiName":"FName",
                                    "filterOperator":"EQ"
                                }
                            ]
                    },
                    "source2SyncDataMapping":
                    [
                        {
                            "sourceObjectApiName":"SalesOrderObj",
                            "fieldMappings":
                            [
                                {
                                    "destApiName":"destDataId",
                                    "sourceApiName":"_id"
                                },
                                {
                                    "destApiName":"destDataName",
                                    "sourceApiName":"name"
                                },
                                {
                                    "destApiName":"sourceDataId",
                                    "sourceApiName":"erp_id"
                                },
                                {
                                    "destApiName":"sourceDataName",
                                    "sourceApiName":"name"
                                }
                            ]
                        }
                    ],
                    "detailCheckSyncDataMappingData":
                    [
                        {
                            "queryObjectMappingData":
                            {
                                "sourceObjectApiName":"SAL_SaleOrder.SaleOrderEntry",
                                "destObjectApiName":"SalesOrderProductObj",
                                "queryFieldMappings":
                                [
                                    {
                                        "destApiName":"name",
                                        "sourceApiName":"FName",
                                        "filterOperator":"EQ"
                                    }
                                ]
                            },
                            "source2SyncDataMapping":
                            [
                                {
                                    "sourceObjectApiName":"SalesOrderProductObj",
                                    "fieldMappings":
                                    [
                                        {
                                            "destApiName":"destDataId",
                                            "sourceApiName":"_id"
                                        },
                                        {
                                            "destApiName":"destDataName",
                                            "sourceApiName":"name"
                                        },
                                        {
                                            "destApiName":"sourceDataId",
                                            "sourceApiName":"erp_id"
                                        },
                                        {
                                            "destApiName":"sourceDataName",
                                            "sourceApiName":"name"
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            }
        }
        '''
        def str2 = '''
        {
            "id":"123456",
            "integrationStreamNodes": 
            {
                "checkSyncDataMappingNode":
                {
                    "queryObjectMappingData":
                    {
                        "sourceObjectApiName":"SAL_SaleOrder.BillHead",
                        "destObjectApiName":"SalesOrderObj",
                        "queryFieldMappings":
                        [
                            [{
                                "destApiName":"name",
                                "sourceApiName":"FName",
                                "filterOperator":"EQ"
                            }]
                        ]
                    }
                }
            }
        }
        '''
        SyncPloyDetailEntity syncPloyDetailEntity = JacksonUtil.fromJson(str2, SyncPloyDetailEntity.class)
        def qmData = new QueryObjectOrFilterFieldMappingsData()
        qmData.add(new QueryObjectOrFilterFieldMappingsData.QueryObjectAndFilterFieldMappingData())
        def mData = new QueryObjectMappingData(sourceObjectApiName: "SAL_SaleOrder.BillHead", destObjectApiName: "SalesOrderObj", queryFieldMappings: qmData)
        def mNode = new IntegrationStreamNodesData.CheckSyncDataMappingNode(queryObjectMappingData: mData)
        def datas = new IntegrationStreamNodesData(checkSyncDataMappingNode: mNode)
        def entity = new SyncPloyDetailEntity(integrationStreamNodes: datas)
        println(syncPloyDetailEntity)
        println(entity)
        def json = JacksonUtil.toJson(entity)
        println(JacksonUtil.fromJson(json, SyncPloyDetailEntity.class))
    }

    def "UpdateTenantNeedPassDataSourceConfig"() {
        PaasDataSourceConfigStatus arg = new PaasDataSourceConfigStatus()
        arg.setCrmObjectApiName(crmObjApiName)
        arg.setStatus(status)
        arg.setDataSourceList(dataSourceList)
        ErpTenantConfigurationEntity result
        TenantConfigurationManager tenantConfigurationManager = Stub()
        tenantConfigurationManager.insert(*_) >> { args ->
            return 1
        }
        tenantConfigurationManager.updateById(*_) >> {
            args ->
                result = args[1]
                return 1
        }
        ErpTenantConfigurationDaoAccess erpTenantConfigurationDaoAccess = Stub()
        erpTenantConfigurationDaoAccess.findOneNoCache(*_) >> { args ->
            if (globalConfig == null) {
                return null
            } else if ("0".equals(args[0])) {
                ErpTenantConfigurationEntity entity = new ErpTenantConfigurationEntity();
                entity.setConfiguration(globalConfig)
                return entity
            } else {
                return null
            }
        }
        AdminSyncPloyDetailDao adminSyncPloyDetailDao = Stub()
        adminSyncPloyDetailDao.listByTenantId(*_) >> {
            List<SyncPloyDetailEntity> entities = Lists.newArrayList()
            SyncPloyDetailEntity entity2 = new SyncPloyDetailEntity()
            entity2.setSourceTenantType(TenantType.CRM)
            entity2.setSourceObjectApiName("SalesOrderObj")
            entities.add(entity2)
            SyncPloyDetailEntity entity1 = new SyncPloyDetailEntity()
            entity1.setSourceTenantType(TenantType.CRM)
            entity1.setSourceObjectApiName("ProductObj")
            entities.add(entity1)
            return entities
        }
        IntegrationStreamServiceImpl monitor = new IntegrationStreamServiceImpl(adminSyncPloyDetailDao: adminSyncPloyDetailDao,
                erpTenantConfigurationDaoAccess: erpTenantConfigurationDaoAccess,
                tenantConfigurationManager: tenantConfigurationManager)
        when:
        monitor.updateTenantNeedPassDataSourceConfig(tenantId, arg)
        then:
        result2 == result.getConfiguration()
        Map<String, List<String>> configuration = JacksonUtil.fromJson(result.getConfiguration(), Map.class);
        Map<String, List<String>> configuration1 = JacksonUtil.fromJson(result2, Map.class)
        configuration.size() == configuration1.size()
        if (status) {
            String key = tenantId + "_" + crmObjApiName;
            configuration.containsKey(key) && configuration1.containsKey(key)
            List<String> list = configuration.get(key)
            List<String> list1 = configuration1.get(key)
            (list == null && list1 == null) || list.size() == list1.size()
        }
        where:
        tenantId | crmObjApiName   | status | globalConfig                                              | dataSourceList             | result1 | result2
        "88521"  | "SalesOrderObj" | true   | null                                                      | ["import"]                 | true    | "{\"88521_SalesOrderObj\":[\"optool_batch\"]}"
        "88521"  | "SalesOrderObj" | true   | "{}"                                                      | ["import", "optool_batch"] | true    | "{\"88521_SalesOrderObj\":[]}"
        "88521"  | "SalesOrderObj" | true   | "{\"88521_SalesOrderObj\":[\"optool_batch\",\"import\"]}" | ["import", "optool_batch"] | true    | "{\"88521_SalesOrderObj\":[]}"
        "88521"  | "SalesOrderObj" | true   | "{\"88521_SalesOrderObj\":[\"optool_batch\",\"import\"]}" | ["import"]                 | true    | "{\"88521_SalesOrderObj\":[\"optool_batch\"]}"
        "88521"  | "SalesOrderObj" | true   | "{\"88521\":[]}"                                          | ["import"]                 | true    | "{\"88521_ProductObj\":[],\"88521_SalesOrderObj\":[\"optool_batch\"]}"
        "88521"  | "SalesOrderObj" | false  | "{\"88521_SalesOrderObj\":[\"import\",\"optool_batch\"]}" | ["import"]                 | true    | "{}"
        "88521"  | "SalesOrderObj" | false  | "{\"88521_SalesOrderObj\":[\"import\",\"optool_batch\"]}" | ["import", "optool_batch"] | true    | "{}"

    }

    def "QueryTenantNeedPassDataSourceConfig"() {
        PaasDataSourceConfigStatus arg = new PaasDataSourceConfigStatus()
        arg.setCrmObjectApiName(crmObjApiName)
        TenantConfigurationManager tenantConfigurationManager = new TenantConfigurationManager()
        ErpTenantConfigurationDaoAccess erpTenantConfigurationDaoAccess = Stub()
        erpTenantConfigurationDaoAccess.findOneNoCache(*_) >> {
            if (config == null) {
                return null
            } else {
                ErpTenantConfigurationEntity entity = new ErpTenantConfigurationEntity();
                entity.setConfiguration(config)
                return entity
            }
        }

        IntegrationStreamServiceImpl monitor = new IntegrationStreamServiceImpl(
                erpTenantConfigurationDaoAccess: erpTenantConfigurationDaoAccess,
                tenantConfigurationManager: tenantConfigurationManager)
        when:
        Result<PaasDataSourceConfigStatus> result = monitor.queryTenantNeedPassDataSourceConfig(tenantId, arg)
        then:
        PaasDataSourceConfigStatus status = result.getData()
        PaasDataSourceConfigStatus status1 = JacksonUtil.fromJson(result2, PaasDataSourceConfigStatus.class)
        status.getStatus() == status1.getStatus()
        if (status.getStatus()) {
            status1.getDataSourceList().size() == status.getDataSourceList().size()
        }
        where:
        tenantId | crmObjApiName   | config                                                    | result2
        "88521"  | "SalesOrderObj" | null                                                      | "{\"status\":true,\"dataSourceList\":[\"optool_batch\",\"import\"]}"
        "88521"  | "SalesOrderObj" | "{}"                                                      | "{\"status\":true,\"dataSourceList\":[\"optool_batch\",\"import\"]}"
        "88521"  | "SalesOrderObj" | "{\"88521_SalesOrderObj\":[\"optool_batch\",\"import\"]}" | "{\"status\":false,\"dataSourceList\":[]}"
        "88521"  | "AccountObj"    | "{\"88521_SalesOrderObj\":[\"optool_batch\",\"import\"]}" | "{\"status\":true,\"dataSourceList\":[\"optool_batch\",\"import\"]}"
        "88521"  | "SalesOrderObj" | "{\"88521\":[]}"                                          | "{\"status\":true,\"dataSourceList\":[\"optool_batch\",\"import\"]}"
        "88521"  | "SalesOrderObj" | "{\"88521_SalesOrderObj\":[]}"                            | "{\"status\":true,\"dataSourceList\":[\"optool_batch\",\"import\"]}"
        "88521"  | "SalesOrderObj" | "{\"88521_SalesOrderObj\":[\"import\"]}"                  | "{\"status\":true,\"dataSourceList\":[\"optool_batch\"]}"
        "88521"  | "SalesOrderObj" | "{\"88521_SalesOrderObj\":[\"optool_batch\"]}"            | "{\"status\":true,\"dataSourceList\":[\"import\"]}"
        "88521"  | "AccountObj"    | "{\"*\":[\"optool_batch\",\"import\"]}"                   | "{\"status\":false,\"dataSourceList\":[]}"


    }
}
