package com.fxiaoke.open.erpsyncdata.admin.service

import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.admin.service.superadmin.SuperAdminService
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpInterfaceLogEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.table.dao.ErpTableDao
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjSplitTypeEnum
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjectTypeEnum
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectDescResult
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result
import com.google.common.collect.Lists
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/8/28
 */
@Ignore
class SuperAdminServiceTest extends BaseSpockTest {
    @Autowired
    private SuperAdminService service;

    @Autowired
    private ErpObjectService erpObjectService;
    @Autowired
    private ErpTableDao erpTableDao;

    @Test
    void queryErpFakeMasterObjectByTenantId(){
        Result<List<ErpObjectDescResult>> ret =  erpObjectService.queryErpFakeMasterObjectByTenantId("81243",1000);
        print("get fake object ret: ")
        print(ret)
    }

    @Test
    void queryInterFaceLog(){
        ErpInterfaceLogEntity entity=new ErpInterfaceLogEntity();
        entity.setTenantId("79675")
        Result<List<ErpInterfaceLogEntity>>  rs = service.queryInterFaceLog(entity,1,11)
        print(rs)
    }

    @Test
    void queryErpObjectEntity(){
        ErpObjectEntity entity=new ErpObjectEntity();
        entity.setTenantId("79675")
        Result<List<ErpObjectEntity>>  rs = service.queryErpObj(entity,1,11)
        print(rs)
    }

    @Test
    void queryErpObjectFieldEntity(){
        ErpObjectFieldEntity entity=new ErpObjectFieldEntity();
        entity.setTenantId("79675")
        Result<List<ErpObjectFieldEntity>>  rs = service.queryErpObjField(entity,1,11)
        print(rs)
    }

    @Test
    void queryErpObjectRelationshipEntity(){
        ErpObjectRelationshipEntity entity=new ErpObjectRelationshipEntity();
        entity.setTenantId("79675")
        Result<List<ErpObjectRelationshipEntity>> rs = service.queryErpObjRelationship(entity,1,11)
        print(rs)
    }

    @Test
    void createErpObjectEntity(){
        ErpObjectEntity entity=new ErpObjectEntity();
        entity.setTenantId("79675")
        entity.setChannel(ErpChannelEnum.ERP_U8)
        entity.setErpObjectName("测试测试测试")
        entity.setErpObjectType(ErpObjectTypeEnum.REAL_OBJECT);
        entity.setErpObjectApiName("testtesttsetadd");
        Result<List<ErpObjectEntity>>  rs = service.createErpObj(Lists.asList(entity))
        print(rs)
    }
    @Test
    void updateErpObjectEntity(){
        ErpObjectEntity entity=new ErpObjectEntity();
        entity.setId("614171741614899200")
        entity.setTenantId("79675")
        entity.setChannel(ErpChannelEnum.ERP_U8)
        entity.setErpObjectName("测试测试测试2323")
        entity.setErpObjectType(ErpObjectTypeEnum.REAL_OBJECT);
        entity.setErpObjectApiName("testtesttsetadd");
        Result<List<ErpObjectEntity>>  rs = service.updateErpObj(Lists.asList(entity))
        print(rs)
    }

    @Test
    void deleteErpObjectEntity(){
        ErpObjectEntity entity=new ErpObjectEntity();
        entity.setId("614171999447154688")
        entity.setTenantId("79675")
        entity.setChannel(ErpChannelEnum.ERP_U8)
        entity.setErpObjectName("测试测试测试222")
        entity.setErpObjectType(ErpObjectTypeEnum.REAL_OBJECT);
        entity.setErpObjectApiName("testtesttsetadd");
        Result<List<ErpObjectEntity>>  rs = service.deleteErpObj(Lists.asList(entity))
        print(rs)
    }

    @Test
    void createErpObjectFieldEntity(){
        ErpObjectFieldEntity entity=new ErpObjectFieldEntity();
        entity.setTenantId("79675")
        entity.setErpObjectApiName("testestset")
        entity.setChannel(ErpChannelEnum.ERP_U8)
        entity.setFieldApiName("testiti")
        entity.setFieldDefineType(ErpFieldTypeEnum.string)
        entity.setFieldLabel("sjhohd")
        entity.setRequired(false)
        Result<List<ErpObjectFieldEntity>>  rs = service.createErpObjectField(Lists.asList(entity))
        print(rs)
    }
    @Test
    void updateErpObjectFieldEntity(){
        ErpObjectFieldEntity entity=new ErpObjectFieldEntity();
        entity.setId("614174809865715712")
        entity.setTenantId("79675")
        entity.setErpObjectApiName("testestset232434")
        entity.setChannel(ErpChannelEnum.ERP_U8)
        entity.setFieldApiName("testiti")
        entity.setFieldDefineType(ErpFieldTypeEnum.string)
        entity.setFieldLabel("sjhohd")
        entity.setRequired(false)
        Result<List<ErpObjectFieldEntity>>  rs = service.updateErpObjectField(Lists.asList(entity))
        print(rs)
    }
    @Test
    void deleteErpObjectFieldEntity(){
        ErpObjectFieldEntity entity=new ErpObjectFieldEntity();
        entity.setId("614174809865715712")
        entity.setTenantId("79675")
        entity.setErpObjectApiName("testestset232434")
        entity.setChannel(ErpChannelEnum.ERP_U8)
        entity.setFieldApiName("testiti")
        entity.setFieldDefineType(ErpFieldTypeEnum.string)
        entity.setFieldLabel("sjhohd")
        entity.setRequired(false)
        Result<List<ErpObjectFieldEntity>>  rs = service.deleteErpObjectField(entity)
        print(rs)
    }

    @Test
    void createErpObjectRelationshipEntity(){
        ErpObjectRelationshipEntity entity=new ErpObjectRelationshipEntity();
        entity.setTenantId("79675")
        entity.setChannel(ErpChannelEnum.ERP_U8)
        entity.setErpRealObjectApiname("hshsi")
        entity.setErpSplitObjectApiname("josihdohf")
        entity.setSplitSeq(2)
        entity.setSplitType(ErpObjSplitTypeEnum.NOT_SPLIT)
        Result<List<ErpObjectRelationshipEntity>> rs = service.createErpObjectRelationship(Lists.asList(entity))
        print(rs)
    }

    @Test
    void updateErpObjectRelationshipEntity(){
        ErpObjectRelationshipEntity entity=new ErpObjectRelationshipEntity();
        entity.setId("614177767152025600")
        entity.setTenantId("79675")
        entity.setChannel(ErpChannelEnum.ERP_U8)
        entity.setErpRealObjectApiname("hshsi233434sdfTes23st所属")
        entity.setErpSplitObjectApiname("josihdohf")
        entity.setSplitSeq(2)
        entity.setSplitType(ErpObjSplitTypeEnum.NOT_SPLIT)
        Result<List<ErpObjectRelationshipEntity>> rs = service.updateErpObjectRelationship(Lists.asList(entity))
        print(rs)
    }

    @Test
    void deleteErpObjectRelationshipEntity(){
        ErpObjectRelationshipEntity entity=new ErpObjectRelationshipEntity();
        entity.setId("614177767152025600")
        entity.setTenantId("79675")
        entity.setChannel(ErpChannelEnum.ERP_U8)
        entity.setErpRealObjectApiname("hshsisefdag1234534")
        entity.setErpSplitObjectApiname("josihdohf")
        entity.setSplitSeq(2)
        entity.setSplitType(ErpObjSplitTypeEnum.NOT_SPLIT)
        Result<List<ErpObjectRelationshipEntity>> rs = service.deleteErpObjectRelationship(Lists.asList(entity))
        print(rs)
    }

    @Test
    public void runSqlTest() {
//        String sql11="SELECT * from erp_sync_time WHERE tenant_id = '81138' limit 10 ;"
//        def query = service.superQuery(sql11);
        String sql = "CREATE OR REPLACE FUNCTION \"public\".\"trans_sync_status\"(int4)\n" +
                "  RETURNS \"pg_catalog\".\"int4\" AS \$\$\n" +
                "select CASE WHEN \$1 = 6 THEN 1 WHEN \$1 in (1, 3, 5) THEN 2 ELSE 3 END ;\n" +
                "\$\$\n" +
                "  LANGUAGE sql ;"
        def result = erpTableDao.setTenantId("81243").superUpdateSql(sql)
        println(result)
    }

    @Test
    void tenantRedoIndex(){
        service.redoIndex("-10001")

    }
}
