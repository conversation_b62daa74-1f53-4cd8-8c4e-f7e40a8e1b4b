package com.fxiaoke.open.erpsyncdata.admin.service.impl

import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.admin.arg.GetDataIntegrationNotificationArg
import com.fxiaoke.open.erpsyncdata.admin.service.DataIntegrationNotificationService
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum
import com.fxiaoke.open.erpsyncdata.preprocess.constant.NotificationType
import org.apache.commons.collections4.CollectionUtils
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

@Ignore
class DataIntegrationNotificationServiceImplTest extends BaseSpockTest {
    @Autowired
    private DataIntegrationNotificationService integrationNotificationService
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager
    @Autowired
    private IdGenerator idGenerator

    @Test
    void getDataList() {
        GetDataIntegrationNotificationArg arg = new GetDataIntegrationNotificationArg()
        arg.setTenantId("84801")
        arg.setDcId("780777150699143168")
        arg.setNotificationType(NotificationType.ALERT)
        //arg.setPloyDetailName("物料分组")

        arg.setPage(0)
        arg.setPageSize(100)

        def model = integrationNotificationService.getDataList(arg);
        assert model!=null && CollectionUtils.isNotEmpty(model.data.getDataList())
    }

    @Test
    void checkAdmin() {
        def result = integrationNotificationService.checkAdmin("88521","1005")
        assert result
    }

    @Test
    void getDcList() {
        def list = integrationNotificationService.getDcList("81243")
        assert list.data.isEmpty()==false
    }

    @Test
    void hasData() {
        ErpTenantConfigurationEntity entity = tenantConfigurationManager.findGlobal(TenantConfigurationTypeEnum.DATA_INTEGRATION_NOTIFICATION_ENTRY_WHITE_LIST.name())
        if(entity==null) {
            entity = new ErpTenantConfigurationEntity()
            entity.setId(idGenerator.get())
            entity.setTenantId("0")
            entity.setDataCenterId("0")
            entity.setChannel(ErpChannelEnum.ALL.name())
            entity.setType(TenantConfigurationTypeEnum.DATA_INTEGRATION_NOTIFICATION_ENTRY_WHITE_LIST.name())
            entity.setConfiguration("88521")
            tenantConfigurationManager.insert("88521",entity)
        }

        def result = integrationNotificationService.hasData("88521",1000)
        assert result.data == true
    }
}
