package com.fxiaoke.open.erpsyncdata.admin.service.impl

import com.facishare.converter.EIEAConverter
import com.fxiaoke.open.erpsyncdata.admin.constant.ExcelTypeEnum
import com.fxiaoke.open.erpsyncdata.preprocess.manager.FileManager
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ImportExcelFile
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.easyexcel.CustomCellWriteHandler
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager
import spock.lang.Specification

class FileServiceImplTest extends Specification{
    def ea = "88466"
    def tenantId = 88466
    def dcId = "6436278b3dcc6b0001e76652"
    def npath = "test"



    def "buildExcelTemplate"() {
        given:
        def eieaConverter = Mock(EIEAConverter) {
            enterpriseAccountToId(*_) >> {
                return tenantId
            }
        }

        def fileManager = Mock(FileManager) {
            uploadTnFile(*_) >> {
                //构造一个假数据
                return npath
            }
        }

        I18NStringManager i18NStringManager = new I18NStringManager()
        CustomCellWriteHandler customCellWriteHandler = new CustomCellWriteHandler(i18NStringManager, '88521', null)


        def fileService = new FileServiceImpl(eieaConverter:eieaConverter,
                i18NStringManager:i18NStringManager,
                fileManager:fileManager)

        ImportExcelFile.FieldDataMappingArg arg = new ImportExcelFile.FieldDataMappingArg()
        arg.setDataCenterId(dcId)
        arg.setDataType(dataType)
        arg.setExcelType(excelType)
        arg.setLang(lang)

        def result = fileService.buildExcelTemplate(ea, arg)

        expect:
        result.success && result.getData().getTnFilePath() == npath

        where:
        dataType  |  excelType  |  lang
        null  |  ExcelTypeEnum.INTEGRATION_STREAM_DATA_MAPPING  |  "zh-CN"
    }
}
