package com.fxiaoke.open.erpsyncdata.admin.service.impl

import com.facishare.eservice.base.utils.IJson
import com.fxiaoke.open.erpsyncdata.admin.arg.CreateObjectMappingArg
import com.fxiaoke.open.erpsyncdata.admin.service.K3UltimateEventSubscribeService
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpK3UltimateTokenDao
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum
import com.google.gson.Gson
import spock.lang.Specification

class IntegrationStreamServiceImplTest extends Specification {
    def "createIntegrationStream"() {
        def ea = "88521"
        def tenantId = 88521
        def dcId = "643f7322b54ea80001767d86"
        def npath = "test"
        def userId = -10000

        CreateObjectMappingArg arg = new Gson().fromJson(jsonString, CreateObjectMappingArg.class)

        def connectInfoDao = Mock(ErpConnectInfoDao) {
            setTenantId(_ as String) >> it

            getByIdAndTenantId(String.valueOf(tenantId), "643f7326b54ea8000176a191") >> {
                return ErpConnectInfoEntity.builder()
                        .id("643f7326b54ea8000176a191")
                        .channel(ErpChannelEnum.CRM)
                        .tenantId("88521")
                        .enterpriseName("新企业测试")
                        .dataCenterName("纷享销客")
                        .number(800)
                        .status(1)
                        .build()
            }

            getByIdAndTenantId(String.valueOf(tenantId), "643f7322b54ea80001767d86") >> {
                return ErpConnectInfoEntity.builder()
                        .id("643f7322b54ea80001767d86")
                        .channel(ErpChannelEnum.ERP_K3CLOUD)
                        .tenantId("88521")
                        .enterpriseName("(88521)新企业测试")
                        .dataCenterName("云星空")
                        .number(1)
                        .status(1)
                        .build()
            }
        }

        def adminSyncPloyDetailDao = Mock(AdminSyncPloyDetailDao) {
            setTenantId(_ as String) >> it

            listByDcIdAndObjApiName(*_) >> []

            findSyncPloyDetailByStartWithName(*_) >> {
                SyncPloyDetailEntity entity = new SyncPloyDetailEntity()
                entity.setIntegrationStreamName("集成流名称")
                return [entity]
            }

            listBySourceTenantTypeAndObjApiName(*_) >> []

            insertIgnore(*_) >> 1
        }

        def tenantConfigurationManager = Mock(TenantConfigurationManager) {
            findOne(*_) >> null
        }

        def erpObjectRelationshipDao = Mock(ErpObjectRelationshipDao) {
            setTenantId(_ as String) >> it

            findBySplit(*_) >> new ErpObjectRelationshipEntity()
        }

        def erpK3UltimateTokenDao = Mock(ErpK3UltimateTokenDao) {
            setTenantId(_ as String) >> it

            findData(*_) >> null
        }

        def k3UltimateEventSubscribeService = Mock(K3UltimateEventSubscribeService) {
            genToken(*_) >> null
        }

        def integrationStreamService = new IntegrationStreamServiceImpl(connectInfoDao:connectInfoDao,
                adminSyncPloyDetailDao:adminSyncPloyDetailDao,
                tenantConfigurationManager:tenantConfigurationManager,
                erpObjectRelationshipDao:erpObjectRelationshipDao,
                erpK3UltimateTokenDao:erpK3UltimateTokenDao,
                k3UltimateEventSubscribeService:k3UltimateEventSubscribeService)

        def result = integrationStreamService.createIntegrationStream(String.valueOf(tenantId), arg, lang)

        expect:
        result.success

        where:
        lang    |   jsonString
        "zh-CN"  |  "{\"integrationStreamName\":\"集成流名称\",\"sourceDataCenterId\":\"643f7326b54ea8000176a191\",\"destDataCenterId\":\"643f7322b54ea80001767d86\",\"sourceObjectApiName\":\"WechatWorkExternalUserObj\",\"destObjectApiName\":\"BD_Customer.BillHead_1\",\"syncRules\":{\"syncTypeList\":[\"get\"],\"events\":[1,2]}}"
        "zh-CN"  |  "{\"integrationStreamName\":\"集成流名称\",\"sourceDataCenterId\":\"643f7322b54ea80001767d86\",\"destDataCenterId\":\"643f7326b54ea8000176a191\",\"sourceObjectApiName\":\"BD_Customer.BillHead_1\",\"destObjectApiName\":\"AccountObj\",\"detailObjectMappings\":[{\"sourceObjectApiName\":\"BD_Customer.BD_CUSTCONTACT_1\",\"destObjectApiName\":\"AccountAddrObj\",\"add\":true,\"hiddenTip\":true}],\"syncRules\":{\"syncTypeList\":[\"get\"],\"events\":[1,2,3]},\"currentDcId\":\"643f7322b54ea80001767d86\"}"
    }
}
