package com.fxiaoke.open.erpsyncdata.admin.service.impl

import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.admin.service.superadmin.ToolsService
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

@Ignore
class ToolsServiceImplTest extends BaseSpockTest {
    @Autowired
    private ToolsService toolsService;

    @Test
    void migrateDepartmentData() {
        def result = toolsService.migrateDepartmentData("88521","6488111b7b1fdb0001f04ef7",true)
        println(result)
    }

    @Test
    void migrateEmployeeData() {
        def result = toolsService.migrateEmployeeData("88521","6488111b7b1fdb0001f04ef7",true)
        println(result)
    }
}
