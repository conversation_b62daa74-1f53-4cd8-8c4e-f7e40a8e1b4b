package com.fxiaoke.open.erpsyncdata.apiproxy.service.impl

import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.HeaderManager
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardDetailData
import com.fxiaoke.open.erpsyncdata.apiproxy.service.ErpCustomInterfaceService
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpCustomInterfaceDao
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg
import com.fxiaoke.open.erpsyncdata.preprocess.arg.HeaderFunctionArg
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum
import com.fxiaoke.open.erpsyncdata.preprocess.data.DoWriteMqData
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdFieldKeyManager
import com.fxiaoke.open.erpsyncdata.preprocess.model.IdFieldKey
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

@Ignore
class ErpCustomInterfaceServiceImplTest extends BaseSpockTest {
    @Autowired
    private ErpCustomInterfaceService erpCustomInterfaceService
    @Autowired
    private ErpCustomInterfaceDao erpCustomInterfaceDao
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao
    @Autowired
    private IdFieldKeyManager idFieldKeyManager
    @Autowired
    private HeaderManager headerManager;

    @Test
    void createErpObjData() {
        def data = erpCustomInterfaceDao.findData("81243",
                "63f82854a8bd974420925e83",
                "erpSalesOrderObj",
                ErpObjInterfaceUrlEnum.create)

        def masterData = new ObjectData()
        masterData.putId("id100")
        masterData.put("name","name100")
        masterData.put("field1","field1 value")
        masterData.put("field2","field2 value")
        masterData.put("field3","field3 value")


        def detailDataList = new ArrayList()
        for(int i = 0;i<10;i++) {
            ObjectData objectData1 = new ObjectData()
            objectData1.put("field1","field1 detail value " + i)
            objectData1.put("field2","field2 detail value " + i)
            objectData1.put("field3","field3 detail value " + i)
            detailDataList.add(objectData1)
        }

        Map<String,List<ObjectData>> detailMap = new HashMap<>()
        detailMap.put("erpSalesOrderObj_1gqdstflb",detailDataList)


        def standardData = new StandardData()
        standardData.objAPIName = "erpSalesOrderObj"
        standardData.setMasterFieldVal(masterData)
        standardData.setDetailFieldVals(detailMap)


        def result = erpCustomInterfaceService.createErpObjData(standardData,data)
        println(result)
    }

    @Test
    void createErpObjDetailData() {
        def data = erpCustomInterfaceDao.findData("81243",
                "63f82854a8bd974420925e83",
                "erpSalesOrderObj",
                ErpObjInterfaceUrlEnum.createDetail)

        def detailData = new ObjectData()
        detailData.put("field1","field1 value")
        detailData.put("field2","field2 value")
        detailData.put("field3","field3 value")


        def standardDetailData = new StandardDetailData()
        standardDetailData.objAPIName = "erpSalesOrderObj"
        standardDetailData.masterDataId = "a100"
        standardDetailData.masterDataName = "n100"
        standardDetailData.detailFieldVal = new HashMap<>()
        standardDetailData.detailFieldVal.put("erpSalesOrderObj_1gqdstflb",detailData)


        def doWriteMqData = new DoWriteMqData()
        doWriteMqData.destObjectApiName ="erpSalesOrderObj_1gqdstflb"


        def result = erpCustomInterfaceService.createErpObjDetailData(doWriteMqData,standardDetailData,data)
        println(result)
    }

    @Test
    void getErpObjData() {
        def data = erpCustomInterfaceDao.findData("81243",
                "63f82854a8bd974420925e83",
                "erpSalesOrderObj",
                ErpObjInterfaceUrlEnum.queryMasterById)

        def arg = new ErpIdArg()
        arg.tenantId = "81243"
        arg.objAPIName = "erpSalesOrderObj"
        arg.dataId = "a1000"
        arg.sourceEventType = 2

        def result = erpCustomInterfaceService.getErpObjData(arg, data)
        println(result)
    }

    @Test
    void getReSyncObjDataById() {
        def data = erpCustomInterfaceDao.findData("81243",
                "63f82854a8bd974420925e83",
                "erpSalesOrderObj",
                ErpObjInterfaceUrlEnum.queryMasterById)

        def arg = new ErpIdArg()
        arg.tenantId = "81243"
        arg.objAPIName = "erpSalesOrderObj"
        arg.dataId = "a1000"
        arg.sourceEventType = 2

        def connectInfo = erpConnectInfoDao.getByIdAndTenantId(arg.tenantId, "63f82854a8bd974420925e83")

        def result = erpCustomInterfaceService.getReSyncObjDataById(arg,data,connectInfo)
        println(result)
    }

    @Test
    void listErpObjDataByTime() {
        def data = erpCustomInterfaceDao.findData("81243",
                "63f82854a8bd974420925e83",
                "erpSalesOrderObj",
                ErpObjInterfaceUrlEnum.queryMasterBatch)

        def arg = new TimeFilterArg()
        arg.tenantId = "81243"
        arg.objAPIName = "erpSalesOrderObj"
        arg.operationType=2
        arg.startTime = System.currentTimeMillis()
        arg.endTime = System.currentTimeMillis() + 10 * 60 * 1000
        arg.limit = 100
        arg.offset = 0

        def result = erpCustomInterfaceService.listErpObjDataByTime(arg, data)
        println(result)
    }

    @Test
    void getHeader() {
        def arg = new HeaderFunctionArg()
        arg.tenantId = "81243"
        arg.dataCenterId = "63f82854a8bd974420925e83"
        arg.functionName = "func_header_test__c"
        arg.functionNameSpace="erpdss"
        arg.interfaceUrl = ErpObjInterfaceUrlEnum.create
        def result = headerManager.getHeaderMapByFunction(arg)
        println(result)
    }
}
