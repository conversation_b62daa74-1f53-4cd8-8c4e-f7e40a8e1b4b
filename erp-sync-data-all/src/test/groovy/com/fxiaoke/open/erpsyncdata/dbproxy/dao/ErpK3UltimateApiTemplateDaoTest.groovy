package com.fxiaoke.open.erpsyncdata.dbproxy.dao

import com.alibaba.fastjson.JSONObject
import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.K3UltimateApiTemplateManager
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.K3UltimateObjApiName
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpK3UltimateApiTemplateEntity
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

@Ignore
class ErpK3UltimateApiTemplateDaoTest extends BaseSpockTest {
    @Autowired
    ErpK3UltimateApiTemplateDao erpK3UltimateApiTemplateDao
    @Autowired
    K3UltimateApiTemplateManager k3UltimateApiTemplateManager

    @Test
    void insert() {
        def apiTemplate = k3UltimateApiTemplateManager.getApiTemplate(K3UltimateObjApiName.bd_material)
        def entity = new ErpK3UltimateApiTemplateEntity.ErpK3UltimateApiTemplateEntityBuilder()
        .id("123456789")
        .tenantId("81243")
        .dataCenterId("64feb0e7a8fadb0001cbaf6f")
        .erpObjApiName(K3UltimateObjApiName.bd_material)
        .apiTemplate(JSONObject.toJSONString(apiTemplate))
        .createTime(System.currentTimeMillis())
        .updateTime(System.currentTimeMillis())
        .build()
        def insert = erpK3UltimateApiTemplateDao.insert(entity)
        assert insert > 0
        findData()
    }

    @Test
    void findData() {
        def entity = erpK3UltimateApiTemplateDao.findData("81243",
                "64feb0e7a8fadb0001cbaf6f",
                K3UltimateObjApiName.bd_material)
        assert entity!=null
    }
}
