package com.fxiaoke.open.erpsyncdata.dbproxy.dao

import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpK3UltimateTokenEntity
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

@Ignore
class ErpK3UltimateTokenDaoTest extends BaseSpockTest {
    @Autowired
    ErpK3UltimateTokenDao erpK3UltimateTokenDao

    @Test
    void insert() {
        def entity = new ErpK3UltimateTokenEntity.ErpK3UltimateTokenEntityBuilder()
        .id("123456789")
        .tenantId("81243")
        .dataCenterId("64feb0e7a8fadb0001cbaf6f")
        .erpObjApiName("bd_material")
        .version("1.0")
        .timestamps(System.currentTimeMillis()+"")
        .token("kgdwbb")
        .createTime(System.currentTimeMillis())
        .updateTime(System.currentTimeMillis())
        .build()
        def insert = erpK3UltimateTokenDao.insert(entity)
        assert insert > 0
        findData()
    }

    @Test
    void findData() {
        def entity = erpK3UltimateTokenDao.findByToken("81243", "5395c86c91d0d7bb2899d48aae2351a8")
        assert entity!=null
    }
}
