package com.fxiaoke.open.erpsyncdata.dbproxy.manager

import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjInterfaceCheckedEntity
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceTypeEnum
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

@Ignore
class ErpObjInterfaceCheckedManagerTest extends BaseSpockTest {
    @Autowired
    ErpObjInterfaceCheckedManager erpObjInterfaceCheckedManager

    @Test
    void insertOrUpdate() {
        def entity = new ErpObjInterfaceCheckedEntity.ErpObjInterfaceCheckedEntityBuilder()
                .tenantId("81243")
                .dataCenterId("63f82854a8bd974420925e83")
                .objApiName("erpProductObj")
                .interfaceUrl(ErpObjInterfaceUrlEnum.create)
                .checkedInterfaceType(ErpObjInterfaceTypeEnum.CUSTOM_FUNC)
                .build()
        def insert = erpObjInterfaceCheckedManager.insertOrUpdate(entity)
        println(insert)
        findData()
    }

    @Test
    void findData() {
        def entity = erpObjInterfaceCheckedManager.findOne("81243",
                "63f82854a8bd974420925e83",
                "erpSalesOrderObj",
                ErpObjInterfaceUrlEnum.create)
        assert entity!=null
        println(entity)
    }
}
