package com.fxiaoke.open.erpsyncdata.dbproxy.manager

import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.google.common.collect.Sets
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

@Ignore
class MonitorReportManagerTest extends BaseSpockTest {
    @Autowired
    private MonitorReportManager monitorReportManager

    @Test
    void sendEnterTempDataMsg() {
        monitorReportManager.sendEnterTempDataMsg("84801","780777150699143168","SAL_SaleOrder", Sets.newHashSet("103917"),false,System.currentTimeMillis(),20,"入库");
        println ""
    }

}
