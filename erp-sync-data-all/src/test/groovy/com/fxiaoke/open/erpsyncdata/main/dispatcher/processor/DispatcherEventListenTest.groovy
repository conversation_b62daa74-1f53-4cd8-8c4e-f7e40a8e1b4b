package com.fxiaoke.open.erpsyncdata.main.dispatcher.processor


import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DispatcherEventData
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SpeedLimitManager
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent
import org.springframework.test.util.ReflectionTestUtils;
import spock.lang.Specification
import spock.lang.Unroll


/**
 * <AUTHOR> @Date: 11:34 2023/6/9
 * @Desc:
 */

class DispatcherEventListenTest extends Specification {
    private static final String TENANT_ID = "tenant123";
    private static final String SOURCE_OBJ_API_NAME = "sourceObj123";
    DispatcherEventListen dispatcherEventListen;
    DispatcherEventData event = DispatcherEventData.builder().tenantId(TENANT_ID).apiName(SOURCE_OBJ_API_NAME).build();

    def "setup" () {
        dispatcherEventListen = new DispatcherEventListen(
                syncMainService: Mock(com.fxiaoke.open.erpsyncdata.preprocess.service.SyncMainService){},
                speedLimitManager : Mock(SpeedLimitManager) {
                    getRemainLimit(*_)>>{return 5;}
                }
        );
    }

    private void callPrivateMethod(String methodName, Object... args) {
        ReflectionTestUtils.invokeMethod(dispatcherEventListen, methodName, args);
    }

    def createEvent(long dispatchTime, int tries) {
        def event = new DispatcherEventData()
        event.setDispatchTime(dispatchTime)
        event.setTries(tries)
        return event
    }
    @Unroll
    def "delayDispatch with different retry values"() {
        given:
        def originalDispatchTime = System.currentTimeMillis()
        def event1 = createEvent(originalDispatchTime, 0)
        def event2 = createEvent(originalDispatchTime, 100)

        when:
        callPrivateMethod("delayDispatch", TENANT_ID, event1)
        callPrivateMethod("delayDispatch", TENANT_ID, event2)

        then:
        event1.getDispatchTime() <= System.currentTimeMillis()
        event2.getDispatchTime() > System.currentTimeMillis()
    }

    @Unroll
    def "ConvertFieldValueId"() {
        DispatcherEventListen dispatcherEventListen = new DispatcherEventListen();
        DispatcherEventData m= new DispatcherEventData();
        m.setEventTypeList(eventTypes)
        when:
        List<SyncDataContextEvent> list= dispatcherEventListen.getEventDataList(m)
        then:
        result.size() == list.size();
        for(int i=0;i<result.size();i++) {
            result.get(i)==list.get(i).getSourceEventType()
        }
        //result有多次说明要分别按顺序执行多次
        where:
        name                                         | eventTypes     | result
        "包含删除+不包含作废->删除"                  | [1, 9]         | [9]
        "包含删除+包含作废->先作废再删除"            | [3, 9]         | [3, 9]
        "包含恢复+包含作废+不包含其他->忽略"         | [8, 3]         | []
        "包含恢复+包含作废-包含其他->其他"           | [8, 3, 2]      | [2]
        "包含恢复+包含作废-包含其他->其他"           | [8, 3, 2, 1] | [2, 1]
        "包含恢复+不包含作废+不包含更新->恢复"       | [8]            | [8]
        "包含恢复+不包含作废+包含更新->先恢复再更新" | [8, 2]         | [8, 2]
        "包含作废->作废"                             | [1, 3]         | [3]
        "包含新增+不包含更新->新增"                  | [1]       | [1]
        "包含新增+包含更新->先更新再新增"            | [1, 2]         | [2, 1]
        "只包含更新->更新"                           | [2]            | [2]

    }


    // 参数化测试数据
    @Unroll
    def "test discardEventDirectly with different configurations"() {
        given:
        ErpTenantConfigurationEntity entity = new ErpTenantConfigurationEntity()
        if (config != null) {
            entity.setConfiguration(config)
        }

        when:
        boolean result = dispatcherEventListen.discardEventDirectly(TENANT_ID, SOURCE_OBJ_API_NAME, entity)

        then:
        result == expectedResult

        where:
        config                             | expectedResult
        null                               | false
        ""                                 | false
        "${TENANT_ID}_${SOURCE_OBJ_API_NAME};otherKey" | true
        "otherTenantId_otherSourceObjAPIName;anotherKey" | false
        "invalid;config;format"            | false
    }

    // Additional cases can be added to cover more edge cases
}



