package com.fxiaoke.open.erpsyncdata.support

import com.alibaba.fastjson.JSONObject
import com.fxiaoke.crmrestapi.arg.ActionAddArg
import com.fxiaoke.crmrestapi.common.contants.CrmConstants
import com.fxiaoke.crmrestapi.common.data.HeaderObj
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.fxiaoke.crmrestapi.common.result.Result
import com.fxiaoke.crmrestapi.result.ActionAddResult
import com.fxiaoke.crmrestapi.result.ObjectDataGetByIdResult
import com.fxiaoke.crmrestapi.service.MetadataActionService
import com.fxiaoke.crmrestapi.service.ObjectDataService
import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import groovy.util.logging.Slf4j
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/9/25
 */
@Ignore
@Slf4j
class CrmRestApiTest extends BaseSpockTest {

    @Autowired
    private MetadataActionService metadataActionService
    @Autowired
    private ObjectDataService objectDataService;


    @Test
    public void test1() {
        String tenantId="82814";
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
        com.fxiaoke.crmrestapi.common.result.Result<ObjectDataGetByIdResult> resultResult = objectDataService.getById(headerObj, "SalesOrderProductObj", "61d7ee677b4c760001a5dd9d", false, true, true, false);
        log.info("{}",resultResult);
        println (JSONObject.toString(resultResult))
    }

    @Test
    void test22() {
        String json = "{\n" +
                "    \"account_no\": \"********-000001\",\n" +
                "    \"field_4qs65__c__r\": \"纷享销客\",\n" +
                "    \"tenant_id\": \"82777\",\n" +
                "    \"deal_status\": \"3\",\n" +
                "    \"address\": \"address345\",\n" +
                "    \"life_status\": \"normal\",\n" +
                "    \"transfer_count\": \"1\",\n" +
                "    \"account_status\": \"3\",\n" +
                "    \"name\": \"82777_客户1\",\n" +
                "    \"_id\": \"610625b484bcf20001e237fd\",\n" +
                "    \"account_level\": \"3\",\n" +
                "    \"object_describe_api_name\": \"AccountObj\",\n" +
                "    \"owner\": [\n" +
                "        \"1000\"\n" +
                "    ],\n" +
                "    \"out_owner\": [\n" +
                "        \"1000\"\n" +
                "    ],\n" +
                "    \"record_type\": \"default__c\",\n" +
                "    \"remark\":\"hello world\"\n" +
                "}"
        ObjectData objectData = JSONObject.parseObject(json,ObjectData.class);

//        HeaderObj headerObj = new HeaderObj(82777, CrmConstants.SYSTEM_USER);
//        ActionEditArg2 arg = new ActionEditArg2();
//        arg.setObjectData(objectData)
//        arg.setFillOutOwner(true)
//        def result = metadataActionService.edit(headerObj, CrmObjectApiName.ACCOUNT_API_NAME, arg)
        println(result)
    }

    @Test
    void addBatchStock() {
        HeaderObj headerObj = new HeaderObj(81961, CrmConstants.SYSTEM_USER);
        ActionAddArg addArg = new ActionAddArg()
        ObjectData data = new ObjectData()
//        data.put("product_id","5f60a7c277d7900001b3b370")
//        data.put("warehouse_id","5f6adf5477d7900001be2536")
//        data.put("batch_id","********")
//        data.put("batch_real_stock",10)

        data.put("product_id","6041e4d6d215ff000161e541")
        data.put("warehouse_id","6040d7c0d215ff00015ef175")
        data.put("batch_id","6042095ad215ff000162341c")
        data.put("batch_real_stock",100)
        data.setOwner(1000)
        addArg.setObjectData(data)
        Result<ActionAddResult> result = metadataActionService.add(headerObj, "BatchStockObj",false,false, addArg);
        log.info("result: {}", result);
    }

    @Test
    void addBatch() {
        HeaderObj headerObj = new HeaderObj(79558, CrmConstants.SYSTEM_USER);
        ActionAddArg addArg = new ActionAddArg()
        ObjectData data = new ObjectData()
        data.setName("20200927-xjybatch001")
        data.put("product_id","5f70293683ecb900019badc2")
        data.setOwner(1000)
        addArg.setObjectData(data)
        Result<ActionAddResult> result = metadataActionService.add(headerObj, "BatchObj",false,false, addArg);
        log.info("result: {}", result);
    }

    @Test
    void addWarehouse() {
        HeaderObj headerObj = new HeaderObj(79558, CrmConstants.SYSTEM_USER);
        ActionAddArg addArg = new ActionAddArg()
        ObjectData data = new ObjectData()
        data.setName("深圳大冲仓001")
        data.put("number","dachong001")
        data.put("is_default",true)
        data.put("is_enable",1)
        data.setOwner(1000)
        addArg.setObjectData(data)
        Result<ActionAddResult> result = metadataActionService.add(headerObj, "WarehouseObj",false,false, addArg);
        log.info("result: {}", result);
    }
}
