package com.fxiaoke.skywalking

import com.alibaba.fastjson.JSONObject
import com.fxiaoke.open.erpsyncdata.common.rule.ConditionUtil
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.util.interceptorUtil.SecurityUtil
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.googlecode.aviator.AviatorEvaluator
import org.junit.Ignore
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR> @Date 2022/11/14 17:37
 * @Version 1.0
 */
@Ignore
class ConditionSpec extends  Specification{


    def "测试生成condition表达式"(){
        given:
        FilterData filterData1= JSONObject.parseObject("{\"label\":\"金额\",\"operate\":\"GT\",\"fieldType\":\"currency\",\"fieldValue\":[\"10\"],\"fieldApiName\":\"field_FY6bw__c\",\"isVariableBetween\":false}");
        FilterData filterData2= JSONObject.parseObject("{\"label\":\"金额\",\"operate\":\"GT\",\"fieldType\":\"currency\",\"fieldValue\":[\"10\"],\"fieldApiName\":\"field_FY6bw__c\",\"isVariableBetween\":false}");
        List<List<FilterData>> filters= Lists.newArrayList();
        filters.add(Lists.newArrayList(filterData1,filterData2))
        when:
        def message= ConditionUtil.parseToOrExpression(filters)
        println message
        then:
        1==1;

    }

    def "测试aviator表达式"(){
        given:
        def expression = "a-(b-c) > 100";
        def envMap= Maps.newHashMap();
        envMap.put("a",100.3);
        envMap.put("b",45);
        envMap.put("c",-199.100);
        def json=JSONObject.parseObject("{\"channel\":\"ERP_K3CLOUD\",\"connectParams\":\"{\\\"baseUrl\\\":\\\"http://172.31.100.60/k3cloud/\\\",\\\"dbId\\\":\\\"5bd164c806c6c8\\\",\\\"dbName\\\":\\\"接口环境（新）\\\",\\\"authType\\\":1,\\\"userName\\\":\\\"ces2\\\",\\\"password\\\":\\\"8888888\\\",\\\"lcid\\\":2052,\\\"useFsHttpClient\\\":true}\",\"dataCenterName\":\"金蝶K3Cloud\",\"enterpriseName\":\"库存专测3\",\"id\":\"754388706949464064\",\"tenantId\":\"82712\"}",ErpConnectInfoEntity.class)
        when:
        boolean compiledExp=  AviatorEvaluator.execute(expression, envMap, false);
        System.out.println(compiledExp);
        // Execute with injected variables.
       then:
       with(compiledExp){
            compiledExp==true;
       }

    }

    def "获取encode"() {
        expect:
        println SecurityUtil.getAes().encryptBase64("{\"accountId\":\"*********\",\"expire\":*************,\"refreshToken\":\"AQWzd-DSb3_IYM0S7E59BhGZNC8NFGW8gt8NEgNF06sSsAN9aSlzXGg4z9mVJ7bOxdTTUL9OM7Hq-E16ja21_PUIfDfjNrAQDYc1QBY92vGKad6LC9tlmPH5LwrZ1IUyGVLDNGQx_QJ_wFgm74_33sR8MZo8cquuCecxY8Mzz-vgaMFdhf99zNvWcWi0SLnSUegIV7a2D3Stvhm5oRwrGemcUdRmOG_R_LFZqGJFeMOh7n_Az6tzdDFac61V_Lo9AXo_Bl2aCWaPOwi_Z3w4nwhOyDkDMgfed4R5Yr0oRQnHCDM095Kk2tHLLJJxFA2jZmoGzFOL6S8EvqR9UrWUQK4hCVa48Q\",\"refreshTokenExpire\":*************,\"token\":\"AQWyURThhiEePye8NZPzoTjcHKpcyIUxOrHb0PUjiXcUN2ptfIAefOGjqYFVT4ONOkWN_DyW_9TwHek-fW1uzKrQophPnSFKcr34tllc9DtpJgXel822OP01YM3qhN3hw9sMAh68jsOIw8dL8vyx7ri_DDc_FOPw5AX2tGkX4wHGSmR5YzLBhDi-us7LqXuMfcIzqTRfihlcOD5CQuf4zVbo5mCKzEF4mYjGEyHm3IXqJTMMb_DRjY2RbrvwckQlRbEGN-C2eqBXs4WHD6BciEsASRfEZ4Ga6Z9oBZYEbWtPrdjiC1vS77YQ8rUcHrWjUxGCR_TdatBAaBo1ww4OvjyIMbuVfw\"}")
    }

    @Unroll
    def "校验avaiator表达式 #uid"(){
        given:
        def conditionExpress="(field_9m25X__c ==-11)";
        def envMap= valueMap
        when:
        boolean compiledExp=  AviatorEvaluator.execute(conditionExpress, envMap, false);
        System.out.println(compiledExp);
        // Execute with injected variables.
        then:
        with(compiledExp){
            compiledExp==type;
        }
        where: "表格方式验证订单信息的分支场景"
        uid | valueMap                 || type
        2   | ["field_9m25X__c": "-11"] || true
        2   | ["field_9m25X__c": "0.11"] || true
        2   | ["field_9m25X__c": "-11"] || true
        2   | ["field_9m25X__c": "1e-3"] || true
        2   | ["field_9m25X__c": "0000t"] || true
        2   | ["field_9m25X__c": "100%"] || true
    }

    def "校验avaiator表达式2"(){
        given:
        def conditionExpress="(field_9m25X__c != nil  && field_9m25X__c != '')";
        def envMap= Maps.newHashMap();
        envMap.put("field_9m25X__c","3")
        when:
        boolean compiledExp=  AviatorEvaluator.execute(conditionExpress, envMap, false);
        System.out.println(compiledExp);
        // Execute with injected variables.
        then:
        with(compiledExp){
            compiledExp==true;
        }

    }

    def "校验avaiator表达式数字类型"(){
        given:
//        BigDecimal a1 = new BigDecimal(100);
//        BigDecimal a2  = new BigDecimal("60.00");
//        BigDecimal a3  = new BigDecimal("-60.000001");
//        def com1=a1>a2;
//        def com2=a1>=100;
//        def com3=a1>a3;

//        BigDecimal bd = new BigDecimal("3.40256010353E14");
//        println bd.toPlainString();
//        BigDecimal bd2 = new BigDecimal("1e-2");
//        println bd2.toPlainString();
//        def decimal = new BigDecimal("1.03E+08")
//        def decimal2= BigDecimal.valueOf(1000L)
        def conditionExpress="(field_FY6bw__c > 11110.0M)";
        def envMap= Maps.newHashMap();
        envMap.put("field_FY6bw__c","-11.00")
        when:
        boolean compiledExp=  AviatorEvaluator.execute(conditionExpress, envMap, false);
        System.out.println(compiledExp);
        // Execute with injected variables.
        then:
        with(compiledExp){
            compiledExp==true;
        }

    }


    def "校验toString转成json"(){

        given:
        String data="{\"data\":\"00\",\n" +
                "\"number\":1}";
        Map<String, String> headerMap=JSONObject.parseObject(data, Map.class)
        System.out.println(headerMap)

//        BigDecimal bd = new BigDecimal("3.40256010353E14");
//        println bd.toPlainString();
//        BigDecimal bd2 = new BigDecimal("1e-2");
//        println bd2.toPlainString();
//        def decimal = new BigDecimal("1.03E+08")
//        def decimal2= BigDecimal.valueOf(1000L)
        def conditionExpress="(field_FY6bw__c > 11110.0M)";
        def envMap= Maps.newHashMap();
        envMap.put("field_FY6bw__c","-11.00")
        when:
        boolean compiledExp=  AviatorEvaluator.execute(conditionExpress, envMap, false);
        System.out.println(compiledExp);
        // Execute with injected variables.
        then:
        with(compiledExp){
            compiledExp==true;
        }

    }





}
