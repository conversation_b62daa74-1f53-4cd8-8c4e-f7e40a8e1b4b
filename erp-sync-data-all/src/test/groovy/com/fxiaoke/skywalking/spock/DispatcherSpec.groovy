package com.fxiaoke.skywalking.spock

import com.alibaba.fastjson.JSONObject
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DispatcherEventData
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpObjManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager
import com.fxiaoke.open.erpsyncdata.main.dispatcher.processor.DispatcherEventListen
import com.fxiaoke.open.erpsyncdata.main.dispatcher.processor.ProcessLimiter
import com.fxiaoke.open.erpsyncdata.main.service.CRMOuterServiceImpl
import com.fxiaoke.open.erpsyncdata.preprocess.manager.IdFieldConvertManager
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService
import com.fxiaoke.open.erpsyncdata.preprocess.service.OverrideOuterService
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncMainService
import com.google.common.collect.Lists
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR> @Date 2022/11/14 17:37
 * @Version 1.0
 */
class DispatcherSpec extends  Specification{

     ThreadLocal<Map<String, ObjectData>> CRM_OBJECT_DATA_LOCAL = new ThreadLocal<>();
    def crmOuterService=Mock(CRMOuterServiceImpl)
    def syncMainService=Mock(SyncMainService)
    def overrideOuterService=Mock(OverrideOuterService)
    def processLimiter=Mock(ProcessLimiter)
    def idFieldConvertManager=Mock(IdFieldConvertManager)
    def objectDataServiceV3=Mock(ObjectDataServiceV3)
    def erpObjManager=Mock(ErpObjManager)
    def notificationService=Mock(NotificationService)


    def dispatcherEventListen=new DispatcherEventListen(syncMainService: syncMainService,overrideOuterService: overrideOuterService,
            processLimiter: processLimiter,objectDataServiceV3: objectDataServiceV3,erpObjManager: erpObjManager,idFieldConvertManager: idFieldConvertManager,notificationService: notificationService,crmOuterService:crmOuterService,
            tenantConfigurationManager: Mock(TenantConfigurationManager));


    @Unroll
    def "校验批量获取crm的数据"(){
        given:


         "mock getEntryBySnapshotId返回的结果"
        dispatcherEventListen.batchGetObjectData(*_) >> batchGetObjectDataAndCache(eventValue.dataId,eventValue.apiName,eventValue.tenantId,new ObjectData())
         "mock getRelation返回的结果"
        overrideOuterService.needDispatcherNow(*_) >> Result2.newSuccess(true)
        idFieldConvertManager.getRealObjApiName(*_) >> "ErpObj"
        syncMainService.syncDataMain(*_) >> Result2.newSuccess();
        processLimiter.permitProcess(*_) >> true;
        crmOuterService.removeBatchCache(*_) >> null;
        when :

        dispatcherEventListen.listen(Lists.newArrayList(eventValue))
        then:
        println(CRM_OBJECT_DATA_LOCAL.get().toString())
        CRM_OBJECT_DATA_LOCAL.get().size()==result
        where:
        eventValue|result
        getEventValue()|1

    }

    DispatcherEventData getEventValue(){
        List<DispatcherEventData> events = Lists.newArrayList();
        DispatcherEventData dispatcherEventData=new DispatcherEventData();
        dispatcherEventData.setApiName("AccountObj");
        dispatcherEventData.setTenantType(TenantTypeEnum.CRM.getType());
        dispatcherEventData.setDataId("63f871e72d740c0001018735");
        dispatcherEventData.setTenantId("84801");
        ObjectData objectData=new ObjectData();
        objectData.put("remark","备注测试");
        objectData.put("object_describe_api_name","AccountObj");
        objectData.put("_id","63f871e72d740c0001018735");
        objectData.put("modifiedTime",System.currentTimeMillis()-100);
        List<String> dataJson=Lists.newArrayList(JSONObject.toJSONString(objectData));
        dispatcherEventData.setUpdateJson(dataJson);
        dispatcherEventData.setModifiedTime(System.currentTimeMillis());
        dispatcherEventData.setEventType(EventTypeEnum.UPDATE.getType());
        dispatcherEventData.setEventTypeList(Lists.newArrayList(EventTypeEnum.UPDATE.getType()));
        events.add(dispatcherEventData);
        return dispatcherEventData;
    }

     void batchGetObjectDataAndCache(String objectId,String apiName,String tenantId,ObjectData objectData){
        Map<String, ObjectData> cache = getCache();
        String combineKey=new StringBuilder().append(tenantId).append("--").append(apiName).append("--").append(objectId).toString();
        cache.put(combineKey,objectData);

    }

     Map<String, ObjectData> getCache() {
        Map<String, ObjectData> stringObjectDataMap = CRM_OBJECT_DATA_LOCAL.get();
        if (stringObjectDataMap ==null){
            stringObjectDataMap = new HashMap<>();
            CRM_OBJECT_DATA_LOCAL.set(stringObjectDataMap);
        }
        return stringObjectDataMap;
    }
    public Result2<Void> removeBatchCache() {
        CRM_OBJECT_DATA_LOCAL.remove();
        return null;
    }








}
