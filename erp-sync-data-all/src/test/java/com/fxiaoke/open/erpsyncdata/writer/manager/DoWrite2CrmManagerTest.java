package com.fxiaoke.open.erpsyncdata.writer.manager;

import com.facishare.eservice.base.constant.Constant;
import com.fxiaoke.crmrestapi.arg.ActionAddArg;
import com.fxiaoke.crmrestapi.arg.ActionEditArg;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.result.ActionEditResult;
import com.fxiaoke.crmrestapi.result.BulkDeleteResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.converter.manager.SyncDataManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.SpeedLimitTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SpeedLimitManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataMappingsManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.factory.OuterServiceFactory;
import com.fxiaoke.open.erpsyncdata.preprocess.impl.SyncSkuSpuServiceImpl;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncPloyDetailSnapshotService;
import com.fxiaoke.open.erpsyncdata.writer.manager.BatchWriteCRMManager;
import com.fxiaoke.open.erpsyncdata.preprocess.service.OuterService;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncPloyDetailSnapshotData2;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncPloyDetailData2;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

/**
 * DoWrite2CrmManager 的单元测试类
 *
 * 测试原则：
 * 1. 不依赖真实的外部服务调用
 * 2. 使用 Mock 对象模拟所有外部依赖
 * 3. 只测试当前类的业务逻辑
 * 4. 测试用例要独立且可重复执行
 * 5. 测试各个依赖服务的交互，而不是调用真实的业务方法
 */
public class DoWrite2CrmManagerTest {

    @InjectMocks
    private DoWrite2CrmManager doWrite2CrmManager;

    @Mock
    private MetadataActionService metadataActionService;

    @Mock
    private MetadataControllerService metadataControllerService;

    @Mock
    private ObjectDataService objectDataService;

    @Mock
    private SpeedLimitManager speedLimitManager;

    @Mock
    private ConfigCenterConfig configCenterConfig;

    @Mock
    private SyncDataManager syncDataManager;

    @Mock
    private SyncDataMappingsManager syncDataMappingsManager;

    @Mock
    private BatchWriteCRMManager batchWriteCRMManager;

    @Mock
    private TenantConfigurationManager tenantConfigurationManager;

    @Mock
    private I18NStringManager i18NStringManager;

    @Mock
    private SyncSkuSpuServiceImpl skuSpuService;

    @Mock
    private SyncPloyDetailSnapshotService syncPloyDetailSnapshotService;

    @Mock
    private OuterServiceFactory outerServiceFactory;

    @Mock
    private OuterService outerService;

    /**
     * 创建测试用的事件对象
     */
    private SyncDataContextEvent createTestEvent(String tenantId, String objectApiName, Integer eventType) {
        SyncDataContextEvent event = new SyncDataContextEvent();
        event.setTenantId(tenantId);
        event.setDestTenantId(tenantId);
        event.setDestTenantType(TenantType.CRM);
        event.setDestObjectApiName(objectApiName);
        event.setDestEventType(eventType);
        event.setSyncDataId("sync_data_" + System.currentTimeMillis());

        // 创建目标数据对象
        com.fxiaoke.open.erpsyncdata.common.data.ObjectData destData =
            new com.fxiaoke.open.erpsyncdata.common.data.ObjectData();
        destData.putApiName(objectApiName);
        event.setDestData(destData);

        event.setSyncDataMap(new HashMap<>());
        event.setSyncPloyDetailSnapshotId("test_snapshot_id");
        return event;
    }

    /**
     * 创建Mock的同步策略详情快照结果
     */
    private Result2<SyncPloyDetailSnapshotData2> createMockSyncPloyDetailSnapshotResult() {
        SyncPloyDetailSnapshotData2 snapshotData = new SyncPloyDetailSnapshotData2();
        snapshotData.setId("test_snapshot_id");
        snapshotData.setSourceTenantId("123");
        snapshotData.setDestTenantId("123");
        snapshotData.setSourceObjectApiName("SourceObject");
        snapshotData.setDestObjectApiName("TestObject");
        snapshotData.setStatus(1); // 启用状态

        SyncPloyDetailData2 detailData = new SyncPloyDetailData2();
        detailData.setId("test_detail_id");
        detailData.setSourceObjectApiName("SourceObject");
        detailData.setDestObjectApiName("TestObject");
        detailData.setStatus(1);
        snapshotData.setSyncPloyDetailData(detailData);

        return Result2.newSuccess(snapshotData);
    }

    /**
     * 测试前的初始化设置
     * 配置默认的Mock行为，确保测试环境的一致性
     *
     * 注意：使用 MockitoAnnotations.openMocks(this) 手动初始化 @Mock 和 @InjectMocks
     * @InjectMocks 会通过反射自动创建 doWrite2CrmManager 并注入所有 @Mock 依赖
     */
    @Before
    public void setUp() {
        // 手动初始化 Mockito 注解
        MockitoAnnotations.openMocks(this);
        // 配置批量写入 - 默认不启用批量写入
        when(configCenterConfig.getBatchWrite2CrmTenantAndObjApiName()).thenReturn(new HashMap<>());

        // 默认速率限制检查通过（不超速）
        when(speedLimitManager.countAndCheck(anyString(), any(), anyLong(), eq(false))).thenReturn(false);

        // 默认 SKU/SPU 服务处理成功
        when(skuSpuService.handleSkuSpu2Crm(any())).thenAnswer(invocation -> invocation.getArgument(0));

        // 默认国际化字符串处理
        when(i18NStringManager.get(anyString(), anyString(), anyString(), anyString())).thenAnswer(invocation -> invocation.getArgument(3));
        when(i18NStringManager.getByEi(anyString(), anyString(), anyString())).thenAnswer(invocation -> invocation.getArgument(2));

        // Mock 同步策略详情快照服务
        when(syncPloyDetailSnapshotService.getSyncPloyDetailSnapshotBySnapshotId(anyString(), anyString()))
            .thenReturn(createMockSyncPloyDetailSnapshotResult());

        // Mock 租户配置管理器
        when(tenantConfigurationManager.getNotUsePaasAddApiName(anyString())).thenReturn(new HashSet<>());

        // Mock 外部服务工厂
        when(outerServiceFactory.get(anyInt())).thenReturn(outerService);
        when(outerService.getMasterObjectApiName(anyString(), anyInt(), anyString()))
            .thenReturn(Result2.newSuccess("TestObject"));
    }

    /**
     * 测试速率限制检查功能
     * 这个测试验证速率限制管理器的调用是否正确
     */
    @Test
    public void testSpeedLimitCheck() {
        // 准备测试数据
        String tenantId = "123";

        // 测试速率限制检查通过的情况
        when(speedLimitManager.countAndCheck(eq(tenantId), any(SpeedLimitTypeEnum.class), eq(1L),false))
            .thenReturn(false);

        boolean isLimited = speedLimitManager.countAndCheck(tenantId, SpeedLimitTypeEnum.TO_CRM, 1L,false);

        // 验证结果
        assertFalse("速率限制检查应该通过", isLimited);
        verify(speedLimitManager).countAndCheck(eq(tenantId), any(SpeedLimitTypeEnum.class), eq(1L),false);
    }

    /**
     * 测试速率限制超限的情况
     */
    @Test
    public void testSpeedLimitExceeded() {
        // 准备测试数据
        String tenantId = "123";

        // 测试速率限制超限的情况
        when(speedLimitManager.countAndCheck(eq(tenantId), any(SpeedLimitTypeEnum.class), eq(1L),false))
            .thenReturn(true);

        boolean isLimited = speedLimitManager.countAndCheck(tenantId, SpeedLimitTypeEnum.TO_CRM, 1L,false);

        // 验证结果
        assertTrue("速率限制检查应该超限", isLimited);
        verify(speedLimitManager).countAndCheck(eq(tenantId), any(SpeedLimitTypeEnum.class), eq(1L),false);
    }

    /**
     * 测试 DoWrite2CrmManager 通过 MetadataActionService 创建 CRM 数据的业务逻辑
     *
     * 测试原则：
     * 1. 测试真实的业务逻辑，而不是直接调用 Mock 对象
     * 2. 验证 DoWrite2CrmManager.writeToCRM() 方法正确调用了 metadataActionService.add()
     * 3. 验证传递给 metadataActionService.add() 的参数是否正确
     * 4. 验证业务逻辑处理返回结果的行为
     */
    @Test
    public void testWriteToCrmWithMetadataActionServiceCreate() {
        // 准备测试数据 - 创建一个新增事件
        String tenantId = "123";
        String objectApiName = "TestObject";
        SyncDataContextEvent event = createTestEvent(tenantId, objectApiName, EventTypeEnum.ADD.getType());

        // 设置目标数据
        event.getDestData().put("name", "Test Object Name");
        event.getDestData().put("status", "active");
        event.getDestData().put("owner", 1000);

        // Mock MetadataActionService.add() 方法的返回结果
        com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> addResult = new com.fxiaoke.crmrestapi.common.result.Result<>();
        addResult.setCode(com.fxiaoke.crmrestapi.common.result.Result.SUCCESS_CODE);
        ActionAddResult actionAddResult = new ActionAddResult();
        ObjectData createdData = new ObjectData();
        createdData.put("_id", "created_id_123");
        createdData.put("name", "Test Object Name");
        createdData.put("status", "active");
        actionAddResult.setObjectData(createdData);
        addResult.setData(actionAddResult);

        // 配置 Mock 行为 - 使用正确的 add 方法签名
        when(metadataActionService.add(any(HeaderObj.class), eq(objectApiName), anyBoolean(), anyBoolean(), any(), any(), any(ActionAddArg.class)))
            .thenReturn(addResult);

        // 执行测试 - 调用真实的业务方法
        doWrite2CrmManager.writeToCRM(event);

        // 验证结果
        assertNotNull("事件的写入结果不应为空", event.getWriteResult());
        assertTrue("写入应该成功", event.getWriteResult().isSuccess());
        assertEquals("返回的数据ID应该正确", "created_id_123", event.getWriteResult().getDestDataId());

        // 验证 metadataActionService.add() 被正确调用
        verify(metadataActionService).add(
            any(HeaderObj.class),
            eq(objectApiName),
            anyBoolean(),
            anyBoolean(),
            any(),
            any(),
            any()
        );

        // 验证其他依赖服务的调用
        verify(speedLimitManager).countAndCheck(eq(tenantId), any(), anyLong(),false);
        verify(skuSpuService).handleSkuSpu2Crm(eq(event));
    }

    /**
     * 测试 MetadataActionService 更新操作的 Mock 行为
     */
    @Test
    public void testMetadataActionServiceEdit() {
        // 准备测试数据
        String objectApiName = "TestObject";
        HeaderObj headerObj = new HeaderObj(123, Constant.SYSTEM_USER_ID);

        // Mock 更新操作的返回结果
        com.fxiaoke.crmrestapi.common.result.Result<ActionEditResult> editResult = new com.fxiaoke.crmrestapi.common.result.Result<>();
        editResult.setCode(com.fxiaoke.crmrestapi.common.result.Result.SUCCESS_CODE);
        ActionEditResult actionEditResult = new ActionEditResult();
        ObjectData updatedData = new ObjectData();
        updatedData.put("_id", "test_id_456");
        updatedData.put("name", "Updated Object");
        actionEditResult.setObjectData(updatedData);
        editResult.setData(actionEditResult);

        when(metadataActionService.edit(any(HeaderObj.class), eq(objectApiName), anyBoolean(), anyBoolean(), any(), any(), any()))
            .thenReturn(editResult);

        // 执行测试
        ActionEditArg editArg = new ActionEditArg();
        com.fxiaoke.crmrestapi.common.result.Result<ActionEditResult> result = metadataActionService.edit(headerObj, objectApiName, false, false, null, null, editArg);

        // 验证结果
        assertNotNull("更新结果不应为空", result);
        assertTrue("更新应该成功", result.isSuccess());
        assertEquals("返回的数据ID应该正确", "test_id_456", result.getData().getObjectData().getId());
        verify(metadataActionService).edit(any(HeaderObj.class), eq(objectApiName), anyBoolean(), anyBoolean(), any(), any(), any(ActionEditArg.class));
    }

    /**
     * 测试批量写入配置检查
     */
    @Test
    public void testBatchWriteConfigCheck() {
        // 准备测试数据
        String tenantId = "123";
        String objectApiName = "TestObject";

        // 测试批量写入配置为空的情况
        when(configCenterConfig.getBatchWrite2CrmTenantAndObjApiName()).thenReturn(new HashMap<>());

        Map<String, java.util.List<String>> batchConfig = configCenterConfig.getBatchWrite2CrmTenantAndObjApiName();

        // 验证结果
        assertNotNull("批量写入配置不应为空", batchConfig);
        assertTrue("批量写入配置应该为空Map", batchConfig.isEmpty());
        verify(configCenterConfig).getBatchWrite2CrmTenantAndObjApiName();
    }

    /**
     * 测试批量写入配置启用的情况
     */
    @Test
    public void testBatchWriteConfigEnabled() {
        // 准备测试数据
        String tenantId = "123";
        String objectApiName = "TestObject";

        // 配置批量写入启用
        HashMap<String, java.util.List<String>> batchConfig = new HashMap<>();
        batchConfig.put(tenantId, java.util.Arrays.asList(objectApiName));
        when(configCenterConfig.getBatchWrite2CrmTenantAndObjApiName()).thenReturn(batchConfig);

        Map<String, java.util.List<String>> result = configCenterConfig.getBatchWrite2CrmTenantAndObjApiName();

        // 验证结果
        assertNotNull("批量写入配置不应为空", result);
        assertTrue("应该包含指定租户", result.containsKey(tenantId));
        assertTrue("应该包含指定对象", result.get(tenantId).contains(objectApiName));
        verify(configCenterConfig).getBatchWrite2CrmTenantAndObjApiName();
    }

    /**
     * 测试 SKU/SPU 服务处理
     */
    @Test
    public void testSkuSpuServiceHandling() {
        // 准备测试数据
        SyncDataContextEvent event = createTestEvent("123", "TestObject", EventTypeEnum.ADD.getType());

        // Mock SKU/SPU 服务处理
        when(skuSpuService.handleSkuSpu2Crm(any(SyncDataContextEvent.class))).thenReturn(event);

        // 执行测试
        SyncDataContextEvent result = skuSpuService.handleSkuSpu2Crm(event);

        // 验证结果
        assertNotNull("处理结果不应为空", result);
        assertEquals("应该返回相同的事件对象", event, result);
        verify(skuSpuService).handleSkuSpu2Crm(event);
    }

    /**
     * 测试国际化字符串管理器
     */
    @Test
    public void testI18NStringManager() {
        // 准备测试数据
        String tenantId = "123";
        String key = "test.key";
        String defaultMessage = "Default Message";

        // Mock 国际化字符串处理
        when(i18NStringManager.get(anyString(), anyString(), eq(tenantId), eq(defaultMessage)))
            .thenReturn(defaultMessage);

        // 执行测试
        String result = i18NStringManager.get(key, "zh_CN", tenantId, defaultMessage);

        // 验证结果
        assertEquals("应该返回默认消息", defaultMessage, result);
        verify(i18NStringManager).get(key, "zh_CN", tenantId, defaultMessage);
    }

    /**
     * 测试同步策略详情快照服务
     */
    @Test
    public void testSyncPloyDetailSnapshotService() {
        // 准备测试数据
        String tenantId = "123";
        String snapshotId = "test_snapshot_id";

        // Mock 同步策略详情快照服务
        Result2<SyncPloyDetailSnapshotData2> mockResult = createMockSyncPloyDetailSnapshotResult();
        when(syncPloyDetailSnapshotService.getSyncPloyDetailSnapshotBySnapshotId(eq(tenantId), eq(snapshotId)))
            .thenReturn(mockResult);

        // 执行测试
        Result2<SyncPloyDetailSnapshotData2> result = syncPloyDetailSnapshotService
            .getSyncPloyDetailSnapshotBySnapshotId(tenantId, snapshotId);

        // 验证结果
        assertNotNull("快照结果不应为空", result);
        assertTrue("快照查询应该成功", result.isSuccess());
        assertNotNull("快照数据不应为空", result.getData());
        assertEquals("快照ID应该正确", snapshotId, result.getData().getId());
        verify(syncPloyDetailSnapshotService).getSyncPloyDetailSnapshotBySnapshotId(tenantId, snapshotId);
    }

    /**
     * 测试租户配置管理器
     */
    @Test
    public void testTenantConfigurationManager() {
        // 准备测试数据
        String tenantId = "123";

        // Mock 租户配置管理器
        when(tenantConfigurationManager.getNotUsePaasAddApiName(eq(tenantId))).thenReturn(new HashSet<>());

        // 执行测试
        Set<String> result = tenantConfigurationManager.getNotUsePaasAddApiName(tenantId);

        // 验证结果
        assertNotNull("配置结果不应为空", result);
        assertTrue("配置应该为空集合", result.isEmpty());
        verify(tenantConfigurationManager).getNotUsePaasAddApiName(tenantId);
    }

    /**
     * 测试外部服务工厂
     */
    @Test
    public void testOuterServiceFactory() {
        // 准备测试数据
        int tenantType = 1;

        // Mock 外部服务工厂
        when(outerServiceFactory.get(eq(tenantType))).thenReturn(outerService);

        // 执行测试
        OuterService result = outerServiceFactory.get(tenantType);

        // 验证结果
        assertNotNull("外部服务不应为空", result);
        assertEquals("应该返回Mock的外部服务", outerService, result);
        verify(outerServiceFactory).get(tenantType);
    }

    /**
     * 测试外部服务的主对象API名称获取
     */
    @Test
    public void testOuterServiceGetMasterObjectApiName() {
        // 准备测试数据
        String objectApiName = "TestObject";
        int tenantType = 1;
        String tenantId = "123";

        // Mock 外部服务
        when(outerService.getMasterObjectApiName(eq(objectApiName), eq(tenantType), eq(tenantId)))
            .thenReturn(com.fxiaoke.open.erpsyncdata.preprocess.result.Result2.newSuccess("TestObject"));

        // 执行测试
        com.fxiaoke.open.erpsyncdata.preprocess.result.Result2<String> result = outerService.getMasterObjectApiName(objectApiName, tenantType, tenantId);

        // 验证结果
        assertNotNull("结果不应为空", result);
        assertTrue("调用应该成功", result.isSuccess());
        assertEquals("应该返回正确的API名称", "TestObject", result.getData());
        verify(outerService).getMasterObjectApiName(objectApiName, tenantType, tenantId);
    }

    /**
     * 测试 ObjectDataService 删除操作
     */
    @Test
    public void testObjectDataServiceBulkDelete() {
        // 准备测试数据
        String objectApiName = "TestObject";
        HeaderObj headerObj = new HeaderObj(123, Constant.SYSTEM_USER_ID);

        // Mock 删除操作的返回结果
        com.fxiaoke.crmrestapi.common.result.Result<BulkDeleteResult> deleteResult = new  com.fxiaoke.crmrestapi.common.result.Result<>();
        deleteResult.setCode( com.fxiaoke.crmrestapi.common.result.Result.SUCCESS_CODE);
        deleteResult.setMessage("删除成功");

        when(objectDataService.bulkDelete(any(HeaderObj.class), eq(objectApiName), any(), eq(true)))
            .thenReturn(deleteResult);

        // 执行测试
        com.fxiaoke.crmrestapi.common.result.Result<BulkDeleteResult> result = objectDataService.bulkDelete(headerObj, objectApiName, null, true);

        // 验证结果
        assertNotNull("删除结果不应为空", result);
        assertTrue("删除应该成功", result.isSuccess());
        assertEquals("删除消息应该正确", "删除成功", result.getMessage());
        verify(objectDataService).bulkDelete(any(HeaderObj.class), eq(objectApiName), any(), eq(true));
    }

    /**
     * 测试事件类型枚举
     */
    @Test
    public void testEventTypeEnum() {
        // 验证事件类型枚举值
        assertEquals("ADD事件类型应该为1", 1, EventTypeEnum.ADD.getType());
        assertEquals("UPDATE事件类型应该为2", 2, EventTypeEnum.UPDATE.getType());
        assertEquals("DELETE_DIRECT事件类型应该为9", 9, EventTypeEnum.DELETE_DIRECT.getType());
        assertEquals("INVALID事件类型应该为3", 3, EventTypeEnum.INVALID.getType());
    }

    /**
     * 测试 API 错误响应处理
     */
    @Test
    public void testApiErrorResponse() {
        // 准备测试数据
        String objectApiName = "TestObject";
        HeaderObj headerObj = new HeaderObj(123,  Constant.SYSTEM_USER_ID);

        // Mock API错误
        com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> errorResult = new com.fxiaoke.crmrestapi.common.result.Result<>();
        errorResult.setCode(5000000);
        errorResult.setMessage("API调用失败：权限不足");

        when(metadataActionService.add(any(HeaderObj.class), eq(objectApiName), anyBoolean(), anyBoolean(), any(), any(), any(ActionAddArg.class)))
            .thenReturn(errorResult);

        // 执行测试
        ActionAddArg addArg = new ActionAddArg();
        com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> result = metadataActionService.add(headerObj, objectApiName, false, false, null, null, addArg);

        // 验证结果
        assertNotNull("错误结果不应为空", result);
        assertFalse("API调用应该失败", result.isSuccess());
        assertEquals("错误码应该正确", 5000000, result.getCode());
        assertTrue("错误信息应该包含权限不足", result.getMessage().contains("权限不足"));
        verify(metadataActionService).add(any(HeaderObj.class), eq(objectApiName), anyBoolean(), anyBoolean(), any(), any(), any(ActionAddArg.class));
    }

    /**
     * 测试 ObjectData 数据操作
     */
    @Test
    public void testObjectDataOperations() {
        // 创建 ObjectData 对象
        ObjectData objectData = new ObjectData();

        // 测试设置字段
        objectData.put("_id", "test_id_123");
        objectData.put("name", "Test Object");
        objectData.put("status", "active");

        // 验证字段设置
        assertEquals("ID应该正确设置", "test_id_123", objectData.get("_id"));
        assertEquals("名称应该正确设置", "Test Object", objectData.get("name"));
        assertEquals("状态应该正确设置", "active", objectData.get("status"));

        // 测试字段数量
        assertEquals("应该有3个字段", 3, objectData.size());
    }
}