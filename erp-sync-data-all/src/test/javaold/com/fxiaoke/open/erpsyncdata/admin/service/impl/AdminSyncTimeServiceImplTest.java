package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.arg.DeleteSyncDataAndSyncDataMappingArg;
import com.fxiaoke.open.erpsyncdata.admin.manager.ErpObjDataPushManager;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncDataMappingService;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncTimeService;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.thread.NamedThreadPoolExecutor;
import com.google.common.collect.Lists;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

import static java.lang.Thread.sleep;

/**
 * <AUTHOR>
 * @Date: 15:22 2021/7/5
 * @Desc:
 */
@Ignore
public class AdminSyncTimeServiceImplTest extends BaseTest {

    @Autowired
    private AdminSyncTimeService adminSyncTimeService;
    private static final int THREAD_COUNT = 100;
    private static final ThreadPoolExecutor DEFAULT_EXECUTOR = new NamedThreadPoolExecutor("erpCommPoolUtil", THREAD_COUNT, Integer.MAX_VALUE);

    @Autowired
    private ErpObjDataPushManager erpObjDataPushManager;
    @Autowired
    private AdminSyncDataMappingService adminSyncDataMappingService;

    @Test
    public void updateLastSyncTime() {
        Result<Void> voidResult = adminSyncTimeService.updateLastSyncTime("80775",
                "ORG_Organizations.BillHead_1", 1, 1618971378127L);
        System.out.println("");
    }

    @Test
    public void test() {
        DeleteSyncDataAndSyncDataMappingArg arg=new DeleteSyncDataAndSyncDataMappingArg();
        arg.setTenantId("132412");
        arg.setDestObjApiName("AccountObj");
        adminSyncDataMappingService.deleteSyncDataAndSyncDataMapping(arg);
        List<StandardData> listss=Lists.newArrayList();
        for(int i=0;i<10;i++){
            StandardData data=new StandardData();
            ObjectData objectData=new ObjectData();
            objectData.putId(i+"dfsasfda");
            objectData.put("id",objectData.getId());
            data.setMasterFieldVal(objectData);
            listss.add(data);
        }
        listss.get(0).getMasterFieldVal().put("id","test20210702001");
        erpObjDataPushManager.pushErpData2Table("82370","AccountObj","AccountObj_1f5hqpedr",
                "1",listss,null,"d10e6153355a4124a3b949f172a6cab8");
        System.out.println("");
    }

    public Function getFunction() {
        Function function = new Function() {
            @Override
            public Object apply(Object obj) {
                try {
                    sleep(10000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                return obj.toString();
            }
        };
        return function;
    }
    public static <R, T> void executeAndWait(ThreadPoolExecutor executor, Collection<T> srcDatas,Collection<R> results, Function function , int timeoutSecond) {
        List<Future> futures = new ArrayList<>();
        for (T srcData : srcDatas) {
            Future future = executor.submit(() -> function.apply(srcData));
            futures.add(future);
        }

        for (int i = 0; i < futures.size(); i++) {
            Future future = futures.get(i);
            try {
                R result = (R)future.get(timeoutSecond, TimeUnit.SECONDS);
                results.add(result);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return;
    }
}