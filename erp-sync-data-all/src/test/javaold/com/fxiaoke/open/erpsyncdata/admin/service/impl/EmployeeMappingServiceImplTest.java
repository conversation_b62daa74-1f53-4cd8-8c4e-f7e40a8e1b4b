package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.manager.AdminSyncDataManager;
import com.fxiaoke.open.erpsyncdata.admin.manager.AdminSyncDataMappingManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldDataMappingEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.BaseArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.PageArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryEmployeeMappingListArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.EmployeeMappingResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.QueryResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.SpecialFieldMappingResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 11:16 2020/9/18
 * @Desc:
 */
@Ignore
@Slf4j
@Data
public class EmployeeMappingServiceImplTest extends BaseTest {

    @Autowired
    private EmployeeMappingServiceImpl employeeMappingService;
    @Autowired
    private AdminSyncDataMappingManager adminSyncDataMappingManager;
    @Autowired
    private AdminSyncDataManager adminSyncDataManager;


    @Test
    public void queryAllUnSyncEmployee() {

        PageArg pageArg = new PageArg();
        pageArg.setPageNum(2);
        pageArg.setPageSize(5);
        Result<QueryResult<List<EmployeeMappingResult>>> queryResultResult = employeeMappingService.queryAllUnSyncEmployee("81138", -10000,"", pageArg);
        log.info("queryAllUnSyncEmployee={}", queryResultResult);
    }

    @Test
    public void queryAllErpEmployee() {
        Result<List<EmployeeMappingResult>> result = employeeMappingService.queryAllErpEmployee("81138", -10000,"");
        log.info("result={}", result);
    }

    @Test
    public void syncErpEmployee() {
        PageArg pageArg = new PageArg();
        pageArg.setPageNum(2);
        pageArg.setPageSize(2);
        Result<QueryResult<List<EmployeeMappingResult>>> queryResultResult = employeeMappingService.queryAllUnSyncEmployee("79675", -10000,"", pageArg);
        log.info("queryAllUnSyncEmployee={}", queryResultResult);
    }

    @Test
    public void queryEmployeeMapping() {
        QueryEmployeeMappingListArg pageArg = new QueryEmployeeMappingListArg();
        pageArg.setPageNum(1);
        pageArg.setPageSize(20);
        pageArg.setIsBind(false);
        Result<QueryResult<List<EmployeeMappingResult>>> queryResultResult = employeeMappingService.queryEmployeeMapping("81138", -10000,"620483680381042688", pageArg,null);
        log.info("queryResultResult={}", queryResultResult);
    }

    @Test
    public void queryEmpOrUserMapping() {
        QueryEmployeeMappingListArg pageArg = new QueryEmployeeMappingListArg();
        pageArg.setPageNum(1);
        pageArg.setPageSize(20);
        pageArg.setIsBind(false);
        pageArg.setFieldType(ErpFieldTypeEnum.employee);
        Result<QueryResult<List<SpecialFieldMappingResult.EmpResult>>> queryEmpOrUserMapping = employeeMappingService.queryEmpOrUserMapping("84801",
                -10000,
                "780777150699143168",
                pageArg);
        pageArg.setIsBind(true);
        queryEmpOrUserMapping = employeeMappingService.queryEmpOrUserMapping("84801",
                -10000,
                "780777150699143168",
                pageArg);
        System.out.println(queryEmpOrUserMapping);
    }

    @Test
    public void updateEmpMapping() {
        ErpFieldDataMappingEntity mappingEntity = ErpFieldDataMappingEntity.builder()
                .id("")
                .tenantId("84801")
                .channel(ErpChannelEnum.ERP_K3CLOUD)
                .dataCenterId("780777150699143168")
                .dataType(ErpFieldTypeEnum.employee)
                .fsDataId("1005")
                .fsDataName(null)
                .erpDataId("00408")
                .erpDataName("吴贝贝")
                .build();
        Result<String> queryEmpOrUserMapping = employeeMappingService.updateEmpOrUserMapping("84801",
                -10000,
                mappingEntity,null);

        System.out.println(queryEmpOrUserMapping);
    }

    @Test
    public void updateUserMapping() {
        ErpFieldDataMappingEntity mappingEntity2 = ErpFieldDataMappingEntity.builder()
                .id("6423e104a360a76518a9384a")
                .tenantId("84801")
                .channel(ErpChannelEnum.ERP_K3CLOUD)
                .dataCenterId("780777150699143168")
                .dataType(ErpFieldTypeEnum.user)
                .fsDataId("1005")
                .fsDataName(null)
                .erpDataId(null)
                .erpDataName("ces2")
                .build();
//        String json = "{\"dataType\":\"user\",\"channel\":\"ERP_K3CLOUD\",\"fsDataId\":1008,\"fsDataName\":\"陈宗鑫\",\"erpDataName\":\"ces2\",\"currentDcId\":\"696453487420604416\"}";
//        mappingEntity2 = JSONObject.parseObject(json,ErpFieldDataMappingEntity.class);
        mappingEntity2.setDataCenterId("780777150699143168");
        Result<String> queryEmpOrUserMapping2 = employeeMappingService.updateEmpOrUserMapping("84801",
                -10000,
                mappingEntity2,null);
        System.out.println(queryEmpOrUserMapping2);
    }

    @Test
    public void deleteUserMapping() {
        BaseArg baseArg = new BaseArg();
        baseArg.setId("64229d14a360a723788c0e67");

        Result<Integer> queryEmpOrUserMapping2 = employeeMappingService.batchDeleteEmpOrUserMapping("84801",
                -10000,
                "780777150699143168",
                ErpFieldTypeEnum.user,
                Lists.newArrayList(""),null);
        System.out.println(queryEmpOrUserMapping2);
    }

    @Test
    public void migrateUserData() {
        Result<Void> result = employeeMappingService.migrateUserData("84801", ErpChannelEnum.ERP_K3CLOUD);
        System.out.println(result);
    }

    @Test
    public void migrateUserData4AllTenant() {
        Result<Void> result = employeeMappingService.migrateUserData4AllTenant(ErpChannelEnum.ERP_K3CLOUD);
        System.out.println(result);
    }

    @Data
    public static class DDsSyncData {
        private String destObjectApiName;
        private String destTenantId;
        private String destDataId;
    }
}