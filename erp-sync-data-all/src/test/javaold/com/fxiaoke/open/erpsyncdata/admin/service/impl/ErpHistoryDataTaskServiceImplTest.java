package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpHistoryDataTaskService;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.AplManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.QueryFuncArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.QueryFunctionResult;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.*;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.JdyConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpHistoryDataTaskResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.QueryResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.otherrestapi.function.data.FunctionServiceFindData;
import com.fxiaoke.otherrestapi.function.data.HeaderObj;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 17:20 2021/8/16
 * @Desc:
 */
@Ignore
public class ErpHistoryDataTaskServiceImplTest extends BaseTest {
    @Autowired
    private ErpHistoryDataTaskService erpHistoryDataTaskService;
    @Autowired
    private AplManager aplManager;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;

    @Test
    public void createErpHistoryDataTask() {
        CreateErpHistoryDataTaskArg arg=new CreateErpHistoryDataTaskArg();
        ErpHistoryDataTaskResult task=new ErpHistoryDataTaskResult();
        task.setCreator(1001);
        task.setTaskType(1);
        task.setTaskName("taskName");
        task.setStartTime(System.currentTimeMillis()-1000*3600*24*30L);
        task.setEndTime(System.currentTimeMillis());
        task.setObjApiName("STK_Inventory.BillHead");
        task.setRemark("remark");
        arg.setTask(task);
        arg.setIsCheck(true);

        CreateErpHistoryDataTaskArg createErpHistoryDataTaskArg=JSONObject.parseObject("{\n" +
                "    \"isCheck\": false,\n" +
                "    \"task\": {\n" +
                "        \"taskName\": \"测试任务0001log\",\n" +
                "        \"dataCenterId\": \"643f7322b54ea80001767d86\",\n" +
                "        \"objApiName\": \"BD_MATERIAL.BillHead\",\n" +
                "        \"taskType\": 1,\n" +
                "        \"remark\": \"测试任务0001logxxxxxx\",\n" +
                "        \"timePeriods\": [\n" +
                "            {\n" +
                "                \"startTime\": 1709481600000,\n" +
                "                \"endTime\": 1709827140000\n" +
                "            }\n" +
                "        ],\n" +
                "        \"executeTime\": 1709869591000,\n" +
                "        \"integrationResults\": [\n" +
                "            {\n" +
                "                \"id\": \"61b4c18e0b25401bb55d94078fabf699\",\n" +
                "                \"integrationStreamName\": \"物料 ERP往CRM\"\n" +
                "            }\n" +
                "        ]\n" +
                "    },\n" +
                "    \"currentDcId\": \"653623d5b4660c00014c129a\"\n" +
                "}",CreateErpHistoryDataTaskArg.class);

        Result<String> erpHistoryDataTask = erpHistoryDataTaskService.createErpHistoryDataTask("88521","643f7322b54ea80001767d86", 1000, createErpHistoryDataTaskArg,null);
        System.out.println("");
    }
    @Test
    public void editTask() {

        CreateErpHistoryDataTaskArg arg= JSONObject.parseObject("{\n" +
                "    \"isCheck\": false,\n" +
                "    \"task\": {\n" +
                "        \"id\": \"65dd5efd4886d20001b15a76\",\n" +
                "        \"lastTaskId\": \"65dd5efd4886d20001b15a76\",\n" +
                "        \"taskName\": \"测试编:17：00\",\n" +
                "        \"dataCenterId\": \"643f7322b54ea80001767d86\",\n" +
                "        \"objApiName\": \"BD_MATERIAL.BillHead\",\n" +
                "        \"taskType\": 1,\n" +
                "        \"remark\": \"测试编:17：00测试编:17：00\",\n" +
                "        \"timePeriods\": [\n" +
                "            {\n" +
                "                \"startTime\": 1708272000000,\n" +
                "                \"endTime\": 1708444740000\n" +
                "            }\n" +
                "        ],\n" +
                "        \"executeTime\": 1709024614000,\n" +
                "        \"integrationResults\": [\n" +
                "            {\n" +
                "                \"id\": \"61b4c18e0b25401bb55d94078fabf699\",\n" +
                "                \"integrationStreamName\": \"物料 ERP往CRM\"\n" +
                "            }\n" +
                "        ]\n" +
                "    },\n" +
                "    \"currentDcId\": \"643f7322b54ea80001767d86\"\n" +
                "}",CreateErpHistoryDataTaskArg.class);

        Result<String> erpHistoryDataTask = erpHistoryDataTaskService.editErpHistoryDataTask("88521","643f7322b54ea80001767d86", 1122, arg,null);
        System.out.println("");
    }

    @Test
    public void queryErpHistoryDataTask() {

        HeaderObj headerObj = new HeaderObj(Integer.valueOf(tenantId), 1000);
        QueryFuncArg functionServiceFindArg=new QueryFuncArg();
        functionServiceFindArg.setFunctionName(CommonConstant.JDY_SYSTEM_FUNC);
        Result<QueryFunctionResult> functionResultResult = aplManager.queryRegularFunction(headerObj, functionServiceFindArg);
        if(functionResultResult.isSuccess()&& ObjectUtils.isNotEmpty(functionResultResult.getData().getResult())){
            QueryFunctionResult.ListFunctionResult result = functionResultResult.getData().getResult();
            //判断是不是已经被其他连接器使用了
            List<ErpConnectInfoEntity> listDcByTenantId = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("88521")).getListDcByTenantId("88521", ErpChannelEnum.ERP_JDY);
            List<String> aplClassName= Lists.newArrayList();
            for (ErpConnectInfoEntity erpConnectInfoEntity : listDcByTenantId) {
                if(StringUtils.isNotEmpty(erpConnectInfoEntity.getConnectParams())){
                    JdyConnectParam connectParam= JSONObject.parseObject(erpConnectInfoEntity.getConnectParams(),JdyConnectParam.class);
                    if(StringUtils.isNotEmpty(connectParam.getAplClassApiName())){
                        aplClassName.add(connectParam.getAplClassApiName());
                    }
                }
            }
            if(result.getFunction().size()>0){
                String aplName = result.getFunction().stream().map(FunctionServiceFindData::getApiName).filter(u -> !aplClassName.contains(u)).findFirst().get();

            }
        }



        QueryErpHistoryDataTasksArg arg=new QueryErpHistoryDataTasksArg();
//        arg.setTaskName("测试编:17：00");
//        List<QueryErpHistoryDataTasksArg.DataCenterObjApiNames> list= Lists.newArrayList();
//        QueryErpHistoryDataTasksArg.DataCenterObjApiNames dataCenterObjApiNames=new QueryErpHistoryDataTasksArg.DataCenterObjApiNames();
////        dataCenterObjApiNames.setErpSplitObjApiNames(Lists.newArrayList("BD_MATERIAL.BillHead"));
//        dataCenterObjApiNames.setDataCenterId("655487ff772ab90001e80626");
//        list.add(dataCenterObjApiNames);
//        arg.setDataCenterObjApiNames(list);
        arg.setStatus(Lists.newArrayList(7));
        Result<QueryResult<List<ErpHistoryDataTaskResult>>> result = erpHistoryDataTaskService.queryErpHistoryDataTask("88458", 1000, arg,null);
        System.out.println("");
    }

    @Test
    public void deleteErpHistoryDataTask() {
    }

    @Test
    public void getErpHistoryDataTask() {
        BaseArg arg=new BaseArg();
        arg.setId("701671337122955264");
        Result<ErpHistoryDataTaskResult> erpHistoryDataTask = erpHistoryDataTaskService.getErpHistoryDataTask("81961", 1122, arg,null);
        System.out.println("");
    }

    @Test
    public void stopErpHistoryDataTask() {
        BaseTaskArg arg=new BaseTaskArg();
        arg.setId("701411002377928704");
        Result<String> result = erpHistoryDataTaskService.stopErpHistoryDataTask("81138", 1122, arg);
        System.out.println("");
    }

    @Test
    public void restartErpHistoryDataTask() {
        ErpHistoryDataRestartTasksArg erpHistoryDataRestartTasksArg=new ErpHistoryDataRestartTasksArg();
        erpHistoryDataRestartTasksArg.setId("65fd5fbffbf82000016fc31e");
        erpHistoryDataRestartTasksArg.setExecuteTime(1711104995000L);
        Result<String> result = erpHistoryDataTaskService.reStartErpHistoryDataTask("89029", 1000, erpHistoryDataRestartTasksArg);
        System.out.println("");
    }

}