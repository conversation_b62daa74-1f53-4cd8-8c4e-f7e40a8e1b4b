package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjCustomFunctionService;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.BaseArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjCustomFunctionResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 14:43 2020/11/23
 * @Desc:
 */
@Ignore
public class ErpObjCustomFunctionServiceImplTest extends BaseTest {
    @Autowired
    private ErpObjCustomFunctionService erpObjCustomFunctionService;
    @Autowired
    private IdGenerator idGenerator;
    @Test
    public void queryErpObjGroovy() {
        List<String> listResult = erpObjCustomFunctionService.queryAllFunctionApiNameByObjectApiName("81138", "", Lists.newArrayList());
        System.out.println(listResult);
    }

    @Test
    public void updateErpObjGroovy() {
        ErpObjCustomFunctionResult result=new ErpObjCustomFunctionResult();
        result.setObjApiName("BD_Customer");
        result.setUrl(ErpObjInterfaceUrlEnum.create);
        result.setFuncApiName("func_83lsf__c");
        Result<String> stringResult = erpObjCustomFunctionService.update("81138", 1001, result,null);
        System.out.println(stringResult);
    }
    @Test
    public void deleteErpObjGroovy() {
        BaseArg deleteArg=new BaseArg();
        deleteArg.setId("628332631981424640");
        Result<String> stringResult = erpObjCustomFunctionService.delete("79675", 1000, "dcId", deleteArg);
        System.out.println(stringResult);
    }
}