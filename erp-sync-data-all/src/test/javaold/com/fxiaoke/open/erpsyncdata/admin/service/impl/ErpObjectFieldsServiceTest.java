package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.constant.ExcelTypeEnum;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.BuildExcelFile;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ImportExcelFile;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjectFieldsService;
import com.fxiaoke.open.erpsyncdata.admin.service.FileService;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectFieldDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpObjExtendDto;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.List;

@Ignore
public class ErpObjectFieldsServiceTest extends BaseTest {
    @Resource
    private ErpObjectFieldsService erpObjectFieldsService;
    @Resource
    private FileService fileService;
    @Autowired
    private ErpObjectFieldDao erpObjectFieldDao;
    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;

    @Test
    public void buildObjectFieldTemplate() {
        String tenantId="88521";
        List<ErpObjectRelationshipEntity> bd_material = erpObjectRelationshipDao.setTenantId("88521").findMasterByRealObjectApiName(tenantId, "643f7322b54ea80001767d86", "BD_MATERIAL");

        List<ErpObjectRelationshipEntity> erpObjectRelationshipEntities = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).ListBySplit(tenantId, "643f7322b54ea80001767d86", Lists.newArrayList("BD_MATERIAL.BillHead"));


        Result<BuildExcelFile.Result> test2 = fileService.buildObjectFieldTemplate("82814",
                "699933926483394560",
                "wqe","zh-CN");
        String downloadPath="https://crm.ceshi112.com/FSC/EM/File/DownloadByPath?Path=%s&name=新增字段导入模板.xlsx";
        System.out.println(String.format(downloadPath,test2.getData().getTnFilePath()));
    }

    @Test
    public void batchImportErpObjectFields() {
        try {
            File file1=new File("D:\\ZxcvbnData\\import.xlsx");
            InputStream inputStream=new FileInputStream(file1);

            MockMultipartFile multipartFile = new MockMultipartFile("GetByPath (20).xlsx",inputStream);

            ImportExcelFile.FieldImportArg arg = new ImportExcelFile.FieldImportArg();
            arg.setTenantId("88521");
            arg.setUserId(1000);
            arg.setDataCenterId("64d34e5a5dba9300010dd999");
            arg.setErpRealObjectApiName("erp_object_apiname_import_data_field_excel_data");
            arg.setFile(multipartFile);
            arg.setExcelType(ExcelTypeEnum.OBJ_FIELD_DATA);
            Result<ImportExcelFile.FieldImportResult> result = erpObjectFieldsService.batchImportErpObjectFields(arg,null);
            System.out.println(result);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Test
    public void test() {
        ErpObjExtendDto objExtendDto = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("84801"))
                .findObjExtendDtoBySplit("84801", "STK_TransferDirect.TransferDirectEntry");
        if (objExtendDto == null) {
            return;
        }
    }

    @Test
    public void initFieldExtendData() {
        Result<Void> result = erpObjectFieldsService.initFieldExtendData("84801", "STK_TransferDirect");
        System.out.println(result);
    }

    @Test
    public void initFieldExtendData2() {
         Result<Void> result = erpObjectFieldsService.initFieldExtendData(Lists.newArrayList("82777"));
        System.out.println(result);
    }

    @Test
    public void test2() {
        List<String> allTenantId = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("84801"))
                .findAllTenantId();
        allTenantId.removeAll(Lists.newArrayList("0","01"));
        List<ErpObjectRelationshipEntity> relationshipEntityList = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("84801"))
                .findAllByTenantId("84801");
        System.out.println(relationshipEntityList);
    }

}
