package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.facishare.converter.EIEAConverter;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.service.OAConnectorService;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result3;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result4;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Ignore
public class OAConnectorServiceImplTest extends BaseTest {
    @Autowired
    private OAConnectorService oaConnectorService;
    @Autowired
    private EIEAConverter eieaConverter;

    @Test
    public void checkAndInitFeiShuConnector() {
        String ei = eieaConverter.enterpriseAccountToId("fszdbd2575")+"";
        Result3<Void> result3 = oaConnectorService.checkAndInitFeiShuConnector(ei,"");
        System.out.println(result3);
    }

    @Test
    public void checkAndInitQywxConnector() {
        Result4<Void> result3 = oaConnectorService.checkAndInitQywxConnector("81243","");
        System.out.println(result3);
    }
}
