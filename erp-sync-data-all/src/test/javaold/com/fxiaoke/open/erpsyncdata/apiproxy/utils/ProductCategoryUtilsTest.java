package com.fxiaoke.open.erpsyncdata.apiproxy.utils;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryBusinessInfoArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryBusinessInfoResult;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ErpProductCategory;
import com.fxiaoke.open.erpsyncdata.preprocess.model.K3CloudConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.junit.Ignore;
import org.junit.Test;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022-10-26
 */
@Ignore
public class ProductCategoryUtilsTest {
    protected static final String tenantId = "84801";

    @Test
    public void getErpCate() {
        K3CloudConnectParam k3CloudConnectParam = K3CloudConnectParam.newUserParam("\n" +
                        "http://**************:8569/k3cloud",
                "63298acc25bb2a","纷享","Aa123456@");
        K3CloudApiClient apiClient = K3CloudApiClient.newInstance(tenantId, k3CloudConnectParam, "");
        Result<QueryBusinessInfoResult> sal_materialgroup = apiClient.queryBusinessInfo(new QueryBusinessInfoArg("SAL_MATERIALGROUP"));
        List<ErpProductCategory> erpProductCategories = ProductCategoryUtils.getErpProductCategories(apiClient);
        System.out.println(erpProductCategories);
    }
}