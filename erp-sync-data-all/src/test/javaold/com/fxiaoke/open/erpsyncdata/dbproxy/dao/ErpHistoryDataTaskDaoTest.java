package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.fxiaoke.open.erpsyncdata.BaseDbTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpHistoryDataTaskEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpHistoryDataTaskStatusEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/5/18
 */
@Ignore
@Slf4j
public class ErpHistoryDataTaskDaoTest extends BaseDbTest {

    @Autowired
    private ErpHistoryDataTaskDao erpHistoryDataTaskDao;


    @Test
    public void testUpdate() {
        ErpHistoryDataTaskEntity entity = new ErpHistoryDataTaskEntity();
        entity.setId("1");
        entity.setOffset(1L);
        int i = erpHistoryDataTaskDao.updateById(entity);
        System.out.println(i);
    }

    @Test
    public void  testTask(){
        erpHistoryDataTaskDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("88521")).listByStatus("88521", Lists.newArrayList(ErpHistoryDataTaskStatusEnum.STATUS_START.getStatus()));

    }


    @Test
    public void testCount(){
        int count = erpHistoryDataTaskDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("88521")).
                viewAllCountByDcIdAndActualObjStatus("88521", Lists.newArrayList("643f7322b54ea80001767d86"), Lists.newArrayList("BD_MATERIAL"), null, Lists.newArrayList(6));
        log.info("count");
        List<ErpHistoryDataTaskEntity> historyDataTaskEntities = erpHistoryDataTaskDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("88521")).
                viewAllListByDcIdAndActualObjStatus("88521", Lists.newArrayList("643f7322b54ea80001767d86"), Lists.newArrayList("BD_MATERIAL"), null, Lists.newArrayList(6), 100, 0,null);

        log.info("historyDataTaskEntities");

    }




}