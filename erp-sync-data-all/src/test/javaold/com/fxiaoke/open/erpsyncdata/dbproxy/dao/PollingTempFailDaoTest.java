package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.fxiaoke.open.erpsyncdata.BaseDbTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.PollingTempFailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.PollingTempFailEntity;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/19
 */
@Ignore
public class PollingTempFailDaoTest extends BaseDbTest {
    @Autowired
    private PollingTempFailDao pollingTempFailDao;

    @Test
    public void name() {
        final PollingTempFailEntity entity = pollingTempFailDao.getAndRetry("65128752a34bb10001ffeadf");
        System.out.println(entity);
    }
}