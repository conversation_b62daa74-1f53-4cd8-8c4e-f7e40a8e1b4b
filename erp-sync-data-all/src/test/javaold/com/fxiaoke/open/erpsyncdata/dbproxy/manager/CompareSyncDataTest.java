package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.CompareResultDoc;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.factory.CompareSyncDataMongoDao;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.Field;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 测试syncdata dao
 *
 * <AUTHOR> (^_−)☆
 * @date 2022/5/10
 */
@Ignore
@Slf4j
public class CompareSyncDataTest extends BaseTest {
    @Autowired
    private CompareSyncDataMongoDao compareSyncDataMongoDao;



  @Test
    public void testSyncData(){
      String tenantId="85521";
      List<CompareResultDoc> compareResultEntities=Lists.newArrayList();
      CompareResultDoc compareResultEntity=new CompareResultDoc();
      compareResultEntity.setTenantId("85521");
      compareResultEntity.setSourceDataId("11111");
      compareResultEntity.setSourceObjApiName("AccountObj");
      compareResultEntity.setDestObjectApiName("Data");
      Map<String,Object> inputParamsMap=Maps.newHashMap();
      //SyncDataContextEvent
      SyncDataContextEvent syncDataContextEvent=new SyncDataContextEvent();
    syncDataContextEvent.setSyncDataId("********");
    syncDataContextEvent.setTenantId("85521");

    inputParamsMap.put("syncDataContextEvent",syncDataContextEvent);
      compareResultEntity.setInputParamsMap(inputParamsMap);
      compareResultEntity.setOutputParamsMap(inputParamsMap);
      compareResultEntity.setSyncLogId("J.E.1.8.0.0.0");
      compareResultEntities.add(compareResultEntity);
       compareSyncDataMongoDao.batchInsert(tenantId, compareResultEntities);

  }
  @Test
  public void testSyncData2(){
    String tenantId="855211";
    List<CompareResultDoc> compareResultEntities=Lists.newArrayList();
    CompareResultDoc compareResultEntity=new CompareResultDoc();
    compareResultEntity.setTenantId("855211");
    compareResultEntity.setSourceDataId("11111");
    compareResultEntity.setSourceObjApiName("AccountObj");
    compareResultEntity.setDestObjectApiName("Data");
    Map<String,Object> inputParamsMap=Maps.newHashMap();
    //SyncDataContextEvent
    SyncDataContextEvent syncDataContextEvent=new SyncDataContextEvent();
    syncDataContextEvent.setSyncDataId("********");
    syncDataContextEvent.setTenantId("85521");

    inputParamsMap.put("syncDataContextEvent",syncDataContextEvent);
    compareResultEntity.setInputParamsMap(inputParamsMap);
    compareResultEntity.setOutputParamsMap(inputParamsMap);
    compareResultEntity.setSyncLogId("J.E.1.9.0.0.0");
    compareResultEntities.add(compareResultEntity);
    compareSyncDataMongoDao.batchInsert(tenantId, compareResultEntities);

  }

//判断两个企业的数据是否一样
@Test
public void compareResult(){
    String tenantId="85521";
  List<CompareResultDoc> compareResultDocs = compareSyncDataMongoDao.listByTenantIdAndTypeAndStatusAndUpdateTime(tenantId, 0, 100);
  String tenantIdOther="855211";
  List<CompareResultDoc> compareResultDocs2 = compareSyncDataMongoDao.listByTenantIdAndTypeAndStatusAndUpdateTime(tenantId, 0, 100);
}

  @Test
  public void compareResultField() throws IllegalAccessException, ClassNotFoundException {
    String data="{\n" +
            "        \"createTime\":*************,\n" +
            "        \"destObjectApiName\":\"Data\",\n" +
            "        \"expireTime\":*************,\n" +
            "        \"hasCompare\":false,\n" +
            "        \"inputParamsMap\":{\n" +
            "            \"syncDataContextEvent\":{\n" +
            "                \"checkSyncDataMapping\":false,\n" +
            "                \"tenantId\":\"85521\"\n" +
            "            }\n" +
            "        },\n" +
            "        \"outputParamsMap\":{\n" +
            "            \"syncDataContextEvent\":{\n" +
            "                \"completeDataProcess\":false,\n" +
            "                \"completeDataWrite\":false,\n" +
            "                \"debugRecordIfDetailCauseMaterSync\":false,\n" +
            "                \"detailData\":{\n" +
            "\n" +
            "                },\n" +
            "                \"detailObjectDatasMap\":{\n" +
            "\n" +
            "                },\n" +
            "                \"detailWriteResults\":[\n" +
            "\n" +
            "                ],\n" +
            "                \"doProcess\":false,\n" +
            "                \"doWrite\":false,\n" +
            "                \"errCode\":0,\n" +
            "                \"errMsg\":\"success\",\n" +
            "                \"finish\":false,\n" +
            "                \"queryCrmObject2DestNode\":false,\n" +
            "                \"reverseWrite2Crm\":false,\n" +
            "                \"stop\":false,\n" +
            "                \"success\":true,\n" +
            "                \"syncDataId\":\"********\",\n" +
            "                \"syncployDetailId\":\"********\",\n" +
            "                \"tenantId\":\"8552111\"\n" +
            "            }\n" +
            "        },\n" +
            "        \"sourceDataId\":\"11111\",\n" +
            "        \"sourceObjApiName\":\"AccountObj\",\n" +
            "        \"syncLogId\":\"J.E.1.8.0.0.0\",\n" +
            "        \"tenantId\":\"85521\",\n" +
            "        \"theSame\":false,\n" +
            "        \"updateTime\":*************\n" +
            "    }";
    String data2="{\"createTime\":*************,\"destObjectApiName\":\"Data\",\"expireTime\":*************,\"hasCompare\":false,\"inputParamsMap\":{\"syncDataContextEvent\":{\"checkSyncDataMapping\":false,\"completeDataProcess\":false,\"completeDataWrite\":false,\"debugRecordIfDetailCauseMaterSync\":false,\"detailData\":{},\"detailObjectDatasMap\":{},\"detailWriteResults\":[],\"doProcess\":false,\"doWrite\":false,\"errCode\":0,\"errMsg\":\"success\",\"finish\":false,\"queryCrmObject2DestNode\":false,\"reverseWrite2Crm\":false,\"stop\":false,\"success\":true,\"syncDataId\":\"********\",\"tenantId\":\"85521\"}},\"outputParamsMap\":{\"syncDataContextEvent\":{\"checkSyncDataMapping\":false,\"completeDataProcess\":false,\"completeDataWrite\":false,\"debugRecordIfDetailCauseMaterSync\":false,\"detailData\":{},\"detailObjectDatasMap\":{},\"detailWriteResults\":[],\"doProcess\":false,\"doWrite\":false,\"errCode\":0,\"errMsg\":\"success\",\"finish\":false,\"queryCrmObject2DestNode\":false,\"reverseWrite2Crm\":false,\"stop\":false,\"success\":true,\"syncDataId\":\"********\",\"tenantId\":\"85521\"}},\"sourceDataId\":\"11111\",\"sourceObjApiName\":\"AccountObj\",\"syncLogId\":\"J.E.1.8.0.0.0\",\"tenantId\":\"85521\",\"theSame\":false,\"updateTime\":*************}";
    CompareResultDoc sourceResultDoc = JSONObject.parseObject(data, CompareResultDoc.class);
    CompareResultDoc compareResultDoc = JSONObject.parseObject(data2, CompareResultDoc.class);
    SyncDataContextEvent syncCompare=new SyncDataContextEvent();
    SyncDataContextEvent sourceInputContext=new SyncDataContextEvent();
    sourceInputContext.setSourceTenantId("8788");
    LinkedHashMap<String, ObjectData> map=Maps.newLinkedHashMap();
    map.put("obj1",new ObjectData());
    sourceInputContext.setDestDetailSyncDataIdAndDestDataMap(map);
    SyncDataContextEvent compareInputContext=new SyncDataContextEvent();
    compareInputContext.setDestEventType(1);
    SyncDataContextEvent sourceOutPutContext=new SyncDataContextEvent();
    ObjectData objectData=new ObjectData();
    objectData.put("obj","obj");
    sourceOutPutContext.setSourceData(objectData);
    SyncDataContextEvent compareOutPutContext=new SyncDataContextEvent();
    compareOutPutContext.setDestData(objectData);
    Map<String, List<String>> compareDiffMap=Maps.newHashMap();
    for (Field field : SyncDataContextEvent.class.getDeclaredFields()) {
        field.setAccessible(true);
      Map<String, List<String>> stringListMap = compareObjByType(compareDiffMap,field, sourceInputContext, sourceOutPutContext, compareInputContext, compareOutPutContext);
      compareDiffMap.putAll(stringListMap);
    }
    if(compareDiffMap.size()>0){
      System.out.println(JSONObject.toJSON(compareDiffMap));
    }
  }


  private Map<String,List<String>> compareObjByType(Map<String, List<String>> compareDiffMap,Field fieldObj,Object sourceInputObj,Object sourceOutObj,Object compareInputObj,Object compareOutObj) throws ClassNotFoundException, IllegalAccessException {
    if(!fieldObj.getType().isPrimitive()&&!wrapperBaseType(fieldObj.getType())&&false){//先跳过
      for (Field field : fieldObj.getClass().getDeclaredFields()) {
        try {
          field.setAccessible(true);
          Class<?> fieldType = field.getType();
          //
          Object sourceInputValue=field.get(sourceInputObj);
          Object compareInputValue=field.get(compareInputObj);
          Object sourceOutputValue=field.get(sourceOutObj);
          Object compareOutputValue=field.get(compareOutObj);
          if (!fieldType.isPrimitive()&&!wrapperBaseType(fieldType)) {
             compareObjByType(compareDiffMap,field, sourceInputValue, sourceOutputValue, compareInputValue, compareOutputValue);
            return compareDiffMap;
          }
          //比对两个值是否都为空或者都不为空。
          compareValue(field.getName(),sourceInputValue,compareInputValue,compareDiffMap,"input");
          compareValue(field.getName(),sourceOutputValue,compareOutputValue,compareDiffMap,"output");
        } catch (Exception e) {
          System.out.println(field);
        }
      }
    }else{
      fieldObj.setAccessible(true);
      Object sourceInputValue=fieldObj.get(sourceInputObj);
      Object compareInputValue=fieldObj.get(compareInputObj);
      Object sourceOutputValue=fieldObj.get(sourceOutObj);
      Object compareOutputValue=fieldObj.get(compareOutObj);
      //比对两个值是否都为空或者都不为空。
      compareValue(fieldObj.getName(),sourceInputValue,compareInputValue,compareDiffMap,"input");
      compareValue(fieldObj.getName(),sourceOutputValue,compareOutputValue,compareDiffMap,"output");
    }

    return compareDiffMap;
  }
  private boolean wrapperBaseType(Class<?> fieldType){

      return fieldType == Integer.class ||
              fieldType == Long.class ||
              fieldType == Float.class ||
              fieldType == Double.class ||
              fieldType == Boolean.class ||
              fieldType == Character.class ||
              fieldType == Byte.class ||
              fieldType == Short.class||
              fieldType == ObjectData.class||
              fieldType == String.class;

  }

  private Boolean compareValue(String fieldName,Object sourceValue,Object destValue,Map<String,List<String>> maps,String type){
     if(ObjectUtils.isNotEmpty(sourceValue)&&ObjectUtils.isNotEmpty(destValue)){
       return true;
     }else if(ObjectUtils.isEmpty(sourceValue)&&ObjectUtils.isEmpty(destValue)){
       return true;
     }
    maps.computeIfAbsent(type,key ->Lists.newArrayList()).add(fieldName);
    System.out.println("compareValue :sourceValue:"+sourceValue+" destValue"+destValue);
     return false;
  }

  //Field field = SyncDataContextEvent.class.getDeclaredField("fieldName"); // 替换为实际的字段名
  //Class<?> fieldType = field.getType();
  //
  //if (fieldType.isPrimitive()) {
  //    // 处理基本类型字段
  //} else {
  //    // 处理非基本类型字段
  //    // 可以根据具体的类型进行相应的处理，如反序列化等
  //    if (fieldType.equals(SyncDataData.class)) {
  //        // 处理 SyncDataData 类型字段
  //        // 可以通过反射获取字段的值，并进行相应的操作
  //        Object fieldValue = field.get(instance); // instance 是 SyncDataContextEvent 实例
  //        SyncDataData syncData = (SyncDataData) fieldValue;
  //        // 进行 SyncDataData 类型的操作
  //    }
  //    // 其他类型的处理...
  //}
}