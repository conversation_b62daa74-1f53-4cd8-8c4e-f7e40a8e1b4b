package com.fxiaoke.open.erpsyncdata.custom.controller.tenant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;

import com.fxiaoke.open.erpsyncdata.custom.utils.tenant.wanma.WanMaFileUtils;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 万马附件上传，自定义函数调用
 */
@Slf4j
@Api(tags = "万马相关接口")
@RestController("WanMaController")
//82370 112测试企业     724968 万马正式
@RequestMapping(path = {"inner/erp/syncdata/customfunction/724968"})
public class WanMaController {
    @Autowired
    private I18NStringManager i18NStringManager;


    @Autowired
    private WanMaFileUtils wanMaFileUtils;

    @ApiOperation(value = "通用方法")
    @RequestMapping(value = "/dowork", method = RequestMethod.POST)
    public Result<String> dowork(@RequestHeader(value = "x-fs-ei") Integer tenantId,
                                 @RequestHeader Map<String, String> headerMap,
                                 @RequestBody Map<String, String> bodyMap) {
        log.info("trace WanMaController dowork tenantId:{}, headerMap:{}, bodymap: {} ", tenantId, headerMap, bodyMap);
        String methodName = headerMap.get("method-name");
        if (StringUtils.isBlank(methodName)) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL.getErrCode(), I18NStringEnum.s3666);
        }
        Result<String> result = null;
        try {
            String param = bodyMap.get("param");
            if ("uploadFile".equals(methodName)) {
                result = wanMaFileUtils.uploadFile(param);
            } else {
                return Result.newError(ResultCodeEnum.PARAM_ILLEGAL.getErrCode(), I18NStringEnum.s3662);
            }
            log.info("result: {}", result);
            return result;
        } catch (Exception e) {
            log.error("e:{}", e);
            return Result.newError("fail", e.getMessage());
        }
    }
}
