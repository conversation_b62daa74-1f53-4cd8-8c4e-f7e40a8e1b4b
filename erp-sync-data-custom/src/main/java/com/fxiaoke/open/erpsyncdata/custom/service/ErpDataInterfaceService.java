package com.fxiaoke.open.erpsyncdata.custom.service;

import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 10:31 2021/2/4
 * @Desc:
 */
public interface ErpDataInterfaceService {
    Result<List<StandardData>> getAllErpObjectData(TimeFilterArg timeFilterArg, ErpConnectInfoEntity connectInfo);
    Result<List<StandardData>> getLimitErpObjectData(TimeFilterArg timeFilterArg, ErpConnectInfoEntity connectInfo);
}
