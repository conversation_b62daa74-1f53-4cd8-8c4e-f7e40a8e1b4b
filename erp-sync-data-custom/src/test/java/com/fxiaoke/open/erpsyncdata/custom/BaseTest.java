package com.fxiaoke.open.erpsyncdata.custom;

import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @Date: 14:17 2021/2/3
 * @Desc:
 */
@Ignore
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath*:spring/custom-applicationContext.xml"})
public abstract class BaseTest {
    @BeforeClass
    public static void before() {
        System.setProperty("process.profile", "fstest");
        System.setProperty("process.name", "fs-erp-sync-data");
    }
}
