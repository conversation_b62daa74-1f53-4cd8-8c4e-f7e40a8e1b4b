package com.fxiaoke.open.erpsyncdata.file.service.impl;


import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import com.facishare.stone.sdk.response.StoneFileUploadResponse;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.crmrestapi.arg.v3.QueryListByIdsArg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.ObjectDataQueryListByIdsResult;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.K3FileManager;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.common.thread.NamedThreadPoolExecutor;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.UpLoadFileArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.CrmFileModel;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.UpLoadFileService;
import com.fxiaoke.open.erpsyncdata.writer.manager.DoWrite2CrmManager;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.CookieJar;
import okhttp3.Dispatcher;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.InputStream;
import java.io.Serializable;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

@Slf4j
@Service("upLoadFileService")
public class UpLoadFileServiceImpl implements UpLoadFileService {
    @Autowired
    private ObjectDataServiceV3 objectDataServiceV3;
    @Autowired
    private ObjectDataService objectDataService;
    @Autowired
    private K3FileManager k3FileManager;
    @Autowired
    private I18NStringManager i18NStringManager;

    private static OkHttpClient client;
    private static ThreadPoolExecutor executor = new NamedThreadPoolExecutor("UpLoadFileService", 20, 20);


    private static final TimedCache<String, Map<String, String>> sfTokenCache = CacheUtil.newTimedCache(1000 * 60 * 10);

    static {
        TrustManager[] trustManagers = new TrustManager[]{new X509TrustManager() {
            @Override
            public void checkClientTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {
            }
            @Override
            public void checkServerTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {
            }
            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return new X509Certificate[]{};
            }
        }};
        try {
            SSLContext sslContext = SSLContext.getInstance("SSL");
            sslContext.init(null,trustManagers,new SecureRandom());
            Dispatcher dispatcher = new Dispatcher();
            dispatcher.setMaxRequests(64);
            dispatcher.setMaxRequestsPerHost(5);
            ConnectionPool connectionPool = new ConnectionPool(5, 5, TimeUnit.SECONDS);
            client = new OkHttpClient.Builder().connectTimeout(120, TimeUnit.SECONDS)
                    .hostnameVerifier((hostname, session) -> true)
                    .readTimeout(120, TimeUnit.SECONDS)
                    .writeTimeout(120, TimeUnit.SECONDS)
                    .retryOnConnectionFailure(true)
                    .dispatcher(dispatcher)
                    .connectionPool(connectionPool)
                    .cookieJar(CookieJar.NO_COOKIES)
                    .sslSocketFactory(sslContext.getSocketFactory(), (X509TrustManager) trustManagers[0])
                    .build();
        } catch (Exception e) {
            log.error("build client Exception={}",e);
            client = new OkHttpClient.Builder().build();
        }

    }

    @Override
    public Result<String> doFileService(String tenantId, UpLoadFileArg arg) {
        if (arg == null || StringUtils.isBlank(arg.getBaseUrl())|| StringUtils.isBlank(arg.getTokenUrl())|| StringUtils.isBlank(arg.getLinkedEntityIdUrl())
                || StringUtils.isBlank(arg.getContentDocumentIdUrl())|| StringUtils.isBlank(arg.getLinkedEntityIdCrmFieldKey())
                || StringUtils.isBlank(arg.getCrmObjApiName())|| StringUtils.isBlank(arg.getFileFieldKey())|| StringUtils.isBlank(arg.getRemarkFieldKey())
                || CollectionUtils.isEmpty(arg.getCrmDataIdList())) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR.getErrCode(),I18NStringEnum.s3665);
        }
        Future<Result<String>> submit = executor.submit(() -> downloadAndUploadFile(tenantId, arg));
        Result<String> result;
        try {
            result = submit.get(9, TimeUnit.SECONDS);
        } catch (TimeoutException e){
            result = Result.newSuccessByI18N(i18NStringManager.getByEi(I18NStringEnum.s1023,tenantId),null,null);
        } catch(Exception e) {
            log.warn("CommonPoolUtil execute exception={} ", e);
            result = Result.newError(ResultCodeEnum.SYSTEM_ERROR, i18NStringManager.getByEi(I18NStringEnum.s1022,tenantId) + e.getMessage());
        }
        return result;
    }

    public Result<String> downloadAndUploadFile(String tenantId, UpLoadFileArg arg) {
        Result<List<ObjectData>> objectDataListResult = getCrmObjDataList(tenantId, arg);
        if (!objectDataListResult.isSuccess()) {
            Result.copy(objectDataListResult);
        }
        List<ObjectData> objectDataList = objectDataListResult.getData();
        if(CollectionUtils.isEmpty(objectDataList)){
            return Result.newError(i18NStringManager.getByEi(I18NStringEnum.s1021,tenantId));
        }
        String tokenUrl = arg.getBaseUrl() + arg.getTokenUrl();
        for (ObjectData objectData : objectDataList) {
            StringBuffer remark = new StringBuffer();
            ObjectData newObjectData = new ObjectData();
            String linkedEntityId = objectData.getString(arg.getLinkedEntityIdCrmFieldKey());
            try {
                if(StringUtils.isBlank(linkedEntityId)){
                    remark.append(i18NStringManager.getByEi2(I18NStringEnum.s1020.getI18nKey(),
                            tenantId,
                            String.format(I18NStringEnum.s1020.getI18nValue(), arg.getLinkedEntityIdCrmFieldKey()),
                            Lists.newArrayList(arg.getLinkedEntityIdCrmFieldKey())));
                }else{
                    String linkedEntityIdUrl = String.format(arg.getBaseUrl() + arg.getLinkedEntityIdUrl(), linkedEntityId);
                    Result<SfFileDetail> fileDetailResult = this.getFileDetail(tenantId, linkedEntityIdUrl, tokenUrl);
                    SfFileDetail fileDetail = fileDetailResult.getData();
                    if (!fileDetailResult.isSuccess() || fileDetail == null || CollectionUtils.isEmpty(fileDetail.getRecords())) {
                        if (!fileDetailResult.isSuccess()) {
                            remark.append(i18NStringManager.getByEi(I18NStringEnum.s7,tenantId)+ "：").append(fileDetailResult.getErrMsg());
                        } else {
                            remark.append(i18NStringManager.getByEi(I18NStringEnum.s1019,tenantId));
                        }
                        if (fileDetail != null) {
                            remark.append(",").append(fileDetail.getMessage());
                        }
                        log.error("getFileDetail result={}", fileDetail);//如果有报错，就不上传了
                    } else {
                        List<SfFileMsg> sfFileMsgList = Lists.newArrayList();
                        for (ContentDocumentData contentDocumentData : fileDetail.getRecords()) {
                            String fileExtension = contentDocumentData.getContentDocument().getFileExtension();
                            String contentDocumentId = contentDocumentData.getContentDocumentId();
                            String contentDocumentIdUrl = String.format(arg.getBaseUrl() + arg.getContentDocumentIdUrl(), contentDocumentId);
                            Result<SfFileVersionDataDetail> fileVersionDataDetailResult = this.getFileVersionDataDetail(tenantId, contentDocumentIdUrl, tokenUrl);
                            SfFileVersionDataDetail fileVersionDataDetail = fileVersionDataDetailResult.getData();
                            if (fileVersionDataDetail == null || CollectionUtils.isEmpty(fileVersionDataDetail.getRecords())) {
                                remark.append(i18NStringManager.getByEi(I18NStringEnum.s1018,tenantId));
                                if (fileVersionDataDetail != null) {
                                    remark.append(",").append(fileVersionDataDetail.getMessage());
                                }
                                log.error("getFileVersionDataDetail result={}", fileDetail);
                                sfFileMsgList.clear();//如果有一个报错，就不上传了
                                break;
                            }
                            for (SfFileVersionData sfFileVersionData : fileVersionDataDetail.getRecords()) {
                                SfFileMsg sfFileMsg = new SfFileMsg();
                                sfFileMsg.setTitle(sfFileVersionData.getTitle());
                                sfFileMsg.setFileExtension(fileExtension);
                                sfFileMsg.setVersionDataUrl(sfFileVersionData.getVersionData());
                                sfFileMsg.setAttributes(sfFileVersionData.getAttributes());
                                sfFileMsgList.add(sfFileMsg);
                            }
                        }
                        if (CollectionUtils.isNotEmpty(sfFileMsgList)) {
                            List<CrmFileModel> fileModelList = Lists.newArrayList();
                            for (SfFileMsg sfFileMsg : sfFileMsgList) {
                                String getFileSizeUrl = arg.getBaseUrl() + sfFileMsg.getAttributes().getUrl();
                                Result<SfFileSizeData> fileSizeResult = getFileSize(tenantId, getFileSizeUrl, tokenUrl);
                                SfFileSizeData fileSize = fileSizeResult.getData();
                                if (fileSize != null) {
                                    String downLoadUrl = arg.getBaseUrl() + sfFileMsg.getVersionDataUrl();
                                    Result<SfFileStream> fileStreamResult = getFileStream(tenantId, downLoadUrl, tokenUrl);
                                    SfFileStream fileStream = fileStreamResult.getData();
                                    if (fileStream != null) {
                                        Result<StoneFileUploadResponse> result = k3FileManager.crmUploadFile(fileStream.getFileInputStream(), fileSize.getTitle(), tenantId, fileSize.getContentSize(), fileSize.getFileExtension());
                                        if (result != null && result.getData() != null) {
                                            StoneFileUploadResponse fileUpload = result.getData();
                                            String filename = fileSize.Title;
                                            if (!filename.endsWith("." + fileUpload.getExtensionName())) {
                                                filename = String.format("%s.%s", filename, fileUpload.getExtensionName());
                                            }
                                            CrmFileModel crmFileModel = CrmFileModel.builder().filename(filename).ext(fileUpload.getExtensionName()).path(String.format("%s.%s", fileUpload.getPath(), fileUpload.getExtensionName())).createTime(System.currentTimeMillis()).size(fileUpload.getSize()).build();
                                            fileModelList.add(crmFileModel);
                                        } else {//如果一个出错，全部清除
                                            remark.append(i18NStringManager.getByEi(I18NStringEnum.s1017,tenantId)+"：");
                                            if(result!=null&&result.getErrMsg()!=null){
                                                remark.append(":").append(fileSizeResult.getErrMsg());
                                            }
                                            fileModelList.clear();
                                            break;
                                        }
                                    } else {//如果一个出错，全部清除
                                        remark.append(i18NStringManager.getByEi(I18NStringEnum.s1016,tenantId));
                                        if(fileStreamResult!=null&&fileStreamResult.getErrMsg()!=null){
                                            remark.append(":").append(fileSizeResult.getErrMsg());
                                        }
                                        fileModelList.clear();
                                        break;
                                    }
                                } else {//如果一个出错，全部清除
                                    remark.append(i18NStringManager.getByEi(I18NStringEnum.s1015,tenantId));
                                    if(fileSizeResult!=null&&fileSizeResult.getErrMsg()!=null){
                                        remark.append(":").append(fileSizeResult.getErrMsg());
                                    }
                                    fileModelList.clear();
                                    break;
                                }
                            }
                            if (CollectionUtils.isNotEmpty(fileModelList)) {
                                newObjectData.put(arg.getFileFieldKey(), fileModelList);
                                remark.append(i18NStringManager.getByEi(I18NStringEnum.s6,tenantId));
                            }
                        }
                    }
                }
                if (remark.length() > 0) {
                    newObjectData.put(arg.getRemarkFieldKey(), remark.toString());
                }
                return this.updateCrmObj(tenantId, objectData.getId(), objectData.getApiName(), newObjectData,arg.getRemarkFieldKey());
            } catch (Exception e) {
                log.error("download and upload file error objectData={} Exception={}", objectData, e);
            }
        }
        return Result.newSuccess();
    }

    public Result<String> updateCrmObj(String tenantId, String id, String apiName, ObjectData objectData, String remarkFieldKey) {
        if (objectData == null || CollectionUtils.isEmpty(objectData.keySet())) {
            return Result.newSuccess();
        }
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
        headerObj.put("x-fs-peer-name", DoWrite2CrmManager.fsPeerName);
        com.fxiaoke.crmrestapi.common.result.Result<ObjectData> objectDataResult = objectDataService.updateObjectData(headerObj, apiName, id, null, objectData);
        if (!objectDataResult.isSuccess()) {
            log.info("updateCrmObj failed result={}", objectDataResult);
            ObjectData newObjectData=new ObjectData();
            newObjectData.put(remarkFieldKey,i18NStringManager.getByEi(I18NStringEnum.s1014,tenantId)+objectDataResult.getMessage());
            objectDataService.updateObjectData(headerObj, apiName, id, null, newObjectData);
            return Result.newError(objectDataResult.getMessage());
        }
        return Result.newSuccess();
    }


    public Result<Map<String, String>> getSfHeaders(String tenantId, String tokenUrl, Boolean removeCache) {
        String tokenKey = tenantId + tokenUrl;
        if (removeCache != null && removeCache) {
            sfTokenCache.remove(tokenKey);
        }
        Map<String, String> oldToken = sfTokenCache.get(tokenKey, false);
        if (oldToken != null) {
            return Result.newSuccess(oldToken);
        }
        Map<String, String> token = getToken(tenantId, tokenUrl);
        if (token == null || token.get("token_type") == null || token.get("access_token") == null) {
            log.error("getSfHeaders failed result={}", token);
            return Result.newError(i18NStringManager.getByEi(I18NStringEnum.s1013,tenantId));
        }
        Map<String, String> sfHeaders = Maps.newHashMap();
        sfHeaders.put("Authorization", token.get("token_type") + " " + token.get("access_token"));
        sfTokenCache.put(tokenKey, sfHeaders);
        return Result.newSuccess(sfHeaders);
    }

    public Result<List<ObjectData>> getCrmObjDataList(String tenantId, UpLoadFileArg arg) {
        com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj = new com.fxiaoke.crmrestapi.common.data.HeaderObj(Integer.valueOf(tenantId), CrmConstants.SYSTEM_USER);
        QueryListByIdsArg queryListByIdsArg = new QueryListByIdsArg();
        queryListByIdsArg.setDescribeApiName(arg.getCrmObjApiName());
        queryListByIdsArg.setDataIdList(arg.getCrmDataIdList());
        queryListByIdsArg.setIncludeInvalid(true);
        List<String> fields = Lists.newArrayList("_id", "object_describe_api_name");
        fields.add(arg.getLinkedEntityIdCrmFieldKey());
        queryListByIdsArg.setSelectFields(fields);
        com.fxiaoke.crmrestapi.common.result.Result<ObjectDataQueryListByIdsResult> resultResult = objectDataServiceV3.queryListByIds(headerObj, queryListByIdsArg);
        if (!resultResult.isSuccess()) {
            log.warn("queryListByIds failed,resultResult={}", resultResult);
            return Result.newError(i18NStringManager.getByEi(I18NStringEnum.s1012,tenantId) + resultResult.getMessage());
        }
        return Result.newSuccess(resultResult.getData().getDataList());
    }

    public Result<SfFileDetail> getFileDetail(String tenantId, String fileDetailUrl, String tokenUrl) {
        Result<Map<String, String>> sfHeaders = this.getSfHeaders(tenantId, tokenUrl, false);
        if (!sfHeaders.isSuccess()) {
            return Result.copy(sfHeaders);
        }
        SfFileDetail detail = doGet(tenantId, fileDetailUrl, sfHeaders.getData(), "", new TypeReference<SfFileDetail>() {
        });
        if (detail != null && "INVALID_SESSION_ID".equals(detail.getErrorCode())) {//重试一次
            sfHeaders = this.getSfHeaders(tenantId, tokenUrl, true);
            if (!sfHeaders.isSuccess()) {
                return Result.copy(sfHeaders);
            }
            detail = doGet(tenantId, fileDetailUrl, sfHeaders.getData(), "", new TypeReference<SfFileDetail>() {
            });
        }
        return Result.newSuccess(detail);
    }

    public Result<SfFileSizeData> getFileSize(String tenantId, String fileSizeUrl, String tokenUrl) {
        Result<Map<String, String>> sfHeaders = this.getSfHeaders(tenantId, tokenUrl, false);
        if (!sfHeaders.isSuccess()) {
            return Result.copy(sfHeaders);
        }
        SfFileSizeData detail = doGet(tenantId, fileSizeUrl, sfHeaders.getData(), "", new TypeReference<SfFileSizeData>() {
        });
        if (detail != null && "INVALID_SESSION_ID".equals(detail.getErrorCode())) {//重试一次
            sfHeaders = this.getSfHeaders(tenantId, tokenUrl, false);
            if (!sfHeaders.isSuccess()) {
                return Result.copy(sfHeaders);
            }
            detail = doGet(tenantId, fileSizeUrl, sfHeaders.getData(), "", new TypeReference<SfFileSizeData>() {
            });
        }
        return Result.newSuccess(detail);
    }

    public Result<SfFileVersionDataDetail> getFileVersionDataDetail(String tenantId, String fileDetailUrl, String tokenUrl) {
        Result<Map<String, String>> sfHeaders = this.getSfHeaders(tenantId, tokenUrl, false);
        if (!sfHeaders.isSuccess()) {
            return Result.copy(sfHeaders);
        }
        SfFileVersionDataDetail detail = doGet(tenantId, fileDetailUrl, sfHeaders.getData(), "", new TypeReference<SfFileVersionDataDetail>() {
        });
        if (detail != null && "INVALID_SESSION_ID".equals(detail.getErrorCode())) {//重试一次
            sfHeaders = this.getSfHeaders(tenantId, tokenUrl, false);
            if (!sfHeaders.isSuccess()) {
                return Result.copy(sfHeaders);
            }
            detail = doGet(tenantId, fileDetailUrl, sfHeaders.getData(), "", new TypeReference<SfFileVersionDataDetail>() {
            });
        }
        return Result.newSuccess(detail);
    }


    public Map<String, String> getToken(String tenantId, String tokenUrl) {//token有效期是7200秒，只缓存10分钟
        return doPost(tenantId, tokenUrl, null, "", new TypeReference<Map<String, String>>() {
        });
    }

    public Result<SfFileStream> getFileStream(String tenantId, String url, String tokenUrl) {
        Result<Map<String, String>> sfHeaders = this.getSfHeaders(tenantId, tokenUrl, false);
        if (!sfHeaders.isSuccess()) {
            return Result.copy(sfHeaders);
        }
        Response response = null;
        Request.Builder builder = new Request.Builder().url(url).get();
        if (sfHeaders.getData() != null) {
            for (String key : sfHeaders.getData().keySet()) {
                builder.header(key, sfHeaders.getData().get(key));
            }
        }
        Request request = builder.build();
        try {
            response = client.newCall(request).execute();
            if (response.body() == null) {
                return null;
            }
            if ("application/octetstream".equals(response.body().contentType().toString())) {
                SfFileStream sfFileStream = new SfFileStream();
                sfFileStream.setFileInputStream(response.body().byteStream());
                return Result.newSuccess(sfFileStream);
            } else {
                List<BaseResult> errors = JacksonUtil.fromJson(response.body().string(), new TypeReference<List<BaseResult>>() {
                });
                SfFileStream sfFileStream = new SfFileStream();
                sfFileStream.setErrorCode(errors.get(0).getErrorCode());
                sfFileStream.setMessage(errors.get(0).getMessage());
                return Result.newSuccess(sfFileStream);
            }

        } catch (Exception e) {
            log.error("execute Exception response={} e={}", response, e);
            return Result.newSystemError(I18NStringEnum.s1011);
        }

    }

    public <T> T doPost(String tenantId, String url, Map<String, String> headers, String content, TypeReference<T> typeReference) {
        return doRequest(RequestMethod.POST, url, headers, content, typeReference);
    }

    public <T> T doGet(String tenantId, String url, Map<String, String> headers, String content, TypeReference<T> typeReference) {
        return doRequest(RequestMethod.GET, url, headers, content, typeReference);
    }

    public <T> T doRequest(RequestMethod requestMethod, String url, Map<String, String> headers, String content, TypeReference<T> typeReference) {
        T result = null;
        String resultStr = null;
        Response response = null;
        MediaType mediaType;
        Request.Builder builder;
        if (RequestMethod.POST.equals(requestMethod)) {
            mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, content);
            builder = new Request.Builder().url(url).post(body);
        } else if (RequestMethod.GET.equals(requestMethod)) {
            builder = new Request.Builder().url(url).get();
        } else {
            mediaType = MediaType.parse("text/plain");
            RequestBody body = RequestBody.create(mediaType, content);
            builder = new Request.Builder().url(url).method(requestMethod.name(), body);
        }
        if (headers != null) {
            for (String key : headers.keySet()) {
                builder.header(key, headers.get(key));
            }
        }
        Request request = builder.build();
        try {
            response = client.newCall(request).execute();
            if (response.body() == null) {
                return result;
            }
            resultStr = response.body().string();
            if (resultStr != null && resultStr.startsWith("[")) {//默认数组的都是异常信息的，如果有正常的是数组的不能调用
                List<BaseResult> errors = JacksonUtil.fromJson(resultStr, new TypeReference<List<BaseResult>>() {
                });
                if (CollectionUtils.isNotEmpty(errors)) {//取第一个报错信息，返回的类都要集成BaseResult才可以
                    result = JacksonUtil.fromJson(JacksonUtil.toJson(errors.get(0)), typeReference);
                }
            } else {
                result = JacksonUtil.fromJson(resultStr, typeReference);
            }
        } catch (Exception e) {
            log.error("execute Exception url={} header={} content={} resultStr={} e={}", url, headers, content, resultStr, e);
            return result;
        }
        return result;
    }


    @Data
    public static class SfFileSizeData extends BaseResult {
        public Integer ContentSize;
        public String FileExtension;
        public String Title;
    }

    @Data
    public static class SfFileDetail extends BaseResult {
        public List<ContentDocumentData> records;
    }

    @Data
    public static class ContentDocumentData implements Serializable {
        public ContentDocument ContentDocument;
        public String ContentDocumentId;
    }

    @Data
    public static class ContentDocument implements Serializable {
        public String FileExtension;
        public String Title;
    }

    @Data
    public static class SfFileVersionDataDetail extends BaseResult {
        public List<SfFileVersionData> records;
    }

    @Data
    public static class SfFileVersionData implements Serializable {
        public Attributes attributes;
        public String VersionData;
        public String Title;
    }

    @Data
    public static class SfFileMsg implements Serializable {
        public Attributes attributes;
        public String FileExtension;
        public String Title;
        public String VersionDataUrl;
    }

    @Data
    public static class Attributes {
        public String type;
        public String url;
    }

    @Data
    public static class SfFileStream extends BaseResult {
        public InputStream fileInputStream;
    }

    @Data
    public static class BaseResult implements Serializable {
        public String message;
        public String errorCode;
    }
}