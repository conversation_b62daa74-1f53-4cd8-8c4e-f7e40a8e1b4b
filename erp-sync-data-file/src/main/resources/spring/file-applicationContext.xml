<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd">

    <import resource="file-common.xml"/>
    <import resource="file-fileserver.xml"/>
    <import resource="file-dubbo-provider.xml"/>
    <import resource="file-dubbo-consumer.xml"/>
    <!-- scan指定的类 -->
    <import resource="file-erp-apiproxy.xml"/>

    <import resource="classpath*:spring/common-spring.xml"/>
    <import resource="classpath*:spring/common-db-proxy.xml"/>
    <import resource="classpath:spring/fs-spring-dubbo-rest-plugin-provider.xml"/>

</beans>