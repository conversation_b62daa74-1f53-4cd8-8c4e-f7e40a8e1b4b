<configuration scan="false" scanPeriod="60 seconds" debug="false">
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} [%class:%line] %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="org.hibernate" level="WARN"/>
    <logger name="org.springframework" level="INFO"/>
    <logger name="com.github" level="ERROR"/>
    <logger name="org.apache" level="WARN"/>
    <logger name="Access" level="DEBUG"/>
    <logger name="org.redisson.connection" level="WARN"/>
    <logger name="com.fxiaoke.dispatcher.processor.Dispatcher" level="INFO"/>

    <root level="DEBUG">
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>