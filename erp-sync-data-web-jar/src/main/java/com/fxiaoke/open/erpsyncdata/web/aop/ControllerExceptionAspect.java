package com.fxiaoke.open.erpsyncdata.web.aop;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import cn.hutool.core.exceptions.ExceptionUtil;
import com.facishare.cep.plugin.exception.BizException;
import com.facishare.open.erp.connertor.exception.OverseasProxyException;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.async.DeferredResult;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/9/3
 */
@Slf4j
@Aspect
@Order(5)
@Component()
public class ControllerExceptionAspect {
    @Autowired
    private I18NStringManager i18NStringManager;

    @Around("execution(* com.fxiaoke.open.erpsyncdata.web.controller..*.*(..)) ")
    public Object transferException(ProceedingJoinPoint proceedingJoinPoint) {
        try {
            return proceedingJoinPoint.proceed();
        } catch (OverseasProxyException e) {
            MethodSignature methodSignature = (MethodSignature) proceedingJoinPoint.getSignature();
            log.warn("erp sync data Exception:method:{},target:{},args:{},ex:",
                    methodSignature.getMethod().getName(), proceedingJoinPoint.getTarget().getClass().getName(), proceedingJoinPoint.getArgs(), e);
            return buildErrorResult(methodSignature, String.valueOf(e.getErrorCode()), e.getErrorMsg());
        } catch (ErpSyncDataException e) {
            MethodSignature methodSignature = (MethodSignature) proceedingJoinPoint.getSignature();
            log.warn("erp sync data Exception:method:{},target:{},args:{},ex:",
                    methodSignature.getMethod().getName(), proceedingJoinPoint.getTarget().getClass().getName(), proceedingJoinPoint.getArgs(), e);
            return buildErrorResult(methodSignature, e.getErrCode(), e.getErrMsg());
        } catch (IllegalStateException e){
            //不合法请求
            MethodSignature methodSignature = (MethodSignature) proceedingJoinPoint.getSignature();
            log.warn("erp sync data Exception:method:{},target:{},args:{},ex:",
                    methodSignature.getMethod().getName(), proceedingJoinPoint.getTarget().getClass().getName(), proceedingJoinPoint.getArgs(), e);
            return buildErrorResult(methodSignature, ResultCodeEnum.PARAM_ILLEGAL.getErrCode(), ExceptionUtil.getMessage(e));
        } catch (Throwable e) {
            MethodSignature methodSignature = (MethodSignature) proceedingJoinPoint.getSignature();
            log.warn("Exception:method:{},target:{},args:{},ex:", methodSignature.getMethod().getName(), proceedingJoinPoint.getTarget().getClass().getName(), proceedingJoinPoint.getArgs(), e);
            throw new BizException(i18NStringManager.get2(I18NStringEnum.s3711, TraceUtil.getLocale(), null, TraceUtil.get()), -1,
                    Result.newSystemError(I18NStringEnum.s3705, e.getClass().getName()));
//            return Result.newErrorTrace(ResultCodeEnum.SYSTEM_ERROR,e.getClass().getName());
        }
    }

    private Object buildErrorResult(MethodSignature methodSignature, String code, String msg) {
        Class<?> returnType = methodSignature.getReturnType();
        Result<?> errorResult = Result.newError(code, msg);
        if (returnType != null && DeferredResult.class.isAssignableFrom(returnType)) {
            DeferredResult<Result<?>> resultDeferredResult = new DeferredResult<>();
            errorResult.setI18nKey(null);
            errorResult.setI18nExtra(null);
            resultDeferredResult.setResult(errorResult);
            return resultDeferredResult;
        }
        return errorResult;
    }
}
