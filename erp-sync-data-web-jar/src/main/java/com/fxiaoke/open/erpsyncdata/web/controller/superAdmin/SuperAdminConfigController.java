package com.fxiaoke.open.erpsyncdata.web.controller.superAdmin;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Entity;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheManager;
import com.alicp.jetcache.MultiLevelCache;
import com.fxiaoke.i18n.SupportLanguage;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.api.Localization;
import com.fxiaoke.i18n.util.LangIndex;
import com.fxiaoke.open.erpsyncdata.admin.arg.BatchChangeTenantInfoArg;
import com.fxiaoke.open.erpsyncdata.admin.constant.UserOperationTypeEnum;
import com.fxiaoke.open.erpsyncdata.admin.constant.UserOperatorModuleEnum;
import com.fxiaoke.open.erpsyncdata.admin.manager.analyse.AllConnectorInfoManager;
import com.fxiaoke.open.erpsyncdata.admin.manager.analyse.AllStreamInfoManager;
import com.fxiaoke.open.erpsyncdata.admin.model.amis.Amis;
import com.fxiaoke.open.erpsyncdata.admin.model.superadmin.AdminConfigInfo;
import com.fxiaoke.open.erpsyncdata.admin.model.superadmin.AdminTenantEnvOpInfo;
import com.fxiaoke.open.erpsyncdata.admin.service.superadmin.SuperAdminServiceImpl;
import com.fxiaoke.open.erpsyncdata.common.jetcache.redisson.RedissonCache;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum.FormatType;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpFieldExtendDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpTenantConfigurationDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpTenantConfigurationDaoAccess;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldExtendEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.UserOperatorLog;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.*;
import com.fxiaoke.open.erpsyncdata.i18n.I18nBase;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpSyncDataBackStageEnvironmentEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.Connector;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.AssertUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 配置项管理
 *
 * <AUTHOR> (^_−)☆
 * @date 2021/12/9
 */
@RestController
@Slf4j
@RequestMapping("erp/syncdata/superadmin/config")
@Validated
public class SuperAdminConfigController extends SuperAdminBaseController {
    @Autowired
    private ErpTenantConfigurationDao erpTenantConfigurationDao;
    @Autowired
    private ErpTenantConfigurationDaoAccess erpTenantConfigurationDaoAccess;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private ErpFieldExtendDao erpFieldExtendDao;
    @Autowired
    private ErpConnectInfoManager connectInfoManager;
    @Autowired
    private TenantEnvManager tenantEnvManager;
    @Autowired
    private CacheManager cacheManager;
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private PlusTenantConfigManager plusTenantConfigManager;
    @Autowired
    private ConfigCenterManager configCenterManager;
    @Autowired
    private AllConnectorInfoManager allConnectorInfoManager;
    @Autowired
    private AllStreamInfoManager allStreamInfoManager;
    @Autowired
    private SuperAdminServiceImpl superAdminService;

    /**
     * 列出
     *
     * @param arg
     * @return
     */
    @PostMapping("/listConfig")
    public Result<List<AdminConfigInfo>> listConfig(@RequestBody AdminConfigInfo arg) {
        if (StrUtil.isAllBlank(arg.getType(), arg.getTenantId())) {
            return Result.newError("类型和tenantId不能同时为空");   // ignoreI18n   实施和开发自用
        }
        List<AdminConfigInfo> erpTenantConfigurations = queryAdminConfigs(arg);
        return Result.newSuccess(erpTenantConfigurations);
    }

    @NotNull
    private List<AdminConfigInfo> queryAdminConfigs(AdminConfigInfo arg) {
        List<ErpTenantConfigurationEntity> entities = erpTenantConfigurationDaoAccess.queryBy(arg.getTenantId(), arg.getDataCenterId(), arg.getType(), 100);
        List<AdminConfigInfo> erpTenantConfigurations = BeanUtil.copyList(entities, AdminConfigInfo.class);
        if (!erpTenantConfigurations.isEmpty()) {
            TenantConfigurationTypeEnum type = EnumUtil.fromStringQuietly(TenantConfigurationTypeEnum.class, arg.getType());
            if (type != null) {
                erpTenantConfigurations.forEach(v -> {
                    v.setTypeEnum(type);
                    v.setFormatType(Opt.ofNullable(type.getFormatType()).map(t -> t.name()).get());
                    v.setDataType(Opt.ofNullable(type.getDataType()).map(t -> t.getTypeName()).get());
                });
            }
        }
        return erpTenantConfigurations;
    }

    @PostMapping("/schemaConfig")
    public Result<Amis.Schema> schemaConfig(@RequestBody AdminConfigInfo arg) {
        if (StrUtil.isAllBlank(arg.getType())) {
            return Result.newError("type is not allowed empty");
        }
        //读取配置列表
        List<AdminConfigInfo> adminConfigInfos = queryAdminConfigs(arg);
        Amis.Schema schema;
        if (adminConfigInfos.isEmpty()) {
            //空数据
            return Result.newSuccess(Amis.Schema.tpl("not found"));
        } else if (adminConfigInfos.size() == 1) {
            //单条配置
            AdminConfigInfo singleConfig = adminConfigInfos.get(0);
            TenantConfigurationTypeEnum typeEnum = singleConfig.getTypeEnum();
            //优先使用传参的类型
            FormatType formatType = EnumUtil.fromString(FormatType.class, arg.getFormatType(), FormatType.none);
            if (formatType == FormatType.none) {
                //使用代码的类型
                if (typeEnum != null && typeEnum.getFormatType() != null) {
                    formatType = typeEnum.getFormatType();
                }
            }
            if (formatType == FormatType.none) {
                schema = Amis.Schema.tpl(singleConfig.getConfiguration());
                return Result.newSuccess(schema);
            }
            switch (formatType) {
                case listObject:
                    Class<?> tClass = (Class<?>) typeEnum.getDataType();
                    List<?> objects;
                    Amis.Crud<?> crud;
                    if (tClass != null) {
                        String configuration = singleConfig.getConfiguration();
                        objects = JSON.parseArray(configuration, tClass);
                        crud = Amis.Crud.parse(tClass, objects);
                    } else {
                        List<Dict> data = JSON.parseArray(singleConfig.getConfiguration(), Dict.class);
                        crud = Amis.Crud.parseMap(data);
                    }
                    return Result.newSuccess(crud.toSchema());
                case tenantList:
                    //企业列表
                    List<String> eis = StrUtil.split(singleConfig.getConfiguration(), ";");
                    if (eis.contains("*")) {
                        schema = Amis.Schema.tpl("all");
                    } else if (eis.isEmpty()) {
                        schema = Amis.Schema.tpl("empty");
                    } else {
                        Map<String, String> requestParam = new HashMap<>();
                        requestParam.put("keywords", StrUtil.join(",", eis));
                        requestParam.put("perPage", "1000");
                        schema = superAdminService.tenantInfoQuery(requestParam).safeData().toSchema();
                    }
                    return Result.newSuccess(schema);
                case json:
                case object:
                case listValueMap:
                case longMap:
                    //json
                    schema = Amis.Schema.jsonEditorStatic(singleConfig.getConfiguration());
                    return Result.newSuccess(schema);
                default:
                    schema = Amis.Schema.tpl(singleConfig.getConfiguration());
                    return Result.newSuccess(schema);
            }
        } else {
            //多条配置
            Amis.Crud<?> crud = Amis.Crud.parse(AdminConfigInfo.class, adminConfigInfos);
            return Result.newSuccess(crud.toSchema());
        }
    }


    /**
     * 查配置
     *
     * @param arg
     * @return
     */
    @PostMapping("/getConfig")
    public Result<AdminConfigInfo> getConfig(@RequestBody AdminConfigInfo arg) {
        if (!StrUtil.isAllNotBlank(arg.getType())) {
            return Result.newError("类型不能为空");   // ignoreI18n   实施和开发自用
        }
        List<AdminConfigInfo> adminConfigInfos = queryAdminConfigs(arg);
        if (adminConfigInfos.isEmpty()) {
            return Result.newError("无法找到数据");   // ignoreI18n   实施和开发自用
        }
        if (adminConfigInfos.size() > 1) {
            return Result.newError("找到多条数据");   // ignoreI18n   实施和开发自用
        }
        return Result.newSuccess(adminConfigInfos.get(0));
    }

    /**
     * 更新企业配置项
     *
     * @param arg
     * @return
     */
    @PostMapping("/upsertConfig")
    public Result<Void> upsertConfig(@RequestBody AdminConfigInfo arg) {
        if (arg.getType() == null || arg.getTenantId() == null) {
            return Result.newError("企业id和类型不能为空");   // ignoreI18n   实施和开发自用
        }
        String tenantId = arg.getTenantId();
        String dcId = arg.getDataCenterId();
        String type = arg.getType();
        ErpTenantConfigurationEntity old = erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findOneNoCache(tenantId, dcId, null, type);
        long now = System.currentTimeMillis();
        TenantConfigurationTypeEnum configType = TenantConfigurationTypeEnum.valueOf(arg.getType());
        plusTenantConfigManager.invalidConvertConfig(arg.getTenantId(), arg.getDataCenterId(), configType);
        if (old != null) {
            //修改
            tenantConfigurationManager.updateConfig(arg.getTenantId(), old.getId(), arg.getConfiguration(), configType);
        } else {
            ErpTenantConfigurationEntity newEntity = new ErpTenantConfigurationEntity();
            BeanUtils.copyProperties(arg, newEntity);
            newEntity.setId(com.fxiaoke.api.IdGenerator.get());
            newEntity.setCreateTime(now);
            newEntity.setUpdateTime(now);
            int insert = tenantConfigurationManager.insert(tenantId, newEntity);
            log.info("insert,{},{}", newEntity, insert);
        }
        return Result.newSuccess();
    }


    /**
     * 删除企业配置项
     * 实际上是将tenantId修改为del_{tenantId}
     */
    @PostMapping("/deleteConfig")
    public Result<Void> deleteConfig(@RequestBody AdminConfigInfo arg) {
        if (arg.getType() == null || arg.getTenantId() == null) {
            return Result.newError("企业id和类型不能为空");   // ignoreI18n   实施和开发自用
        }
        String tenantId = arg.getTenantId();
        String dcId = arg.getDataCenterId();
        String type = arg.getType();
        ErpTenantConfigurationEntity old = erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findOneNoCache(tenantId, dcId, null, type);
        if (old == null) {
            return Result.newError("无法找到数据");   // ignoreI18n   实施和开发自用
        }
        int i = erpTenantConfigurationDao.deleteById(tenantId, old.getId());
        return Result.newSuccess();
    }

    /**
     * 查找企业某个对象的字段扩展，id类型
     *
     * @param tenantId
     * @param objApiName
     * @return
     */
    @GetMapping("/findIdFieldExtend/{tenantId}/{dataCenterId}/{objApiName}")
    public Result<List<Entity>> findIdFieldExtend(@PathVariable String tenantId, @PathVariable String dataCenterId, @PathVariable String objApiName) {
        List<ErpFieldExtendEntity> entities = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .queryIdFieldByObjApiName(tenantId, dataCenterId, objApiName);
        List<Entity> result = entities.stream().map(Entity::parse).collect(Collectors.toList());
        return Result.newSuccess(result);
    }

    /**
     * 更新字段扩展
     *
     * @param tenantId
     * @param arg
     * @return
     */
    @PostMapping("/updateFieldExtend/{tenantId}/{dataCenterId}")
    public Result<Integer> updateFieldExtend(@PathVariable String tenantId, @PathVariable String dataCenterId, @RequestBody Dict arg) {
        ErpFieldExtendEntity entity = new ErpFieldExtendEntity();
        entity.setTenantId(tenantId);
        entity.setObjApiName(AssertUtil.notEmpty(arg.getStr("objApiName"), tenantId));
        entity.setFieldApiName(AssertUtil.notEmpty(arg.getStr("fieldApiName"), tenantId));
        entity.setSaveExtend(arg.getStr("saveExtend"));
        entity.setViewExtend(arg.getStr("viewExtend"));
        entity.setFieldDefineType(ErpFieldTypeEnum.valueOf(arg.getStr("fieldDefineType")));
        entity.setUpdateTime(System.currentTimeMillis());
        entity.setDataCenterId(dataCenterId);
        log.info("SuperAdminConfigController.updateFieldExtend,entity={}", entity);
        int i = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateByApiName(entity);
        return Result.newSuccess(i);
    }

    /**
     * 修改企业环境
     *
     * @param tenantId
     * @param env
     * @return
     */
    @PostMapping("/changeTenantInfo")
    public Result<Void> changeTenantInfo(@RequestParam String tenantId, @RequestParam ErpSyncDataBackStageEnvironmentEnum env) {
        tenantEnvManager.changeTenantEnv(tenantId, env);
        return Result.newSuccess();
    }

    /**
     * 修改企业环境
     *
     * @param arg
     * @return
     */
    @PostMapping("/batchChangeTenantInfo")
    public Result<Void> batchChangeTenantInfo(@RequestBody BatchChangeTenantInfoArg arg) {
        List<String> tenantIds = Splitter.on(",").splitToList(arg.getIds());
        ErpSyncDataBackStageEnvironmentEnum env = arg.getEnv();
        if (tenantIds.isEmpty() || env == null) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        //校验是否允许变更环境
        ImmutableMap<String, ImmutableSet<String>> groupWhiteMap = tenantConfigurationManager.getGroupWhiteMap(TenantConfigurationTypeEnum.FORBID_INTO_ENV);
        Set<String> notAllowChangeKeys = groupWhiteMap.get(env.getEnvironment());
        notAllowChangeKeys = notAllowChangeKeys == null ? ImmutableSet.of() : notAllowChangeKeys;
        Set<String> notAllowChangeTenantIdSet = new HashSet<>();
        List<String> allowTenantIds = Lists.newArrayList();
        for (String tenantId : tenantIds) {
            if (!notAllowChangeKeys.isEmpty()) {
                if (notAllowChangeKeys.contains(tenantId)) {
                    notAllowChangeTenantIdSet.add(tenantId);
                    continue;
                }
                List<Connector> connectorList = connectInfoManager.listConnectorsByTenantId(tenantId);
                Set<String> connectorKeys = connectorList.stream().map(v -> v.getKey()).collect(Collectors.toSet());
                if (CollUtil.containsAny(notAllowChangeKeys, connectorKeys)) {
                    notAllowChangeTenantIdSet.add(tenantId);
                    continue;
                }
            }
            allowTenantIds.add(tenantId);
        }
        if (CollectionUtils.isNotEmpty(allowTenantIds)) {
            tenantEnvManager.batchChangeTenantInfo(allowTenantIds, env).safeData();
        }
        AdminTenantEnvOpInfo info = new AdminTenantEnvOpInfo();
        info.setUserId(getLoginUserId());
        info.setUserName(getName());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        info.setOperatorTime(sdf.format(new Date()));
        info.setTenantId(getLoginUserTenantId());
        info.setTenantIds(allowTenantIds);
        info.setDirection("to " + env.name());
        //可能有多个企业是超级管理企业，所以这里固定了，userId为登录企业的用户id（不一定是1）
        UserOperatorLogManager.asyncSavaLog(UserOperatorLog.create("1", "1", UserOperatorModuleEnum.TENANT_CONFIG_OPERATION.name(),
                UserOperatorModuleEnum.TENANT_CONFIG_OPERATION.name(), getLoginUserId(), UserOperationTypeEnum.EDIT.name(), null, JSONObject.toJSONString(info)));
        if (!notAllowChangeTenantIdSet.isEmpty()) {
            return Result.newError(String.format("以下企业不允许切换到%s环境:%s", env.getEnvironment(), notAllowChangeTenantIdSet));   // ignoreI18n   实施和开发自用
        }
        return Result.newSuccess();
    }

    @PostMapping("/getConfigTypes")
    public Result<Dict> getConfigTypes(@RequestParam(required = false) String term) {
        List<Amis.Option> options = getEnumOptions(TenantConfigurationTypeEnum.class, term);
        return Result.newSuccess(Dict.of("options", options));
    }

    @NotNull
    private List<Amis.Option> getEnumOptions(Class<? extends Enum<?>> clazz, String term) {
        final Enum<?>[] values = clazz.getEnumConstants();
        String finalTerm = StrUtil.removeAll(term, '_', ' ');
        List<Amis.Option> options = Arrays.stream(values)
                //倒序
                .sorted(Comparator.comparingInt(v -> -1 * v.ordinal()))
                .filter(v -> {
                    // 标记为 @Deprecated的不显示
                    try {
                        boolean isDeprecated = clazz.getField(v.name()).isAnnotationPresent(Deprecated.class);
                        if (isDeprecated) {
                            return false;
                        }
                    } catch (NoSuchFieldException e) {
                        // 处理异常逻辑，例如日志记录
                    }
                    return StrUtil.isBlank(finalTerm)
                            //忽略_和空格
                            || StrUtil.containsIgnoreCase(StrUtil.removeAll(v.name(), '_', ' '), finalTerm);
                })
                .map(v -> Amis.Option.of(v.name(), v.name()))
                .collect(Collectors.toList());
        return options;
    }


    @PostMapping("/getFormatTypes")
    public Result<Dict> getFormatTypes(@RequestParam(required = false) String term) {
        List<Amis.Option> options = getEnumOptions(FormatType.class, term);
        return Result.newSuccess(Dict.of("options", options));
    }


    /**
     * 初始化页面变量
     *
     * @return
     */
    @RequestMapping(value = "/initPageData", method = RequestMethod.GET)
    public Result<Dict> initPageData(@RequestParam String page) {
        Dict dict = Dict.create();
        if (Objects.equals(page, "tenantList")) {
            ImmutableMap<String, ImmutableSet<String>> groupWhiteMap = tenantConfigurationManager.getGroupWhiteMap(TenantConfigurationTypeEnum.FORBID_INTO_ENV);
            if (!groupWhiteMap.isEmpty()) {
                dict.put("reminder", "环境限制信息：" + JacksonUtil.toJson(groupWhiteMap));   // ignoreI18n   实施和开发自用
            }
        }
        if (Objects.equals(page, "connectorList")) {
            Date lastSyncTime = allConnectorInfoManager.getMongoLastSyncTime();
            if (lastSyncTime != null) {
                dict.put("reminder", "数据最后同步时间：" + DateUtil.format(lastSyncTime, "yyyy-MM-dd HH:mm:ss.SSS"));   // ignoreI18n   实施和开发自用
            }
        }
        if (Objects.equals(page, "streamList")) {
            Date lastSyncTime = allStreamInfoManager.getMongoLastSyncTime();
            if (lastSyncTime != null) {
                dict.put("reminder", "数据最后同步时间：" + DateUtil.format(lastSyncTime, "yyyy-MM-dd HH:mm:ss.SSS"));   // ignoreI18n   实施和开发自用
            }
        }
        return Result.newSuccess(dict);
    }


    /**
     * 远程cache
     *
     * @return
     */
    @PostMapping(value = "/handleRemoteJetCache")
    public Result<Object> handleRemoteJetCache(@RequestBody Dict dict) {
        //操作jetCache远程缓存
        String cacheName = dict.getStr("cacheName");
        String opt = dict.getStr("opt");
        if ("scanKey".equals(opt)) {
            Set<String> keys = redisDataSource.scanKeys("erpSyncData:cache:" + cacheName + "*");
            return Result.newSuccess(keys);
        }
        if ("get".equals(opt)) {
            String key = dict.getStr("key");
            Cache<Object, Object> cache = cacheManager.getCache(cacheName);
            if (cache instanceof MultiLevelCache) {
                RedissonCache remoteCache = (RedissonCache) ((MultiLevelCache<?, ?>) cache).caches()[1];
                Object o = remoteCache.get(key);
                return Result.newSuccess(o);
            }
        }
        return Result.newError("unsupported opt");
    }

    @GetMapping(value = "/cmsGlobalConfigTypes")
    public Result<Dict> webConfigTypes() {
        final List<Dict> collect = ConfigCenter.globalConfig.keySet().stream()
                .map(key -> Dict.of("label", key, "value", key))
                .collect(Collectors.toList());
        return Result.newSuccess(Dict.of("options", collect));
    }

    @GetMapping(value = "/cmsGlobalConfig")
    public Result<Dict> globalConfig(@RequestParam("key") String key) {
        // ConfigFactory 需要通知获取,速度较慢,直接调用configAdmin获取最新数据
        return Result.newSuccess(Dict.of("config", configCenterManager.getGlobalConfig(key)));
    }

    @PostMapping(value = "/updateCmsGlobalConfig")
    public Result<Void> updateGlobalConfig(@RequestBody Dict dict) {
        final String key = dict.getStr("key");
        final String config = dict.getStr("config");
        if (StringsUtils.anyBlank(key, config)) {
            return Result.newError("key or config is empty");
        }
        String cmsConfig = config.replace("\n", "\\n").trim();
        configCenterManager.updateGlobalConfig(key, cmsConfig);
        if (Arrays.stream(TenantConfigurationTypeEnum.values()).map(Enum::name).anyMatch(key::equals)) {
            tenantConfigurationManager.updateGlobalConfig(key, config);
        }
        return Result.newSuccess();
    }


    /**
     * 枚举多语查看
     *
     * @return
     */
    @PostMapping("/i18nEnum")
    public Result<Amis.Crud<Dict>> i18nEnum() {
        Set<Class<?>> i18nCls = ClassUtil.scanPackageBySuper("com.fxiaoke.open.erpsyncdata", I18nBase.class);
        List<Dict> allData = new ArrayList<>();
        I18nClient i18nClient = I18nClient.getInstance();
        Map<Byte, SupportLanguage> languageMapMapping = LangIndex.getInstance().getLanguageMapMapping();
        Map<String, Amis.Col> configMap = new HashMap<>();
        for (Class<?> cls : i18nCls) {
            if (EnumUtil.isEnum(cls)) {
                String enumName = cls.getSimpleName();
                log.info("begin scan {}", enumName);
                //noinspection unchecked
                Enum<? extends I18nBase>[] enumConstants = (Enum<? extends I18nBase>[]) cls.getEnumConstants();
                if (enumConstants != null) {
                    for (Enum<? extends I18nBase> anEnum : enumConstants) {
                        String name = enumName + "." + anEnum.name();
                        I18nBase item = (I18nBase) anEnum;
                        String i18nKey = item.getI18nKey();
                        Dict dict = Dict.of(
                                "name", name,
                                "i18nKey", i18nKey,
                                "defaultValue", item.getI18nValue(),
                                "notSetI18n", false
                        );
                        if (i18nKey != null) {
                            Localization localization = i18nClient.get(i18nKey, 0);
                            if (localization == null) {
                                dict.put("notSetI18n", true);
                            } else {
                                localization.getData().forEach((k, v) -> {
                                    SupportLanguage supportLanguage = languageMapMapping.get(k);
                                    if (supportLanguage != null) {
                                        configMap.putIfAbsent(supportLanguage.getName(), new Amis.Col().toggled(false));
                                        dict.put(supportLanguage.getName(), v);
                                    }
                                });
                            }
                        }
                        allData.add(dict);
                    }
                }
            }
        }
        Amis.ColHelper<Dict> colHelper = Amis.ColHelper.parseMap(allData, configMap);
        colHelper.get("i18nKey").toggled(true).searchable();
        colHelper.get("name").searchable();
        colHelper.get("defaultValue").searchable();
        return Result.newSuccess(colHelper.getCrud());
    }
}
