package com.fxiaoke.open.erpsyncdata.web.service.customFunction.api;

import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.OkHttpUtils;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.HttpRequest2;
import com.fxiaoke.open.erpsyncdata.preprocess.model.HttpResponse2;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> (^_−)☆
 */
@Slf4j
@Service
public class FuncApiProxyRequestImpl extends JsonFuncApiServiceImpl<HttpRequest2, HttpResponse2> {
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;

    public FuncApiProxyRequestImpl() {
        super("proxyRequest");
    }

    @Override
    public Result<HttpResponse2> execute(String tenantId, HttpRequest2 arg) {
        if (!tenantConfigurationManager.inWhiteList(tenantId, TenantConfigurationTypeEnum.PROXY_REQUEST_EIS)) {
            log.info("invalid tenant call proxyRequest,{}",tenantId);
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        HttpResponse2 execute = OkHttpUtils.execute(arg);
        return Result.newSuccess(execute);
    }
}
