package com.fxiaoke.open.erpsyncdata.web.service.customFunction.api;

import com.fxiaoke.open.erpsyncdata.apiproxy.utils.HttpRspLimitLenUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.ParameterizedType;

/**
 * <AUTHOR> (^_−)☆
 */
public abstract class JsonFuncApiServiceImpl<A, R> implements FuncApiService{
    private final String apiType;
    private final Class<A> argType;

    @SuppressWarnings("unchecked")
    public JsonFuncApiServiceImpl(final String apiType) {
        this.apiType = apiType;
        argType = (Class<A>) (((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0]);
    }

    @Override
    public String getApiType() {
        return apiType;
    }

    protected A extractArg(HttpServletRequest request) {
        String data;
        try {
            long limitSize = ConfigCenter.LIST_CONTENT_LENGTH_LIMIT;
            Result<String> dataResult = HttpRspLimitLenUtil.convertStreamToString(request.getInputStream(), limitSize);
            data = dataResult.safeData();
        } catch (Exception e) {
            throw ErpSyncDataException.wrap(e);
        }
        A arg = JacksonUtil.fromJson(data, argType);
        return arg;
    }

    public Result<R> executeByRequest(String tenantId,HttpServletRequest request) {
        A a = extractArg(request);
        return execute(tenantId,a);
    }

    public abstract Result<R> execute(String tenantId,A arg);

}
