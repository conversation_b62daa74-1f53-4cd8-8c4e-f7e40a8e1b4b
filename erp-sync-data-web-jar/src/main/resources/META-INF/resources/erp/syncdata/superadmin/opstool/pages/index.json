{"status": 0, "msg": "", "data": {"pages": [{"label": "Home", "url": "/", "redirect": "/readme"}, {"children": [{"label": "使用说明", "url": "readme", "icon": "fa-brands fa-readme", "schemaApi": "get:./pages/readme.json"}]}, {"label": "业务数据", "children": [{"label": "企业列表", "url": "/tenantList", "icon": "fa fa-list", "schemaApi": "get:./pages/info/tenantList.json"}, {"label": "单租户数据", "url": "/single", "children": [{"label": "集成流", "url": "/streamList", "icon": "fa fa-list", "schemaApi": "get:./pages/info/streamList.json"}, {"label": "ERP对象列表", "url": "/objList", "icon": "fa fa-list", "schemaApi": "get:./pages/info/objList.json"}, {"label": "ERP字段列表", "url": "/fieldList", "icon": "fa fa-list", "schemaApi": "get:./pages/info/fieldList.json"}, {"label": "ERP字段扩展列表", "url": "/fieldExtendList", "icon": "fa fa-list", "schemaApi": "get:./pages/info/fieldExtendList.json"}, {"label": "对象自定义函数使用情况", "url": "/findObjFuncUsage", "icon": "fa fa-list", "schemaApi": "get:./pages/info/findObjFuncUsage.json"}]}, {"label": "全局数据", "url": "/multi", "children": [{"label": "连接器", "url": "/connectorList", "icon": "fa fa-list", "schemaApi": "get:./pages/info/connectorList.json"}, {"label": "集成流", "url": "/streamList_new", "icon": "fa fa-list", "schemaApi": "get:./pages/info/streamList_new.json"}, {"label": "连接器HUB", "url": "/connectorHub", "icon": "fa fa-list", "schemaApi": "get:./pages/info/connectorHub.json"}]}]}, {"label": "监控数据", "children": [{"label": "企业灰度操作日志列表", "url": "/operationList", "icon": "fa fa-list", "schemaApi": "get:./pages/info/operationList.json"}, {"label": "企业配额", "url": "/syncQuota", "icon": "fa fa-list", "schemaApi": "get:./pages/info/syncQuota.json"}, {"label": "延迟数据", "url": "/dataNodeList", "icon": "fa fa-list", "schemaApi": "get:./pages/info/dataNodeList.json"}]}, {"label": "运维管理", "children": [{"label": "配置管理", "url": "/config", "icon": "fa-solid fa-gear", "schemaApi": "get:./pages/config/config.json", "children": [{"label": "配置", "url": "/config", "icon": "fa-solid fa-gear", "schemaApi": "get:./pages/config/config.json"}, {"label": "全局配置", "url": "/config/globalConfig", "schemaApi": "get:./pages/config/globalConfig.json"}, {"label": "特殊配置", "url": "/specialConfig", "children": [{"label": "写crm对象限速", "url": "/config/Check2CrmBatchLimit.json", "schemaApi": "get:./pages/config/Check2CrmBatchLimit.json"}, {"label": "CRM->ERP分发速度限制", "url": "/config/2erpTenantLimit", "schemaApi": "get:./pages/config/2ErpTenantLimit.json"}, {"label": "推送数据限速", "url": "/config/pushSeedLimit", "schemaApi": "get:./pages/config/pushSeedLimit.json"}, {"label": "调用ERP接口分页", "url": "/config/updatePagingSize", "schemaApi": "get:./pages/config/erpListPageNum.json"}, {"label": "配置中心全局配置", "url": "/config/cmsGlobalConfig", "schemaApi": "get:./pages/config/cmsGlobalConfig.json"}]}, {"label": "枚举多语查看", "url": "/config/i18nEnum", "schemaApi": "get:./pages/config/i18nEnum.json"}]}, {"label": "多云管理", "url": "/multicloud", "icon": "fa fa-cloud", "children": [{"label": "多云配置管理", "url": "/multicloud/configManager", "schemaApi": "get:./pages/multicloud/configManager.json"}, {"label": "多云请求", "url": "/multicloud/multiRequest", "schemaApi": "get:./pages/multicloud/multiRequest.json"}]}, {"label": "后台操作（请谨慎操作）", "url": "/job", "children": [{"label": "pg管理", "url": "/job/pg", "schemaApi": "get:./pages/job/pg.json"}, {"label": "集合", "url": "/job/job", "schemaApi": "get:./pages/job/job.json"}, {"label": "清理资源", "url": "/job/clean", "schemaApi": "get:./pages/job/clean.json"}, {"label": "刷库", "url": "/job/brushData", "schemaApi": "get:./pages/job/brushData.json"}, {"label": "pg ddl操作", "url": "/job/pgddl", "schemaApi": "get:./pages/job/pgddl.json"}, {"label": "mongo ddl操作", "url": "/job/mongoddl", "schemaApi": "get:./pages/job/mongoddl.json"}]}, {"label": "重试操作（请谨慎操作）", "url": "/retry", "children": [{"label": "mq发送失败的数据", "url": "/retry/retry", "schemaApi": "get:./pages/retry/retry.json"}, {"label": "临时库数据失败重试", "url": "/retry/retryTemp", "schemaApi": "get:./pages/retry/retryTemp.json"}]}, {"label": "多企业操作", "children": [{"label": "统计数据库表数量", "url": "/databaseTableCount", "icon": "fa fa-list", "schemaApi": "get:./pages/batchTenant/databaseTableCount.json"}, {"label": "重置全链路日志过期时间", "url": "/resetSyncLogExpireTime", "icon": "fa fa-list", "schemaApi": "get:./pages/batchTenant/resetSyncLogExpireTime.json"}, {"label": "按时间补刷数据", "url": "/processChangeDataByTime", "icon": "fa fa-list", "schemaApi": "get:./pages/batchTenant/processChangeDataByTime.json"}, {"label": "获取Sap CPQ的集成流信息", "url": "/allSapCpqPloyDetails", "icon": "fa fa-list", "schemaApi": "get:./pages/batchTenant/allSapCpqPloyDetails.json"}, {"label": "全集成流启用", "url": "/allIntegratedEnable", "icon": "fa fa-list", "schemaApi": "get:./pages/batchTenant/allIntegratedEnable.json"}]}, {"label": "模板", "icon": "fa-solid fa-gear", "children": [{"label": "模板列表", "url": "/templateTable", "schemaApi": "get:./pages/template/templateTable.json"}, {"label": "APL模板", "url": "/aplTemplate", "schemaApi": "get:./pages/template/aplTemplate.json"}, {"label": "解析对象和集成流", "url": "/templateParse", "schemaApi": "get:./pages/template/templateParse.json"}, {"label": "模板对象数据", "url": "/objInfo", "schemaApi": "get:./pages/template/objInfoTable.json"}, {"label": "模板集成流数据", "url": "/streamInfo", "schemaApi": "get:./pages/template/streamInfoTable.json"}]}]}]}}