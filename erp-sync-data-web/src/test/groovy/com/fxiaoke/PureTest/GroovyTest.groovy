package com.fxiaoke.PureTest

import com.facishare.userlogin.fsi.utils.MD5Util
import groovy.util.logging.Slf4j

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2021/1/11
 */
@Slf4j
class GroovyTest {
    static void main(String[] args) {
        Map<String,String> params = new HashMap<>()
        params.put("APPSECRET","crmBank")
        params.put("STARTTIME","*************")
        params.put("ENDTIME","*************")
        params.put("OFFSET","0")
        params.put("LIMIT","50")
        //params.put("USER_ID","100001")
        //params.put("DATAID","740189")
        generateToken(params)
        params.each {entry->
            if(entry.value.contains("Bank")) {
                println(entry.key)
            }
        }
    }

    private static void generateToken(Map<String,String> params) {
        // 将参数以参数名的字典升序排序
        Map<String, String> sortParams = new TreeMap<String, String>(params)
        // 遍历排序的字典,并拼接value1+value2......格式
        StringBuilder values = new StringBuilder()
        for (Map.Entry<String, String> entry : sortParams) {
            values.append(entry.getValue().toUpperCase())
        }
        String sign = MD5Util.getMD5(values.toString())
        println(sign)
    }
}
