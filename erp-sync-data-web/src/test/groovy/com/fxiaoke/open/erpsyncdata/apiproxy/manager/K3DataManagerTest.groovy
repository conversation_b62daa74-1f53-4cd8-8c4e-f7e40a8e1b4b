//package com.fxiaoke.open.erpsyncdata.apiproxy.manager
//
//import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum
//import com.fxiaoke.open.erpsyncdata.BaseSpockTest
//import com.fxiaoke.open.erpsyncdata.admin.model.InitK3Obj
//import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.K3DataConverter
//import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.MetaDataInfoManager
//import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient
//import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData
//import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryArg
//import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.SaveArg
//import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao
//import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpTenantConfigurationDao
//import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity
//import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator
//import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil
//import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil
//import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil
//import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg
//import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg
//import com.fxiaoke.open.erpsyncdata.preprocess.model.K3CloudConnectParam
//import com.fxiaoke.open.erpsyncdata.common.rule.FilterData
//import com.google.common.collect.Maps
//import groovy.util.logging.Slf4j
//import org.junit.Ignore
//import org.junit.Test
//import org.springframework.beans.factory.annotation.Autowired
///**
// *
// * <AUTHOR> (^_−)☆
// * @date 2020/11/13
// */
//@Ignore
//@Slf4j
//class K3DataManagerTest extends BaseSpockTest {
//    @Autowired
//    private K3DataManager k3DataManager
//    @Autowired
//    private ErpConnectInfoDao erpConnectInfoDao;
//    @Autowired
//    private MetaDataInfoManager metaDataInfoManager;
//    @Autowired
//    private IdGenerator idGenerator;
//    @Autowired
//    private ErpTenantConfigurationDao erpTenantConfigurationDao;
//    @Test
//    public void testView() {
////        ErpConnectInfoEntity query=new ErpConnectInfoEntity()
////        query.setChannel(ErpChannelEnum.ERP_K3CLOUD)
////        List<ErpConnectInfoEntity> list=erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("tenantId")).queryList(query)
////        for(ErpConnectInfoEntity entity:list){
////            ErpTenantConfigurationEntity config=new ErpTenantConfigurationEntity()
////            config.setId(idGenerator.get())
////            config.setChannel(ErpChannelEnum.ERP_K3CLOUD.name())
////            config.setDataCenterId(entity.getId())
////            config.setTenantId(entity.getTenantId())
////            config.setConfiguration("['*']")
////            config.setType(TenantConfigurationTypeEnum.USE_BILLQUERY_INTERFACE_TO_VIEW.name())
////            config.setCreateTime(System.currentTimeMillis())
////            config.setUpdateTime(System.currentTimeMillis())
////            erpTenantConfigurationDao.insert(config)
////        }
//        String formId="SAL_SaleOrder"
//        ErpIdArg erpIdArg = new ErpIdArg();
//        erpIdArg.setTenantId("81243");
//        erpIdArg.setObjAPIName(formId);
//        erpIdArg.setDataId("219279");
//        ErpConnectInfoEntity entity=erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("81243")).getByIdAndTenantId("81243","628312575457230848");
//        K3CloudConnectParam k3CloudConnectParam=new K3CloudConnectParam();
//        k3CloudConnectParam.setBaseUrl("http://172.31.100.60/k3cloud/");
//        k3CloudConnectParam.setDbId("5ec229fad54306");
//        k3CloudConnectParam.setDbName("接口环境");
//        k3CloudConnectParam.setAuthType(1);
//        k3CloudConnectParam.setUserName("ces2");
//        k3CloudConnectParam.setPassword("8888888");
//        InitK3Obj.Arg arg=new InitK3Obj.Arg();
//        arg.setTenantId("81243")
//        arg.setDataCenterId("628312575457230848")
//        arg.setFormId(formId)
////        def obj111 = superAdminObjDescribeService.initK3Obj(arg)
////
//        K3CloudApiClient apiClient = K3CloudApiClient.newInstance(tenantId, k3CloudConnectParam, "628312575457230848");
////        Result<QueryBusinessInfoResult> businessInfo = apiClient.queryBusinessInfo(new QueryBusinessInfoArg(formId));
////        def describe = metaDataInfoManager.getErpObjectDescribe(apiClient, new QueryBusinessInfoArg(formId), true);
////        ErpObjectDescribe erpObjectDescribe = MetaDataInfoUtils.analysisK3MetaDataInfo(businessInfo.getData(), true);
//        Map<String,String>formId2DataId= Maps.newHashMap()
////        formId2DataId.put("PRD_MO","")
////        formId2DataId.put("SAL_DELIVERYNOTICE","")
////        formId2DataId.put("SC_RECEIVESETTLEBILL","")
////        formId2DataId.put("STK_TransferDirect","")
////        formId2DataId.put("SAL_SC_CustMat","")
////        formId2DataId.put("BD_SAL_PriceList","778169")
////        formId2DataId.put("STK_Inventory","")
////        formId2DataId.put("AR_receivable","")
////        formId2DataId.put("SAL_SaleOrder","101576")
//        formId2DataId.put("AR_RECEIVEBILL","104257")
////        formId2DataId.put("AR_REFUNDBILL","100005")
////        formId2DataId.put("BD_BatchMainFile","679985")
////        formId2DataId.put("BD_CommonContact","660588")
////        formId2DataId.put("BD_Customer","CUST00281")
////        formId2DataId.put("BD_Empinfo","103774")
////        formId2DataId.put("BD_MATERIAL","00004")
////        formId2DataId.put("BD_MATERIALUNITCONVERT","100022")
////        formId2DataId.put("BD_OPERATOR","100004")
////        formId2DataId.put("BD_SerialMainFile","100004")
////        formId2DataId.put("BD_STOCK","681685")
////        formId2DataId.put("BD_UNIT","10096")
////        formId2DataId.put("BOS_ASSISTANTDATA_DETAIL","0010fb03051146d19381fbae0e221f14")
////        formId2DataId.put("IV_SALESIC","100036")
////        formId2DataId.put("IV_SALESOC","100081")
////        formId2DataId.put("ORG_Organizations","100759")
////        formId2DataId.put("SAL_OUTSTOCK","100266")
////        formId2DataId.put("SAL_RETURNSTOCK","100008")
////        formId2DataId.put("SEC_User","100767")
//
//        for(String obj:formId2DataId.keySet()){
//            arg.setFormId(obj)
//            erpIdArg.setObjAPIName(obj);
//            erpIdArg.setDataId(formId2DataId.get(obj));
//            //def initResult = superAdminObjDescribeService.initK3Obj(arg)
//            def result = k3DataManager.getErpObjData(erpIdArg, entity)
//            println ""
//        }
////        String con="{\"baseUrl\":\"https://gdjls.test.ik3cloud.com/k3cloud\",\"dbId\":\"20190805173235053\",\"dbName\":\"接口环境\",\"authType\":1,\"userName\":\"wzx1\",\"password\":\"abc@12345\",\"appId\":null,\"lcid\":2052,\"useFsHttpClient\":true}"
////        entity.setConnectParams(con)
//        def result = k3DataManager.getErpObjData(erpIdArg, entity)
//        println(JacksonUtil.toJson(result))
//    }
//
//    @Test
//    public void testSave() {
//        String tenantId = "81138"
//        SaveArg saveArg = new SaveArg();
//        StandardData standardData = JacksonUtil.fromJson(dataJson, StandardData.class)
//        K3DataConverter saveConverter = k3DataManager.buildSaveConverter(tenantId,null, standardData);
//        //转换参数
//        saveConverter.fillSaveArg(standardData, saveArg,tenantId);
//        println(JacksonUtil.toJson(saveArg))
//    }
//
//    @Test
//    public void testQuery() {
//        String ei="80774";
//        ErpConnectInfoEntity connectInfo = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(ei)).listByTenantId(ei).get(0);
//        TimeFilterArg timeFilterArg=new TimeFilterArg();
//        timeFilterArg.setTenantId(ei);
//        timeFilterArg.setObjAPIName("BD_STOCK");
//        timeFilterArg.setStartTime(System.currentTimeMillis()-1000*3600*24*30L);
//        timeFilterArg.setEndTime(System.currentTimeMillis());
//        timeFilterArg.setOperationType(1)
//        timeFilterArg.setLimit(5)
//        def result = k3DataManager.listErpObjDataByTime(timeFilterArg, connectInfo);
//
//
//
//        String tenantId = "81138"
//        List<FilterData> filterDatas = [
//                FilterData.builder()
//                        .fieldApiName("FCustId")
//                        .operate(FilterOperatorEnum.IN.value)
//                        .fieldValue([715926, 716188]).build(),
//                FilterData.builder()
//                        .fieldApiName("FUseOrgId.FNumber")
//                        .operate(FilterOperatorEnum.IS.value)
//                        .fieldValue(["000"]).build(),
//        ]
//        QueryArg queryArg = new QueryArg()
//        queryArg.setFilterString("FNumber = '20200706-000125'")
//        queryArg.addAndFilters(filterDatas)
//        queryArg.setFormId("BD_Customer")
//        queryArg.setFieldKeys("FCustId,FNumber,FName,FUseOrgId.FNumber,FUseOrgId,FCreateOrgId,FDocumentStatus,FForbidStatus,FInvoiceType");
//        def data = k3DataManager.queryK3ObjData(tenantId, "",queryArg)
//        println(GsonUtil.toJson(data))
//    }
//    String dataJson = "{\n" +
//            "    \"objAPIName\": \"SAL_SaleOrder\",\n" +
//            "    \"masterFieldVal\": {\n" +
//            "        \"ChargeId.Name\": \"zsl1110测试客户001\",\n" +
//            "        \"FSaleOrgId.FNumber\": \"000\",\n" +
//            "        \"FBillTypeID.FNumber\": \"XSDD01_SYS\",\n" +
//            "        \"FBusinessType\": \"NORMAL\",\n" +
//            "        \"FSaleOrderFinance.FLocalCurrId.FNumber\": \"PRE007\",\n" +
//            "        \"IsDirectChange\": false,\n" +
//            "        \"FSaleOrderFinance.FJoinStockAmount\": 0.0,\n" +
//            "        \"FReceiveId.FNumber\": \"20201110-000435\",\n" +
//            "        \"BillTypeId.Name\": \"标准销售订单\",\n" +
//            "        \"HeadLocId.Id\": 100248,\n" +
//            "        \"ModifierId.Id\": 605439,\n" +
//            "        \"FChangeDate\": null,\n" +
//            "        \"FSettleAddress\": null,\n" +
//            "        \"BillTypeId.Id\": \"eacb50844fc84a10b03d7b841f3a6278\",\n" +
//            "        \"FSaleOrderFinance.FSettleModeId\": null,\n" +
//            "        \"FCloserId\": null,\n" +
//            "        \"FCorrespondOrgId\": null,\n" +
//            "        \"FLinkPhone\": \" \",\n" +
//            "        \"FSaleOrderFinance.FBillTaxAmount_LC\": 23.0,\n" +
//            "        \"FISINIT\": false,\n" +
//            "        \"FSaleOrderFinance.FCrePreBatchOver\": \"0\",\n" +
//            "        \"FSaleOrderFinance.FMargin\": 0.0,\n" +
//            "        \"FHeadDeliveryWay\": null,\n" +
//            "        \"FChargeId.FNumber\": \"20201110-000435\",\n" +
//            "        \"FSettleId.FNumber\": \"20201110-000435\",\n" +
//            "        \"FManualClose\": false,\n" +
//            "        \"FSaleOrderFinance.FBillAmount_LC\": 177.0,\n" +
//            "        \"FSaleOrderFinance.FJoinOrderAmount\": 0.0,\n" +
//            "        \"SalerId.Name\": \"谢嘉裕\",\n" +
//            "        \"FSaleOrderFinance.FRecConditionId\": null,\n" +
//            "        \"FSaleOrderFinance.FIsPriceExcludeTax\": true,\n" +
//            "        \"FSaleOrderFinance.FCrePreBatAndMonStatus\": \"A\",\n" +
//            "        \"FSalePhaseID\": null,\n" +
//            "        \"FSaleOrderFinance.FCreChkAmount\": 0.0,\n" +
//            "        \"FCancelStatus\": \"A\",\n" +
//            "        \"FCreateDate\": \"2020-11-11T19:35:55.207\",\n" +
//            "        \"CustId.Id\": 738962,\n" +
//            "        \"FHEADLOCID.FNumber\": \"BIZ202011101046090\",\n" +
//            "        \"FSaleOrderFinance.FRecNoticeNo\": \" \",\n" +
//            "        \"FSaleOrderFinance.FDiscountListId\": null,\n" +
//            "        \"FSaleOrderFinance.FExchangeRate\": 1.0,\n" +
//            "        \"ReceiveId.Id\": 738962,\n" +
//            "        \"FOppID\": 0,\n" +
//            "        \"FSaleOrderFinance.FExchangeTypeId.FNumber\": \"HLTX01_SYS\",\n" +
//            "        \"ChargeId.Id\": 738962,\n" +
//            "        \"FSaleOrderFinance.FNeedPayAdvance\": false,\n" +
//            "        \"FSaleOrderFinance.FAssRefundMargin\": 0.0,\n" +
//            "        \"FSaleOrderFinance.FRecBarcodeLink\": \" \",\n" +
//            "        \"FSaleOrderFinance.FPayAdvanceRate\": 0.0,\n" +
//            "        \"FSaleGroupId\": null,\n" +
//            "        \"FSaleOrderFinance.FBillTaxAmount\": 23.0,\n" +
//            "        \"ReceiveId.Name\": \"zsl1110测试客户001\",\n" +
//            "        \"FBillNo\": \"XSDD000346\",\n" +
//            "        \"CustId.Name\": \"zsl1110测试客户001\",\n" +
//            "        \"FLinkMan\": \" \",\n" +
//            "        \"FSaleOrderFinance.FMarginLevel\": 0.0,\n" +
//            "        \"FReceiveAddress\": \"000\",\n" +
//            "        \"SaleOrgId.Name\": \"纷享销客\",\n" +
//            "        \"SalerId.Id\": 706328,\n" +
//            "        \"FModifierId.FNumber\": null,\n" +
//            "        \"FChangerId\": null,\n" +
//            "        \"SettleId.Id\": 738962,\n" +
//            "        \"SettleId.Name\": \"zsl1110测试客户001\",\n" +
//            "        \"FCreditCheckResult\": \"0\",\n" +
//            "        \"CreatorId.Id\": 605439,\n" +
//            "        \"FSaleOrderFinance.FAssociateMargin\": 0.0,\n" +
//            "        \"FSaleOrderFinance.FOverOrgTransDirect\": false,\n" +
//            "        \"FCloseDate\": null,\n" +
//            "        \"FCloseStatus\": \"A\",\n" +
//            "        \"FNetOrderBillNo\": \" \",\n" +
//            "        \"FSaleDeptId\": null,\n" +
//            "        \"FNote\": \" \",\n" +
//            "        \"FSaleOrderFinance.FCreChkDays\": 0,\n" +
//            "        \"FApproveDate\": null,\n" +
//            "        \"FDocumentStatus\": \"A\",\n" +
//            "        \"FSaleOrderFinance.FIsIncludedTax\": true,\n" +
//            "        \"FCancellerId\": null,\n" +
//            "        \"FSalerId.FNumber\": \"88888_GW000153_100762\",\n" +
//            "        \"FSaleOrderFinance.FBillAllAmount_LC\": 200.0,\n" +
//            "        \"FReceiveContact\": null,\n" +
//            "        \"FVersionNo\": \"000\",\n" +
//            "        \"FSaleOrderFinance.FRecBillId\": null,\n" +
//            "        \"FSaleOrderFinance.FBillAllAmount\": 200.0,\n" +
//            "        \"FNetOrderBillId\": 0,\n" +
//            "        \"SaleOrgId.Id\": 1,\n" +
//            "        \"FCancelDate\": null,\n" +
//            "        \"FSaleOrderFinance.FPayAdvanceAmount\": 0.0,\n" +
//            "        \"FSignStatus\": \"A\",\n" +
//            "        \"FCustId.FNumber\": \"20201110-000435\",\n" +
//            "        \"FIsMobile\": false,\n" +
//            "        \"FSaleOrderFinance.FCreMonControlOver\": \"0\",\n" +
//            "        \"FSaleOrderFinance.FBillAmount\": 177.0,\n" +
//            "        \"FSaleOrderFinance.FCreChkOverAmount\": 0.0,\n" +
//            "        \"FSaleOrderFinance.FCreChkStatus\": \"A\",\n" +
//            "        \"FApproverId\": null,\n" +
//            "        \"FModifyDate\": \"2020-11-11T19:35:55.207\",\n" +
//            "        \"FSaleOrderFinance.FSettleCurrId.FNumber\": \"PRE001\",\n" +
//            "        \"FDate\": \"2020-11-11T00:00:00\",\n" +
//            "        \"FChangeReason\": \" \",\n" +
//            "        \"FSaleOrderFinance.FPriceListId\": null,\n" +
//            "        \"FCreatorId.FNumber\": null,\n" +
//            "        \"FSOFrom\": \" \"\n" +
//            "    },\n" +
//            "    \"detailFieldVals\": {\n" +
//            "        \"SAL_SaleOrder.SaleOrderEntry\": [\n" +
//            "            {\n" +
//            "                \"FTerminaterId\": null,\n" +
//            "                \"BaseUnitId.Id\": 10101,\n" +
//            "                \"FOrderEntryPlan[0].FNOTICEREMAINQTY\": 5.0,\n" +
//            "                \"MaterialId.Id\": 719542,\n" +
//            "                \"StockUnitID.Name\": \"Pcs\",\n" +
//            "                \"FOrderEntryPlan[0].FPlanQty\": 5.0,\n" +
//            "                \"FLOCKQTY\": 0.0,\n" +
//            "                \"FARJOINAMOUNT\": 0.0,\n" +
//            "                \"MaterialId.Name\": \"贝贝产品100\",\n" +
//            "                \"FLeftQty\": 0.0,\n" +
//            "                \"FParentRowId\": \" \",\n" +
//            "                \"FOrderEntryPlan[0].FNOTICEBASEQTY\": 0.0,\n" +
//            "                \"SupplyOrgId.Name\": \"纷享销客\",\n" +
//            "                \"FAmount\": 88.5,\n" +
//            "                \"FStockBaseDen\": 0.0,\n" +
//            "                \"FOrderEntryPlan[0].FNOTICEREMAINBASEQTY\": 5.0,\n" +
//            "                \"FStockBaseTransJoinQty\": 0.0,\n" +
//            "                \"FOrderEntryPlan[0].FBaseDeliRemainQty\": 5.0,\n" +
//            "                \"FMaterialName\": null,\n" +
//            "                \"FStockQty\": 5.0,\n" +
//            "                \"FEntryTaxRate\": null,\n" +
//            "                \"FSrcType\": \" \",\n" +
//            "                \"FTRANSRETURNBASEQTY\": 0.0,\n" +
//            "                \"FBefDisAmt\": 0.0,\n" +
//            "                \"FMapName\": null,\n" +
//            "                \"FAvailableQty\": 0.0,\n" +
//            "                \"FBaseReturnQty\": 0.0,\n" +
//            "                \"FSPMENTRYID\": \" \",\n" +
//            "                \"SupplyOrgId.Id\": 1,\n" +
//            "                \"SettleOrgIds\": null,\n" +
//            "                \"OrderEntryPlan[0].PlanBaseUnitId.Name\": \"Pcs\",\n" +
//            "                \"FBaseUnitQty\": 5.0,\n" +
//            "                \"FRetailSaleProm\": false,\n" +
//            "                \"ReceiptOrgId.Name\": \"纷享销客\",\n" +
//            "                \"FPurQty\": 0.0,\n" +
//            "                \"FSOStockLocalId\": null,\n" +
//            "                \"FBASEAPQTY\": 0.0,\n" +
//            "                \"FAuxPropId\": null,\n" +
//            "                \"FPurUnitID\": null,\n" +
//            "                \"FMaterialId.FNumber\": \"CH4470\",\n" +
//            "                \"FOrderEntryPlan[0].FDetailLocAddress\": \"000\",\n" +
//            "                \"FRetNoticeQty\": 0.0,\n" +
//            "                \"FServiceContext\": null,\n" +
//            "                \"OrderEntryPlan[0].PlanBaseUnitId.Id\": 10101,\n" +
//            "                \"PriceUnitId.Id\": 10101,\n" +
//            "                \"FTransportLeadTime1\": 0,\n" +
//            "                \"FTaxCombination\": null,\n" +
//            "                \"FInvoiceQty\": 0.0,\n" +
//            "                \"FCanOutQty\": 5.0,\n" +
//            "                \"FChangeFlag\": \" \",\n" +
//            "                \"OwnerId.Name\": \"纷享销客\",\n" +
//            "                \"FBFLowId\": null,\n" +
//            "                \"FReceiptOrgId.FNumber\": \"000\",\n" +
//            "                \"FFreezeDate\": null,\n" +
//            "                \"FStockOutQty\": 0.0,\n" +
//            "                \"FEntryDiscountList\": null,\n" +
//            "                \"FIsReturn\": null,\n" +
//            "                \"FMrpTerminateStatus\": \"A\",\n" +
//            "                \"FLOCKFLAG\": false,\n" +
//            "                \"FBaseDeliQty\": 0.0,\n" +
//            "                \"FZHJStockQty\": 0.0,\n" +
//            "                \"FReturnType\": \" \",\n" +
//            "                \"FRowType\": \"Service\",\n" +
//            "                \"FSRCBIZUNITID\": null,\n" +
//            "                \"FBaseARJoinQty\": 0.0,\n" +
//            "                \"FSysPrice\": 0.0,\n" +
//            "                \"FSalBaseARJoinQty\": 0.0,\n" +
//            "                \"FOrderEntryPlan[0].FPlanUnitId.FNumber\": \"Pcs\",\n" +
//            "                \"FEntryTaxAmount\": null,\n" +
//            "                \"FInvoiceAmount\": 0.0,\n" +
//            "                \"FAwaitQty\": 0.0,\n" +
//            "                \"FTerminateDate\": null,\n" +
//            "                \"FParentMatId\": null,\n" +
//            "                \"FOrderEntryPlan[0].FBasePlanQty\": 5.0,\n" +
//            "                \"FTRANSRETURNQTY\": 0.0,\n" +
//            "                \"OrderEntryPlan[0].DetailLocId.Id\": 100248,\n" +
//            "                \"FReceiveAmount\": 0.0,\n" +
//            "                \"FOUTLMTUNIT\": \"SAL\",\n" +
//            "                \"FRemainOutQty\": 5.0,\n" +
//            "                \"FStockBaseARJoinQty\": 0.0,\n" +
//            "                \"FSupplyOrgId.FNumber\": \"000\",\n" +
//            "                \"FBaseReBackQty\": 0.0,\n" +
//            "                \"FExpUnit\": \" \",\n" +
//            "                \"FReBackQty\": 0.0,\n" +
//            "                \"FPrice\": 17.699115,\n" +
//            "                \"FMaterialIsSubContract\": null,\n" +
//            "                \"FQty\": 5.0,\n" +
//            "                \"FProduceDate\": null,\n" +
//            "                \"FStockBaseCanReturnQty\": 0.0,\n" +
//            "                \"FFreezerId\": null,\n" +
//            "                \"FInventoryQty\": 0.0,\n" +
//            "                \"FSalBaseNum\": 0.0,\n" +
//            "                \"FCONSIGNSETTQTY\": 0.0,\n" +
//            "                \"FBasePurJoinQty\": 0.0,\n" +
//            "                \"FOrderEntryPlan[0].FDeliRemainQty\": 5.0,\n" +
//            "                \"FPurJoinQty\": 0.0,\n" +
//            "                \"FBaseTransJoinQty\": 0.0,\n" +
//            "                \"FISMRP\": false,\n" +
//            "                \"FBaseStockOutQty\": 0.0,\n" +
//            "                \"FExpiryDate\": null,\n" +
//            "                \"OutLmtUnitID.Name\": \"Pcs\",\n" +
//            "                \"FInvoiceJoinQty\": 0.0,\n" +
//            "                \"FBASEFINARQTY\": 0.0,\n" +
//            "                \"OwnerId.Id\": 1,\n" +
//            "                \"ReceiptOrgId.Id\": 1,\n" +
//            "                \"OrderEntryPlan[0].PlanUnitId.Id\": 10101,\n" +
//            "                \"FOrderEntryPlan[0].FBaseDeliCommitQty\": 0.0,\n" +
//            "                \"FOutLmtUnitID.FNumber\": \"Pcs\",\n" +
//            "                \"StockUnitID.Id\": 10101,\n" +
//            "                \"FAllAmount_LC\": 100.0,\n" +
//            "                \"FIsInventory\": null,\n" +
//            "                \"FOrderEntryPlan[0].FPlanDeliveryDate\": \"2020-11-11T19:35:55.14\",\n" +
//            "                \"FPriceListEntry\": null,\n" +
//            "                \"FSOStockId\": null,\n" +
//            "                \"FMinPlanDeliveryDate\": \"2020-11-11T19:35:55.14\",\n" +
//            "                \"FMrpCloseStatus\": \"A\",\n" +
//            "                \"FPriceCoefficient\": 1.0,\n" +
//            "                \"FMrpFreezeStatus\": \"A\",\n" +
//            "                \"FPriceBaseQty\": 5.0,\n" +
//            "                \"FStockBaseCanOutQty\": 5.0,\n" +
//            "                \"FMtoNo\": \" \",\n" +
//            "                \"PriceUnitId.Name\": \"Pcs\",\n" +
//            "                \"FAllAmount\": 100.0,\n" +
//            "                \"FBASEARQTY\": 0.0,\n" +
//            "                \"FLot\": null,\n" +
//            "                \"FStockBasePurJoinQty\": 0.0,\n" +
//            "                \"FAmount_LC\": 88.5,\n" +
//            "                \"FReserveType\": \"1\",\n" +
//            "                \"FPriceDiscount\": 0.0,\n" +
//            "                \"FPurOrderQty\": 0.0,\n" +
//            "                \"FSTOCKBASEREBACKQTY\": 0.0,\n" +
//            "                \"FBaseCanOutQty\": 5.0,\n" +
//            "                \"FARQTY\": 0.0,\n" +
//            "                \"FPriority\": 0,\n" +
//            "                \"FDeliveryDate\": \"2020-11-11T19:35:55.14\",\n" +
//            "                \"FOldQty\": 5.0,\n" +
//            "                \"FSPMANDRPMCONTENT\": \" \",\n" +
//            "                \"FBomId\": null,\n" +
//            "                \"FOwnerTypeId\": \"BD_OwnerOrg\",\n" +
//            "                \"FBaseCanReturnQty\": 0.0,\n" +
//            "                \"FOrderEntryPlan[0].FTransportLeadTime\": 0,\n" +
//            "                \"FOrderEntryPlan[0].FStockId\": null,\n" +
//            "                \"FSALBASEFINARQTY\": 0.0,\n" +
//            "                \"FMapId\": null,\n" +
//            "                \"FEntryNote\": \"111\",\n" +
//            "                \"FSTOCKBASESTOCKOUTQTY\": 0.0,\n" +
//            "                \"FBaseDeliveryMaxQty\": 5.0,\n" +
//            "                \"FLimitDownPrice\": 0.0,\n" +
//            "                \"FOrderEntryPlan[0].FPlanBaseUnitId.FNumber\": \"Pcs\",\n" +
//            "                \"FStockBaseQty\": 5.0,\n" +
//            "                \"FOEMInStockJoinQty\": 0.0,\n" +
//            "                \"FInStockPrice\": 0.0,\n" +
//            "                \"FPurBaseQty\": 0.0,\n" +
//            "                \"FDeliveryMinQty\": 5.0,\n" +
//            "                \"FMaterialModel\": null,\n" +
//            "                \"MaterialIsProduce\": null,\n" +
//            "                \"FIsFree\": false,\n" +
//            "                \"FCanReturnQty\": 0.0,\n" +
//            "                \"FStockUnitID.FNumber\": \"Pcs\",\n" +
//            "                \"FCONSIGNSETTBASEQTY\": 0.0,\n" +
//            "                \"FPurReqQty\": 0.0,\n" +
//            "                \"FOrderEntryPlan[0].FDetailLocId.FNumber\": \"BIZ202011101046090\",\n" +
//            "                \"FUnitID.FNumber\": \"Pcs\",\n" +
//            "                \"FDiscountRate\": 0.0,\n" +
//            "                \"FBaseDeliveryMinQty\": 5.0,\n" +
//            "                \"FBaseRemainOutQty\": 5.0,\n" +
//            "                \"OrderEntryPlan[0].PlanUnitId.Name\": \"Pcs\",\n" +
//            "                \"FDiscount\": 0.0,\n" +
//            "                \"FBaseRetNoticeQty\": 0.0,\n" +
//            "                \"FTransJoinQty\": 0.0,\n" +
//            "                \"FNetOrderEntryId\": 0,\n" +
//            "                \"FBaseDeliJoinQty\": 0.0,\n" +
//            "                \"FPromotionMatchType\": \" \",\n" +
//            "                \"FOwnerId.FNumber\": \"000\",\n" +
//            "                \"FReturnQty\": 0.0,\n" +
//            "                \"FTaxAmount_LC\": 11.5,\n" +
//            "                \"FExpPeriod\": 0,\n" +
//            "                \"FDeliveryStatus\": \"A\",\n" +
//            "                \"FDeliveryMaxQty\": 5.0,\n" +
//            "                \"FBefDisAllAmt\": 0.0,\n" +
//            "                \"StockOrgId.Id\": 1,\n" +
//            "                \"FBasePurOrderQty\": 0.0,\n" +
//            "                \"UnitId.Id\": 10101,\n" +
//            "                \"FSrcBillNo\": \" \",\n" +
//            "                \"FDeliveryControl\": false,\n" +
//            "                \"FPriceUnitQty\": 5.0,\n" +
//            "                \"FAPAMOUNT\": 0.0,\n" +
//            "                \"FOrderEntryPlan[0].FPlanDate\": \"2020-11-11T19:35:55.14\",\n" +
//            "                \"OutLmtUnitID.Id\": 10101,\n" +
//            "                \"FBarcode\": \" \",\n" +
//            "                \"FPurPriceUnitId\": null,\n" +
//            "                \"StockOrgId.Name\": \"纷享销客\",\n" +
//            "                \"FDeliQty\": 0.0,\n" +
//            "                \"FRowId\": \"001dd8c7-1cd7-80f3-11eb-24120ecf5701\",\n" +
//            "                \"UnitId.Name\": \"Pcs\",\n" +
//            "                \"FBaseInvoiceJoinQty\": 0.0,\n" +
//            "                \"FBranchId\": null,\n" +
//            "                \"FOrderEntryPlan[0].FDeliCommitQty\": 0.0,\n" +
//            "                \"FTaxNetPrice\": 20.0,\n" +
//            "                \"BaseUnitId.Name\": \"Pcs\",\n" +
//            "                \"FOrderEntryPlan[0].FNOTICEQTY\": 0.0,\n" +
//            "                \"FBasePurReqQty\": 0.0,\n" +
//            "                \"FStockOrgId.FNumber\": \"000\",\n" +
//            "                \"FPriceUnitId.FNumber\": \"Pcs\",\n" +
//            "                \"FCurrentInventory\": 0.0,\n" +
//            "                \"FARAMOUNT\": 0.0,\n" +
//            "                \"FMaterialType\": null,\n" +
//            "                \"FSetPriceUnitID\": null,\n" +
//            "                \"FBaseOEMInStockJoinQty\": 0.0,\n" +
//            "                \"FTaxPrice\": 20.0,\n" +
//            "                \"FBaseUnitId.FNumber\": \"Pcs\"\n" +
//            "            },\n" +
//            "            {\n" +
//            "                \"FTerminaterId\": null,\n" +
//            "                \"BaseUnitId.Id\": 10101,\n" +
//            "                \"FOrderEntryPlan[0].FNOTICEREMAINQTY\": 5.0,\n" +
//            "                \"MaterialId.Id\": 719542,\n" +
//            "                \"StockUnitID.Name\": \"Pcs\",\n" +
//            "                \"FOrderEntryPlan[0].FPlanQty\": 5.0,\n" +
//            "                \"FLOCKQTY\": 0.0,\n" +
//            "                \"FARJOINAMOUNT\": 0.0,\n" +
//            "                \"MaterialId.Name\": \"贝贝产品100\",\n" +
//            "                \"FLeftQty\": 0.0,\n" +
//            "                \"FParentRowId\": \" \",\n" +
//            "                \"FOrderEntryPlan[0].FNOTICEBASEQTY\": 0.0,\n" +
//            "                \"SupplyOrgId.Name\": \"纷享销客\",\n" +
//            "                \"FAmount\": 88.5,\n" +
//            "                \"FStockBaseDen\": 0.0,\n" +
//            "                \"FOrderEntryPlan[0].FNOTICEREMAINBASEQTY\": 5.0,\n" +
//            "                \"FStockBaseTransJoinQty\": 0.0,\n" +
//            "                \"FOrderEntryPlan[0].FBaseDeliRemainQty\": 5.0,\n" +
//            "                \"FMaterialName\": null,\n" +
//            "                \"FStockQty\": 5.0,\n" +
//            "                \"FEntryTaxRate\": null,\n" +
//            "                \"FSrcType\": \" \",\n" +
//            "                \"FTRANSRETURNBASEQTY\": 0.0,\n" +
//            "                \"FBefDisAmt\": 0.0,\n" +
//            "                \"FMapName\": null,\n" +
//            "                \"FAvailableQty\": 0.0,\n" +
//            "                \"FBaseReturnQty\": 0.0,\n" +
//            "                \"FSPMENTRYID\": \" \",\n" +
//            "                \"SupplyOrgId.Id\": 1,\n" +
//            "                \"SettleOrgIds\": null,\n" +
//            "                \"OrderEntryPlan[0].PlanBaseUnitId.Name\": \"Pcs\",\n" +
//            "                \"FBaseUnitQty\": 5.0,\n" +
//            "                \"FRetailSaleProm\": false,\n" +
//            "                \"ReceiptOrgId.Name\": \"纷享销客\",\n" +
//            "                \"FPurQty\": 0.0,\n" +
//            "                \"FSOStockLocalId\": null,\n" +
//            "                \"FBASEAPQTY\": 0.0,\n" +
//            "                \"FAuxPropId\": null,\n" +
//            "                \"FPurUnitID\": null,\n" +
//            "                \"FMaterialId.FNumber\": \"CH4470\",\n" +
//            "                \"FOrderEntryPlan[0].FDetailLocAddress\": \"000\",\n" +
//            "                \"FRetNoticeQty\": 0.0,\n" +
//            "                \"FServiceContext\": null,\n" +
//            "                \"OrderEntryPlan[0].PlanBaseUnitId.Id\": 10101,\n" +
//            "                \"PriceUnitId.Id\": 10101,\n" +
//            "                \"FTransportLeadTime1\": 0,\n" +
//            "                \"FTaxCombination\": null,\n" +
//            "                \"FInvoiceQty\": 0.0,\n" +
//            "                \"FCanOutQty\": 5.0,\n" +
//            "                \"FChangeFlag\": \" \",\n" +
//            "                \"OwnerId.Name\": \"纷享销客\",\n" +
//            "                \"FBFLowId\": null,\n" +
//            "                \"FReceiptOrgId.FNumber\": \"000\",\n" +
//            "                \"FFreezeDate\": null,\n" +
//            "                \"FStockOutQty\": 0.0,\n" +
//            "                \"FEntryDiscountList\": null,\n" +
//            "                \"FIsReturn\": null,\n" +
//            "                \"FMrpTerminateStatus\": \"A\",\n" +
//            "                \"FLOCKFLAG\": false,\n" +
//            "                \"FBaseDeliQty\": 0.0,\n" +
//            "                \"FZHJStockQty\": 0.0,\n" +
//            "                \"FReturnType\": \" \",\n" +
//            "                \"FRowType\": \"Service\",\n" +
//            "                \"FSRCBIZUNITID\": null,\n" +
//            "                \"FBaseARJoinQty\": 0.0,\n" +
//            "                \"FSysPrice\": 0.0,\n" +
//            "                \"FSalBaseARJoinQty\": 0.0,\n" +
//            "                \"FOrderEntryPlan[0].FPlanUnitId.FNumber\": \"Pcs\",\n" +
//            "                \"FEntryTaxAmount\": null,\n" +
//            "                \"FInvoiceAmount\": 0.0,\n" +
//            "                \"FAwaitQty\": 0.0,\n" +
//            "                \"FTerminateDate\": null,\n" +
//            "                \"FParentMatId\": null,\n" +
//            "                \"FOrderEntryPlan[0].FBasePlanQty\": 5.0,\n" +
//            "                \"FTRANSRETURNQTY\": 0.0,\n" +
//            "                \"OrderEntryPlan[0].DetailLocId.Id\": 100248,\n" +
//            "                \"FReceiveAmount\": 0.0,\n" +
//            "                \"FOUTLMTUNIT\": \"SAL\",\n" +
//            "                \"FRemainOutQty\": 5.0,\n" +
//            "                \"FStockBaseARJoinQty\": 0.0,\n" +
//            "                \"FSupplyOrgId.FNumber\": \"000\",\n" +
//            "                \"FBaseReBackQty\": 0.0,\n" +
//            "                \"FExpUnit\": \" \",\n" +
//            "                \"FReBackQty\": 0.0,\n" +
//            "                \"FPrice\": 17.699115,\n" +
//            "                \"FMaterialIsSubContract\": null,\n" +
//            "                \"FQty\": 5.0,\n" +
//            "                \"FProduceDate\": null,\n" +
//            "                \"FStockBaseCanReturnQty\": 0.0,\n" +
//            "                \"FFreezerId\": null,\n" +
//            "                \"FInventoryQty\": 0.0,\n" +
//            "                \"FSalBaseNum\": 0.0,\n" +
//            "                \"FCONSIGNSETTQTY\": 0.0,\n" +
//            "                \"FBasePurJoinQty\": 0.0,\n" +
//            "                \"FOrderEntryPlan[0].FDeliRemainQty\": 5.0,\n" +
//            "                \"FPurJoinQty\": 0.0,\n" +
//            "                \"FBaseTransJoinQty\": 0.0,\n" +
//            "                \"FISMRP\": false,\n" +
//            "                \"FBaseStockOutQty\": 0.0,\n" +
//            "                \"FExpiryDate\": null,\n" +
//            "                \"OutLmtUnitID.Name\": \"Pcs\",\n" +
//            "                \"FInvoiceJoinQty\": 0.0,\n" +
//            "                \"FBASEFINARQTY\": 0.0,\n" +
//            "                \"OwnerId.Id\": 1,\n" +
//            "                \"ReceiptOrgId.Id\": 1,\n" +
//            "                \"OrderEntryPlan[0].PlanUnitId.Id\": 10101,\n" +
//            "                \"FOrderEntryPlan[0].FBaseDeliCommitQty\": 0.0,\n" +
//            "                \"FOutLmtUnitID.FNumber\": \"Pcs\",\n" +
//            "                \"StockUnitID.Id\": 10101,\n" +
//            "                \"FAllAmount_LC\": 100.0,\n" +
//            "                \"FIsInventory\": null,\n" +
//            "                \"FOrderEntryPlan[0].FPlanDeliveryDate\": \"2020-11-11T19:35:55.157\",\n" +
//            "                \"FPriceListEntry\": null,\n" +
//            "                \"FSOStockId\": null,\n" +
//            "                \"FMinPlanDeliveryDate\": \"2020-11-11T19:35:55.157\",\n" +
//            "                \"FMrpCloseStatus\": \"A\",\n" +
//            "                \"FPriceCoefficient\": 1.0,\n" +
//            "                \"FMrpFreezeStatus\": \"A\",\n" +
//            "                \"FPriceBaseQty\": 5.0,\n" +
//            "                \"FStockBaseCanOutQty\": 5.0,\n" +
//            "                \"FMtoNo\": \" \",\n" +
//            "                \"PriceUnitId.Name\": \"Pcs\",\n" +
//            "                \"FAllAmount\": 100.0,\n" +
//            "                \"FBASEARQTY\": 0.0,\n" +
//            "                \"FLot\": null,\n" +
//            "                \"FStockBasePurJoinQty\": 0.0,\n" +
//            "                \"FAmount_LC\": 88.5,\n" +
//            "                \"FReserveType\": \"1\",\n" +
//            "                \"FPriceDiscount\": 0.0,\n" +
//            "                \"FPurOrderQty\": 0.0,\n" +
//            "                \"FSTOCKBASEREBACKQTY\": 0.0,\n" +
//            "                \"FBaseCanOutQty\": 5.0,\n" +
//            "                \"FARQTY\": 0.0,\n" +
//            "                \"FPriority\": 0,\n" +
//            "                \"FDeliveryDate\": \"2020-11-11T19:35:55.157\",\n" +
//            "                \"FOldQty\": 5.0,\n" +
//            "                \"FSPMANDRPMCONTENT\": \" \",\n" +
//            "                \"FBomId\": null,\n" +
//            "                \"FOwnerTypeId\": \"BD_OwnerOrg\",\n" +
//            "                \"FBaseCanReturnQty\": 0.0,\n" +
//            "                \"FOrderEntryPlan[0].FTransportLeadTime\": 0,\n" +
//            "                \"FOrderEntryPlan[0].FStockId\": null,\n" +
//            "                \"FSALBASEFINARQTY\": 0.0,\n" +
//            "                \"FMapId\": null,\n" +
//            "                \"FEntryNote\": \"111\",\n" +
//            "                \"FSTOCKBASESTOCKOUTQTY\": 0.0,\n" +
//            "                \"FBaseDeliveryMaxQty\": 5.0,\n" +
//            "                \"FLimitDownPrice\": 0.0,\n" +
//            "                \"FOrderEntryPlan[0].FPlanBaseUnitId.FNumber\": \"Pcs\",\n" +
//            "                \"FStockBaseQty\": 5.0,\n" +
//            "                \"FOEMInStockJoinQty\": 0.0,\n" +
//            "                \"FInStockPrice\": 0.0,\n" +
//            "                \"FPurBaseQty\": 0.0,\n" +
//            "                \"FDeliveryMinQty\": 5.0,\n" +
//            "                \"FMaterialModel\": null,\n" +
//            "                \"MaterialIsProduce\": null,\n" +
//            "                \"FIsFree\": false,\n" +
//            "                \"FCanReturnQty\": 0.0,\n" +
//            "                \"FStockUnitID.FNumber\": \"Pcs\",\n" +
//            "                \"FCONSIGNSETTBASEQTY\": 0.0,\n" +
//            "                \"FPurReqQty\": 0.0,\n" +
//            "                \"FOrderEntryPlan[0].FDetailLocId.FNumber\": \"BIZ202011101046090\",\n" +
//            "                \"FUnitID.FNumber\": \"Pcs\",\n" +
//            "                \"FDiscountRate\": 0.0,\n" +
//            "                \"FBaseDeliveryMinQty\": 5.0,\n" +
//            "                \"FBaseRemainOutQty\": 5.0,\n" +
//            "                \"OrderEntryPlan[0].PlanUnitId.Name\": \"Pcs\",\n" +
//            "                \"FDiscount\": 0.0,\n" +
//            "                \"FBaseRetNoticeQty\": 0.0,\n" +
//            "                \"FTransJoinQty\": 0.0,\n" +
//            "                \"FNetOrderEntryId\": 0,\n" +
//            "                \"FBaseDeliJoinQty\": 0.0,\n" +
//            "                \"FPromotionMatchType\": \" \",\n" +
//            "                \"FOwnerId.FNumber\": \"000\",\n" +
//            "                \"FReturnQty\": 0.0,\n" +
//            "                \"FTaxAmount_LC\": 11.5,\n" +
//            "                \"FExpPeriod\": 0,\n" +
//            "                \"FDeliveryStatus\": \"A\",\n" +
//            "                \"FDeliveryMaxQty\": 5.0,\n" +
//            "                \"FBefDisAllAmt\": 0.0,\n" +
//            "                \"StockOrgId.Id\": 1,\n" +
//            "                \"FBasePurOrderQty\": 0.0,\n" +
//            "                \"UnitId.Id\": 10101,\n" +
//            "                \"FSrcBillNo\": \" \",\n" +
//            "                \"FDeliveryControl\": false,\n" +
//            "                \"FPriceUnitQty\": 5.0,\n" +
//            "                \"FAPAMOUNT\": 0.0,\n" +
//            "                \"FOrderEntryPlan[0].FPlanDate\": \"2020-11-11T19:35:55.157\",\n" +
//            "                \"OutLmtUnitID.Id\": 10101,\n" +
//            "                \"FBarcode\": \" \",\n" +
//            "                \"FPurPriceUnitId\": null,\n" +
//            "                \"StockOrgId.Name\": \"纷享销客\",\n" +
//            "                \"FDeliQty\": 0.0,\n" +
//            "                \"FRowId\": \"001dd8c7-1cd7-80f3-11eb-24120ecf5702\",\n" +
//            "                \"UnitId.Name\": \"Pcs\",\n" +
//            "                \"FBaseInvoiceJoinQty\": 0.0,\n" +
//            "                \"FBranchId\": null,\n" +
//            "                \"FOrderEntryPlan[0].FDeliCommitQty\": 0.0,\n" +
//            "                \"FTaxNetPrice\": 20.0,\n" +
//            "                \"BaseUnitId.Name\": \"Pcs\",\n" +
//            "                \"FOrderEntryPlan[0].FNOTICEQTY\": 0.0,\n" +
//            "                \"FBasePurReqQty\": 0.0,\n" +
//            "                \"FStockOrgId.FNumber\": \"000\",\n" +
//            "                \"FPriceUnitId.FNumber\": \"Pcs\",\n" +
//            "                \"FCurrentInventory\": 0.0,\n" +
//            "                \"FARAMOUNT\": 0.0,\n" +
//            "                \"FMaterialType\": null,\n" +
//            "                \"FSetPriceUnitID\": null,\n" +
//            "                \"FBaseOEMInStockJoinQty\": 0.0,\n" +
//            "                \"FTaxPrice\": 20.0,\n" +
//            "                \"FBaseUnitId.FNumber\": \"Pcs\"\n" +
//            "            }\n" +
//            "        ]\n" +
//            "    }\n" +
//            "}"
//}
