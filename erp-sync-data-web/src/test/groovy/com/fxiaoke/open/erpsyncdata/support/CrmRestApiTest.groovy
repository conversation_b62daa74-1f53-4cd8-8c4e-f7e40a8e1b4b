//package com.fxiaoke.open.erpsyncdata.support
//
//import com.fxiaoke.crmrestapi.arg.ActionAddArg
//import com.fxiaoke.crmrestapi.common.contants.CrmConstants
//import com.fxiaoke.crmrestapi.common.data.HeaderObj
//import com.fxiaoke.crmrestapi.common.data.ObjectData
//import com.fxiaoke.crmrestapi.common.result.Result
//import com.fxiaoke.crmrestapi.result.ActionAddResult
//import com.fxiaoke.crmrestapi.service.MetadataActionService
//import com.fxiaoke.open.erpsyncdata.BaseSpockTest
//import groovy.util.logging.Slf4j
//import org.junit.Ignore
//import org.junit.Test
//import org.springframework.beans.factory.annotation.Autowired
//
///**
// *
// * <AUTHOR> (^_−)☆
// * @date 2020/9/25
// */
//@Ignore
//@Slf4j
//class CrmRestApiTest extends BaseSpockTest {
//
//    @Autowired
//    private MetadataActionService metadataActionService
//
//    @Test
//    void addBatchStock() {
//        HeaderObj headerObj = new HeaderObj(81961, CrmConstants.SYSTEM_USER);
//        ActionAddArg addArg = new ActionAddArg()
//        ObjectData data = new ObjectData()
////        data.put("product_id","5f60a7c277d7900001b3b370")
////        data.put("warehouse_id","5f6adf5477d7900001be2536")
////        data.put("batch_id","20200925")
////        data.put("batch_real_stock",10)
//
//        data.put("product_id","6041e4d6d215ff000161e541")
//        data.put("warehouse_id","6040d7c0d215ff00015ef175")
//        data.put("batch_id","6042095ad215ff000162341c")
//        data.put("batch_real_stock",100)
//        data.setOwner(1000)
//        addArg.setObjectData(data)
//        Result<ActionAddResult> result = metadataActionService.add(headerObj, "BatchStockObj",false,false, addArg);
//        log.info("result: {}", result);
//    }
//
//    @Test
//    void addBatch() {
//        HeaderObj headerObj = new HeaderObj(79558, CrmConstants.SYSTEM_USER);
//        ActionAddArg addArg = new ActionAddArg()
//        ObjectData data = new ObjectData()
//        data.setName("20200927-xjybatch001")
//        data.put("product_id","5f70293683ecb900019badc2")
//        data.setOwner(1000)
//        addArg.setObjectData(data)
//        Result<ActionAddResult> result = metadataActionService.add(headerObj, "BatchObj",false,false, addArg);
//        log.info("result: {}", result);
//    }
//
//    @Test
//    void addWarehouse() {
//        HeaderObj headerObj = new HeaderObj(79558, CrmConstants.SYSTEM_USER);
//        ActionAddArg addArg = new ActionAddArg()
//        ObjectData data = new ObjectData()
//        data.setName("深圳大冲仓001")
//        data.put("number","dachong001")
//        data.put("is_default",true)
//        data.put("is_enable",1)
//        data.setOwner(1000)
//        addArg.setObjectData(data)
//        Result<ActionAddResult> result = metadataActionService.add(headerObj, "WarehouseObj",false,false, addArg);
//        log.info("result: {}", result);
//    }
//}
