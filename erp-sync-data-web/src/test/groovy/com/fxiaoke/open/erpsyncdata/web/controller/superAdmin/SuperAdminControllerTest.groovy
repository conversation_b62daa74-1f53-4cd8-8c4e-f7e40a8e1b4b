package com.fxiaoke.open.erpsyncdata.web.controller.superAdmin

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.UserOperatorLogDao
import spock.lang.Specification

class SuperAdminControllerTest extends Specification {

    SuperAdminController controller

    def "test operationQuery"() {
        given:
        UserOperatorLogDao userOperatorLogDao = Mock(UserOperatorLogDao)
        controller = new SuperAdminController(userOperatorLogDao: userOperatorLogDao)
        when:
        def res = controller.operationQuery("test")
        println(res)
        then:
        noExceptionThrown()
    }
}
