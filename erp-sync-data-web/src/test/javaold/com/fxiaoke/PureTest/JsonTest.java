package com.fxiaoke.PureTest;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

public class JsonTest {

    public static void fastJsonTest() {
    }

    public static void main(String[] args) {

        String  str = "";
        Map<String, Object> map = new HashMap<>();
        map.put("name", "wubb");
        map.put("sex", "male");
        String bigStr = "0.";
        for (int i = 0; i < 15; i++) {
            bigStr += "0";
            map.put("bigNumber", new BigDecimal(bigStr));
            System.out.printf("with %s 0%n", i+1);
            System.out.println(JacksonUtil.toJson(map));
            System.out.println(JSON.toJSONString(map));
        }
    }
}
