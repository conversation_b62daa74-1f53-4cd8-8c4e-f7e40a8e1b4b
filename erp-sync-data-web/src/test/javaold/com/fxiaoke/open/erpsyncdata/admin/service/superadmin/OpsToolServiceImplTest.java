package com.fxiaoke.open.erpsyncdata.admin.service.superadmin;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.model.SendMsgHelper;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendTextNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/4/25
 */
@Ignore
@Slf4j
public class OpsToolServiceImplTest extends BaseTest {
    @Autowired
    private OpsToolServiceImpl opsToolService;

    @Test
    public void asyncSyncAllNoTriggerErpTemp() {
        SendMsgHelper sendMsgHelper = new SendMsgHelper();
        SendTextNoticeArg sendTextNoticeArg = new SendTextNoticeArg();
        sendTextNoticeArg.setTenantId("84307");
        sendTextNoticeArg.setDataCenterId("");
        sendTextNoticeArg.setReceivers(Collections.singletonList(1000));
        sendTextNoticeArg.setMsgTitle("重新同步临时库数据");
        sendMsgHelper.setSendTextNoticeArg(sendTextNoticeArg);
        Result<Void> res = opsToolService.syncAllNoTriggerErpTemp("84307", "764537083710275584", "push_main", null, "62418971cb696470549045fe", sendMsgHelper);
        log.info("sync erp temp result:{}", res);
    }
}