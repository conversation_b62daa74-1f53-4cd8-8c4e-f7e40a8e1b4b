package com.fxiaoke.open.erpsyncdata.admin.service.superadmin;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.arg.CopyBetweenDbArg;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date: 11:00 2022/5/23
 * @Desc:
 */
@Ignore
public class SuperAdminBrushDataServiceImplTest extends BaseTest {
    @Autowired
    private SuperAdminBrushDataService superAdminBrushDataService;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Test
    public void cutDownByTenantId() {
    }

    @Test
    public void cutDownByTenantIds() {

    }

    @Test
    public void brushTableData() {
        Result<String> result=superAdminBrushDataService.brushTableData(Lists.newArrayList("82325"),"-10002");
        System.out.println("");
    }

    @Test
    public void brushDispatcherTime() {
        Result<String> result=superAdminBrushDataService.brushDispatcherTime(Lists.newArrayList("81243"),1000*60*60,null);
        System.out.println("");
    }
    @Test
    public void brushCopyBetweenDb() {
        CopyBetweenDbArg arg=new CopyBetweenDbArg();
        arg.setDeleteDest(true);
        arg.setSourceDbRoute("-10001");
        arg.setDestDbRoute("-10001");
        arg.setTenantId("1234");
        arg.setTableNames(Lists.newArrayList("erp_connect_info","erp_object","erp_object_relationship","erp_object_field","erp_field_extend","erp_obj_groovy","erp_field_data_mapping","sync_ploy_detail","erp_sync_time","erp_tenant_configuration"));
        Result<String> result=superAdminBrushDataService.copyBetweenDb(arg);
        System.out.println("");
    }
    @Test
    public void getTenantIdRoute() {
        Result<Map<String, Set<String>>> tenantIdRoute = superAdminBrushDataService.getTenantIdRoute(null,"-10001");
        System.out.println("");
    }

}