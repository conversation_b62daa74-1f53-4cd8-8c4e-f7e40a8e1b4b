package com.fxiaoke.open.erpsyncdata.admin.service.superadmin;


import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.ViewResult;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.SuperAdminReplaceViewService;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ListErpObjDataResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 14:03 2021/11/4
 * @Desc:
 */
@Ignore
public class SuperAdminReplaceViewServiceImplTest extends BaseTest {

    @Autowired
    private SuperAdminReplaceViewService superAdminReplaceViewService;
    @Test
    public void useView() {
        superAdminReplaceViewService.useView("82777");
    }

    @Test
    public void brushQueryCode() {
        Result<String> result = superAdminReplaceViewService.brushQueryCode("82777", "696453487420604416", K3CloudForm.BD_Customer);
        System.out.println("");
    }

    @Test
    public void getByExecuteBillQuery() {
        Result<ViewResult> byExecuteBillQuery = superAdminReplaceViewService.getByExecuteBillQuery("82777", "696453487420604416", K3CloudForm.BD_Customer, "CUST00286");
        System.out.println("");
    }
    @Test
    public void compareViewAndBillQueryResult() {
        Result<Object> result = superAdminReplaceViewService.compareViewAndBillQueryResult("82777", "696453487420604416", K3CloudForm.SAL_SaleOrder, "XSDD001145");
        System.out.println("");
    }
    @Test
    public void executeBillQuery() {
        Result<List<Result<ListErpObjDataResult>>>  result = superAdminReplaceViewService.executeBillQuery("81243");
        System.out.println("");
    }
}