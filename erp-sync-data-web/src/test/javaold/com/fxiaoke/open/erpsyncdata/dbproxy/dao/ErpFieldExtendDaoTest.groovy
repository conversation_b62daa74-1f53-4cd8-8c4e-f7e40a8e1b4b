package com.fxiaoke.open.erpsyncdata.dbproxy.dao

import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldExtendEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil
import groovy.util.logging.Slf4j
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/11/4
 */
@Ignore
@Slf4j
class ErpFieldExtendDaoTest extends BaseSpockTest {
    @Autowired
    private ErpFieldExtendDao erpFieldExtendDao
    @Autowired
    private IdGenerator idGenerator

    @Test
    public void insert() {
        ErpFieldExtendEntity erpFieldExtendEntity = new ErpFieldExtendEntity();
        erpFieldExtendEntity.setId(idGenerator.get())
        erpFieldExtendEntity.setTenantId("k30")
        erpFieldExtendEntity.setObjApiName("Account")
        erpFieldExtendEntity.setFieldApiName("name")
        erpFieldExtendEntity.setSaveCode("FName")
        erpFieldExtendEntity.setViewCode("Name")
        Long time = System.currentTimeMillis()
        erpFieldExtendEntity.setCreateTime(time)
        erpFieldExtendEntity.setUpdateTime(time)
        def insert = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("k30")).insert(erpFieldExtendEntity)
        log.info("result:{}",insert)
    }

    @Test
    public void query() {
        def fields = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("84801"))
                .queryByObjApiName("84801","STK_TransferDirect")
        log.info("{}",fields)
    }
}
