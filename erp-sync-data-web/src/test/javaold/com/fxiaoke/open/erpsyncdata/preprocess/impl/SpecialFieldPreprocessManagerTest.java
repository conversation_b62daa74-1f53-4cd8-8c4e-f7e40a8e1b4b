package com.fxiaoke.open.erpsyncdata.preprocess.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.SpecialFieldPreprocessManager;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjDataResult;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.DoWriteMqData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 16:37 2020/8/26
 * @Desc:
 */
@Ignore
@Slf4j
public class SpecialFieldPreprocessManagerTest extends BaseTest {

    @Autowired
    private SpecialFieldPreprocessManager specialFieldPreprocessManager;
    @Autowired
    private com.fxiaoke.open.erpsyncdata.preprocess.manager.SpecialFieldPreprocessManager specialFieldPreprocessService;

    @Test
    public void convertErpFieldValue2Crm() {
        SyncDataContextEvent erpObjDataVO = getData();
        List<SyncDataContextEvent> erpObjDataVOS = specialFieldPreprocessManager.convertErpFieldValue2CrmList("79675","",null, Lists.newArrayList(erpObjDataVO));
        log.info("result={}", erpObjDataVOS);
    }

    @Test
    public void convertErp2Crm() {
        SyncDataContextEvent doWriteMqData = JacksonUtil.fromJson(objDataStr, SyncDataContextEvent.class);
        specialFieldPreprocessManager.convertCrmFieldValue2Erp("81138","", doWriteMqData);
    }

    String objDataStr = "{\"destData\":{\"tenant_id\":\"81138\",\"FSaleOrgId.FNumber\":\"000\",\"FBillTypeID.FNumber\":\"XSDD01_SYS\",\"object_describe_api_name\":\"SAL_SaleOrder.BillHead\",\"FSalerId.FNumber\":[\"88888\"],\"FCustId.FNumber\":\"CUST3476\",\"_id\":\"5fc465a6facb5e0001659040\",\"FDate\":1606704425154},\"destDataId\":\"5fc465a6facb5e0001659040\",\"destDetailSyncDataIdAndDestDataMap\":{\"6f4197f877c347f49723ee4a55ab2c52\":{\"tenant_id\":\"81138\",\"owner\":[],\"FQty\":\"22.00\",\"fake_master_detail\":\"5fc465a6facb5e0001659040\",\"FMaterialId.FNumber\":\"CH4139\",\"object_describe_api_name\":\"SAL_SaleOrder.SaleOrderEntry\",\"SettleOrgIds\":\"000\",\"_id\":\"5fc465a6facb5e0001659041\",\"created_by\":[]}},\"destEventType\":1,\"destObjectApiName\":\"SAL_SaleOrder.BillHead\",\"destTenantId\":\"81138\",\"destTenantType\":2,\"sourceTenantId\":\"81138\",\"syncDataId\":\"72cb7b7e5c97457e8d7efc829af149ad\",\"syncPloyDetailSnapshotId\":\"87a728e3bc494f0ab8eafcc1d13cd796\"}";

    public SyncDataContextEvent getData() {
        SyncDataContextEvent erpObjDataVO = new SyncDataContextEvent();
        erpObjDataVO.setSourceEventType(1);
        erpObjDataVO.setDetailData(Maps.newHashMap());
        for (int i = 1; i < 4; i++) {
            ObjectData objectData = new ObjectData();
            for (int j = 1; j < 5; j++) {
                objectData.put("field" + i + j, "value" + i + j);
            }
            if (i == 1) {
                String obj = "objApiName" + i;
                objectData.putApiName(obj);
                erpObjDataVO.setSourceData(objectData);
            } else {
                String detailObj = "detailObjApiName" + i;
                objectData.putApiName(detailObj);
                erpObjDataVO.getDetailData().put(detailObj, Lists.newArrayList(objectData));
            }
        }
        return erpObjDataVO;
    }

    @Test
    public void CacheTest() {
        StopWatch stopWatch = new StopWatch("test Cache");
        for (int i = 0; i < 10; i++) {
            stopWatch.start(String.format("%s time", i));
            Map<String, ErpObjectFieldEntity> erpObjFieldMap = specialFieldPreprocessService.getErpObjFieldMap("81138","", "BD_Customer.BillHead");
            stopWatch.stop();
        }
        System.out.println(stopWatch.prettyPrint());

    }
}