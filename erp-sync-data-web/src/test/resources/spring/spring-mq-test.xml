<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd">

    <import resource="classpath:spring/web-common.xml"/>
<!--    <import resource="classpath:spring/all-rest-api.xml"/>-->
    <import resource="classpath:spring/web-fileserver.xml"/>
    <import resource="classpath:spring/all-dubbo-provider-test.xml"/>
    <import resource="classpath:spring/web-dubbo-consumer.xml"/>
    <import resource="classpath:spring/all-mq-consumer.xml"/>
    <import resource="classpath:spring/web-mq-producer.xml"/>
    <import resource="classpath*:spring/datacenter-applicationContext.xml"/>
    <import resource="classpath*:spring/syncconverter-context.xml"/>
    <import resource="classpath*:spring/syncwriter-context.xml"/>
    <import resource="classpath*:spring/fseventtrigger-applicationContext.xml"/>
    <import resource="classpath*:spring/common-db-proxy.xml"/>
    <import resource="classpath*:spring/admin-context.xml"/>
    <import resource="classpath*:spring/erp-preprocess-data.xml"/>
    <import resource="classpath*:spring/erp-apiproxy-data.xml"/>
    <import resource="classpath*:spring/oa-sync-data.xml"/>
</beans>