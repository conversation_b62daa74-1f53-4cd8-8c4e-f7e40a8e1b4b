package com.fxiaoke.open.oasyncdata.constant;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2020/7/14.
 */
public interface CustomFunctionConstant {
    /** 自定义函数没有绑定对象传NONE */
    String BINDING_OBJECT_API_NAME =  "NONE";
    /** 自定义函数命名空间 */
    String NAME_SPACE = "sync_data";
    /** 是否执行，value为false为不执行同步 */
    String IS_EXEC = "isExec";
    /** 是否覆盖原自定义函数 */
    String IS_COVER = "isCover";
    /** 自定义函数入参名的值 */
    String SYNC_ARG_NAME = "syncArg";
    /** 自定义函数参数入参类型值Map */
    String SYNC_ARG_TYPE_MAP = "Map";
}
