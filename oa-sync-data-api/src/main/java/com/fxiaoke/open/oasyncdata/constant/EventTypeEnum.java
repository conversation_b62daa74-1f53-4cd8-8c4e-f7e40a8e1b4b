package com.fxiaoke.open.oasyncdata.constant;


import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum EventTypeEnum {
    ADD("1", "新增", I18NStringEnum.s652.getI18nKey()),
    UPDATE("2", "更新", I18NStringEnum.s364.getI18nKey()),
    INVALID("3", "作废", I18NStringEnum.s365.getI18nKey()),
    SYNC_MENU("4", "手动同步", I18NStringEnum.s675.getI18nKey()),

    ;
    private String type;
    private String name;
    private String i18nKey;

    public static String getNameByType(I18NStringManager i18NStringManager, String lang, String tenantId, String type) {
        for (EventTypeEnum eventType : EventTypeEnum.values()) {
            if (eventType.getType() == type) {
                return i18NStringManager.get(eventType.getI18nKey(),lang,tenantId,eventType.getName());
            }
        }
        return null;
    }

    public static String getNameByEiAndType(I18NStringManager i18NStringManager,String ei, String type) {
        for (EventTypeEnum eventType : EventTypeEnum.values()) {
            if (eventType.getType() == type) {
                return i18NStringManager.getByEi(eventType.getI18nKey(),ei,eventType.getName());
            }
        }
        return null;
    }

    public boolean match(String otherType) {
        return otherType != null && this.type .equals(otherType) ;
    }
}