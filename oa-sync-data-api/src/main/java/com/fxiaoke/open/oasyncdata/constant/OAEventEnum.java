package com.fxiaoke.open.oasyncdata.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * Created by liuyc
 */
@Getter
@ToString
@AllArgsConstructor
public enum OAEventEnum {
    CREATE("Create","1"),
    DEAL("Deal","2"),
    DELETE("Delete","3"),
    ;

    /**
     * 对象名称
     */
    private final String eventType;
    /**
     * 对象ApiName
     */
    private final String eventStatus;
}
