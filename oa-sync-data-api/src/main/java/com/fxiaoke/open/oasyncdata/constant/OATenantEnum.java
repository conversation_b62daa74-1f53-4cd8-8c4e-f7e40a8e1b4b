package com.fxiaoke.open.oasyncdata.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.arg.AutoBindArg;
import com.fxiaoke.open.oasyncdata.arg.SyncRuleArg;
import com.fxiaoke.open.oasyncdata.model.OAMessageResyncRule;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * crm对象
 */
@Getter
@ToString
@AllArgsConstructor
public enum OATenantEnum {
    OA_AUTO_BIND_FIELD("自动绑定oa账号的字段", I18NStringEnum.s1213.getI18nKey(), AutoBindArg.class ),

    OA_RECORD_NOT_BIND_ACCOUNT("没有绑定账号的时候，是否需要同步", I18NStringEnum.s1214.getI18nKey(), SyncRuleArg.class ),

    OA_SUPPORT_ERROR_LOG("设置的企业，错误请求失败的日志不上报", I18NStringEnum.s2306.getI18nKey(), SyncRuleArg.class ),

    OA_SUPPORT_CUSTOM_DOMAIN("支持自定义的域名", I18NStringEnum.s2307.getI18nKey(), SyncRuleArg.class ),

    OA_RETRY_CRON_EXPRESSION("支持自定义的重试的时间", I18NStringEnum.s5004.getI18nKey(), SyncRuleArg.class ),

    OA_SETTING_RETRY_MESSAGE("支持前端重试时间", I18NStringEnum.s5005.getI18nKey(), OAMessageResyncRule.class ),


    ;
    /**
     * oa配置状态描述
     */
    private final String desc;
    private final String i18nKey;
    /**
     * oa配置状态
     */
    private final Class<?> configType;

    public String getDesc(I18NStringManager i18NStringManager, String lang, String tenantId) {
        return i18NStringManager.get(i18nKey,lang,tenantId,desc);
    }
}
