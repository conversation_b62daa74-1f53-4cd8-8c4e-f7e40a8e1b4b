package com.fxiaoke.open.oasyncdata.result;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 16:35 2021/8/11
 * @Desc:
 */
@Data
@ApiModel
public class ErpInterfaceMonitorResult implements Serializable {
    /**
     * 数据id
     */
    @ApiModelProperty("数据id")
    public String id;

    /**
     * 企业id
     */
    @ApiModelProperty("企业id")
    private String tenantId;

    /**
     * 对象apiName
     */
    @ApiModelProperty("对象apiName")
    private String objApiName;

    /**
     * 接口类型
     */
    @ApiModelProperty("接口类型")
    private String type;

    /**
     * 入参
     */
    @ApiModelProperty("入参")
    private String arg;

    /**
     * 结果数据
     */
    @ApiModelProperty("结果数据")
    private String result;

    /**
     * 调用状态 目前默认只有1：成功
     */
    @ApiModelProperty("调用状态")
    private Integer status;

    /**
     * 调用状态描述 目前默认只有成功
     */
    @ApiModelProperty("调用状态描述")
    private String statusDesc="成功";   // ignoreI18n     不会使用

    /**
     * 调用时间
     */
    @ApiModelProperty("调用时间")
    private Long callTime;

    /**
     * 返回时间
     */
    @ApiModelProperty("返回时间")
    private Long returnTime;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * traceId
     */
    @ApiModelProperty("traceId")
    private String traceId;

    /**
     * 花费时间
     */
    @ApiModelProperty("花费时间")
    private Long costTime;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Long createTime;

    /**
     * 过期时间
     */
    @ApiModelProperty("过期时间")
    private Date expireTime;

    /**
     * 数据是否大于2M
     */
    @ApiModelProperty("数据是否大于2M")
    private Boolean dataLarge2M=false;

    /**
     * 数据量
     */
    @ApiModelProperty("数据量")
    private Integer dataCount;

    /**
     * 开始时间
     */
    private Long startTime;
    /**
     * 结束时间
     */
    private Long endTime;

    /**
     * 查询时间段
     */
    private String queryTimePeriod;
    /**
     * 限制偏移量
     * 返回格式如 limit 100 offset 0
     */
    private String limit;
    /**
     * 接口名称
     */
    public String interfaceName;
    /**
     * 接口路径
     */
    public String interfaceUrl;
}
