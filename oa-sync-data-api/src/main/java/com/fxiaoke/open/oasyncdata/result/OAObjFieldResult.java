package com.fxiaoke.open.oasyncdata.result;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * oa字段信息
 *
 * <AUTHOR>
 * @date 2021/3/16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor

public class OAObjFieldResult implements Serializable {
    private static final long serialVersionUID = 3739723344368834569L;


    private String id;

    /**
     * 企业id
     */

    private String tenantId;

    /**
     * 对象名称
     */

    private String objApiName;


    /**
     * 标签
     */

    private String label;

    /**
     * 字段ApiName
     */

    private String fieldApiName;

    /**
     * 占位符
     */

    private String replaceName;

    /**
     * 创建时间
     */

    private Long createTime;

    /**
     * 修改时间
     */

    private Long updateTime;

}