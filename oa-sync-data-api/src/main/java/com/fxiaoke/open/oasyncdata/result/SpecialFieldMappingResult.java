package com.fxiaoke.open.oasyncdata.result;


import com.fxiaoke.open.oasyncdata.constant.ErpChannelEnum;
import com.fxiaoke.open.oasyncdata.constant.ErpFieldTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 10:35 2020/8/18
 * @Desc:
 */
@Data
@ApiModel
@AllArgsConstructor
@NoArgsConstructor
public class SpecialFieldMappingResult implements Serializable {
    @ApiModelProperty("数据id")
    public String id; //
//    @ApiModelProperty("企业id")
//    public int tenantId; //
    @ApiModelProperty("渠道，ERP_K3CLOUD,ERP_SAP,ERP_U8")
    public ErpChannelEnum channel; //渠道，k3,sap,u8，其他
    @ApiModelProperty("基础数据类型,部门->department,员工->employee,国家->country,省->province,市->city,区->district...")
    public ErpFieldTypeEnum dataType ;
    @ApiModelProperty("fs基础数据id")
    public String fsDataId ;
    @ApiModelProperty("fs基础数据name")
    public String fsDataName;
    @ApiModelProperty("erp基础数据id")
    public String erpDataId ;
    @ApiModelProperty("erp基础数据name")
    public String erpDataName;
    @ApiModelProperty("操作类型：add,update,delete")
    public String operateType;
    @ApiModelProperty("crm产品分类id")
    public String fsCategoryId;

}
