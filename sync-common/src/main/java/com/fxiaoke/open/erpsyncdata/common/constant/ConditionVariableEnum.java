package com.fxiaoke.open.erpsyncdata.common.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import lombok.Getter;

@Getter
public enum ConditionVariableEnum {
    REFERENCE_ACCOUNT(SyncPloyTypeEnum.OUTPUT.getType(),
            "object_reference_accountobj",
            "关联客户",
            I18NStringEnum.s1009.getI18nKey()),
    REFERENCE_PARTNER(SyncPloyTypeEnum.OUTPUT.getType(),
            "object_reference_partnerobj",
            "关联合作伙伴",
            I18NStringEnum.s1010.getI18nKey()),
    ;

    private Integer ployType;
    private String value;
    private String description;
    private String i18nKey;

    ConditionVariableEnum(Integer ployType, String value, String description, String nameI18nKey) {
        this.ployType = ployType;
        this.value = value;
        this.description = description;
        this.i18nKey = nameI18nKey;
    }
}
