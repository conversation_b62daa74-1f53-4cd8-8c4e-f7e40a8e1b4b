package com.fxiaoke.open.erpsyncdata.common.constant;

/**
 *  load by fs-metadata
 */
public interface FieldTypeContants {
  String SELECT_ONE = "select_one";
  String CURRENCY = "currency";
  String DATE = "date";
  String DATE_TIME = "date_time";
  String EMAIL = "email";
  String EMBEDDED_OBJECT = "embedded_object";
  String FILE_ATTACHMENT = "file_attachment";
  String IMAGE = "image";
  String LONG_TEXT = "long_text";
  String NUMBER = "number";
  String OBJECT_REFERENCE = "object_reference";
  String PERCENTILE = "percentile";
  String PHONE_NUMBER = "phone_number";
  String SELECT_MANY = "select_many";
  String TEXT = "text";
  String TIME = "time";
  String TRUE_OR_FALSE = "true_or_false";
  String URL = "url";
  String TAG = "tag";
  String EMBEDDED_OBJECT_LIST = "embedded_object_list";
  String FORMULA = "formula";
  String ARRAY = "array";
  String COMPOSITE_ARRAY = "composite_array";
  String AUTO_NUMBER = "auto_number";
  String EMPLOYEE = "employee";
  String DEPARTMENT = "department";
  String LOCATION = "location";
  String MULTI_LEVEL_SELECT_ONE = "multi_level_select_one";
  String RECORD_TYPE = "record_type";
  String SUMMARY = "summary";
  String COUNTRY = "country";
  String PROVINCE = "province";
  String CITY = "city";
  String DISTRICT = "district";
  String QUOTE = "quote";
  String GROUP = "group";
  String COUNT = "count"; //统计字段
  //String AREA = "area"; // 地区组件
  String SIGNATURE = "signature";  //签字
  String MASTER_DETAIL = "master_detail";  //主从字段
  String LOCK_RULE = "lock_rule";  //锁定规则
  String UseScope = "use_scope"; //对象使用范围
  String RICH_TEXT = "rich_text";
  String GEO_POINT = "geo_point";
}
