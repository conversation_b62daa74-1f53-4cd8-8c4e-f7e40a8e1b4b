package com.fxiaoke.open.erpsyncdata.common.constant;

import lombok.Getter;

@Getter
public enum FiledVariableValueEnum {
    ACCOUNT_OWNER(SyncPloyTypeEnum.INPUT.getType(), "account_owner", "负责人(数据源企业关联客户的负责人)"),
    PARTNER_OWNER(SyncPloyTypeEnum.INPUT.getType(), "partner_owner", "负责人(数据源企业关联合作伙伴的负责人)"),
    ACCOUNT_SOURCE(SyncPloyTypeEnum.INPUT.getType(), "account_source", "来源(数据源企业关联的客户)"),
    PARTNER_SOURCE(SyncPloyTypeEnum.INPUT.getType(), "partner_source", "来源(数据源企业关联的合作伙伴)"),
    PRIORITY_RELATION_OWNER(SyncPloyTypeEnum.INPUT.getType(), "priority_relation_owner", "负责人(默认匹配)"),
    PRIORITY_RELATION_SOURCE(SyncPloyTypeEnum.INPUT.getType(), "priority_relation_source", "来源(默认匹配)"),
    OBJECT_DATA_REFLECT_OUTER_OWNER(SyncPloyTypeEnum.INPUT.getType(), "object_data_reflect_outer_owner", "外部负责人(同步数据的负责人映射的对接人)"),
    OUTER_RELATION_OWNER(SyncPloyTypeEnum.INPUT.getType(), "outer_relation_owner", "外部负责人(数据源方主负责人)"),
    INPUT_CURRENT_DATE(SyncPloyTypeEnum.INPUT.getType(), "input_current_date", "当前日期"),
    INPUT_CURRENT_DATE_TIME(SyncPloyTypeEnum.INPUT.getType(), "input_current_date_time", "当前日期时间"),
    INPUT_CURRENT_TIME(SyncPloyTypeEnum.INPUT.getType(), "input_current_time", "当前时间"),
    DOWNSTREAM_RELATION_OWNER(SyncPloyTypeEnum.OUTPUT.getType(), "downstream_relation_owner", "负责人(目标方的主负责人)"),
    PURCHASE_ORDER_SUPPLIER_ID(SyncPloyTypeEnum.OUTPUT.getType(), "purchase_order_supplier_id", "供应商(上游企业供应商)"),
    GOODS_RECEIVED_NOTE_WAREHOUSE_ID(SyncPloyTypeEnum.OUTPUT.getType(), "goods_received_note_warehouse_id", "所属仓库(默认仓库)"),
    OUTPUT_CURRENT_DATE(SyncPloyTypeEnum.OUTPUT.getType(), "output_current_date", "当前日期"),
    OUTPUT_CURRENT_DATE_TIME(SyncPloyTypeEnum.OUTPUT.getType(), "output_current_date_time", "当前日期时间"),
    OUTPUT_CURRENT_TIME(SyncPloyTypeEnum.OUTPUT.getType(), "output_current_time", "当前时间"),
    ;

    FiledVariableValueEnum(Integer type, String value, String desc) {
        this.type = type;
        this.value = value;
        this.desc = desc;
    }

    /**
     * type = 1  采集 type = 2  下发
     */
    private Integer type;
    private String value;
    private String desc;

    public static Integer getSyncPloyType(String filedVariableValue) {
        for (FiledVariableValueEnum value : values()) {
            if (value.getValue().equals(filedVariableValue)) {
                if (value.getType().equals(SyncPloyTypeEnum.INPUT.getType())) {
                    return SyncPloyTypeEnum.INPUT.getType();
                } else if (value.getType().equals(SyncPloyTypeEnum.OUTPUT.getType())) {
                    return SyncPloyTypeEnum.OUTPUT.getType();
                }
            }
        }
        return null;
    }
}
