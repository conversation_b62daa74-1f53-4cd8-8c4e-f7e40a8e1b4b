package com.fxiaoke.open.erpsyncdata.common.jetcache;

import cn.hutool.core.util.StrUtil;
import com.alicp.jetcache.CacheManager;
import com.alicp.jetcache.CacheResult;
import com.alicp.jetcache.external.ExternalCacheConfig;
import com.alicp.jetcache.support.BroadcastManager;
import com.alicp.jetcache.support.CacheMessage;
import com.alicp.jetcache.support.SquashedLogger;
import com.fxiaoke.notifier.support.NotifierClient;
import com.github.trace.TraceContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Base64Utils;

import java.util.Set;


/**
 *
 */
public class FastNotifierBroadcastManager extends BroadcastManager {
    private static final Logger logger = LoggerFactory.getLogger(FastNotifierBroadcastManager.class);
    private final ExternalCacheConfig<?, ?> config;

    /**
     * 进推送的类型，为空不限制
     */
    public Set<Integer> onlyPublishTypes;

    public FastNotifierBroadcastManager(final CacheManager cacheManager, final ExternalCacheConfig<?, ?> config,final Set<Integer> onlyPublishTypes) {
        super(cacheManager);
        checkConfig(config);
        this.config = config;
        this.onlyPublishTypes = onlyPublishTypes;
    }

    @Override
    public synchronized void startSubscribe() {
        NotifierClient.register(config.getBroadcastChannel(), msg -> {
            String content = msg.getContent();
            byte[] bytes = Base64Utils.decodeFromString(content);
            processNotification(bytes, this.config.getValueDecoder());
            logger.debug("NotifierClient process message {}:{}", config.getBroadcastChannel(), msg);
        });
        logger.info("NotifierClient register {}",config.getBroadcastChannel());
    }

    @Override
    public CacheResult publish(final CacheMessage cacheMessage) {
        try {
            int type = cacheMessage.getType();
            if (onlyPublishTypes == null || onlyPublishTypes.contains(type)) {
                final byte[] msg = this.config.getValueEncoder().apply(cacheMessage);
                //base64转String
                String msgStr = Base64Utils.encodeToString(msg);
                String ea = StrUtil.blankToDefault(TraceContext.get().getEa(), "0");
                String employeeId = StrUtil.blankToDefault(TraceContext.get().getEmployeeId(), "0");
                NotifierClient.send(config.getBroadcastChannel(), msgStr, ea + "." + employeeId);
            }
            return CacheResult.SUCCESS_WITHOUT_MSG;
        } catch (Throwable e) {
            SquashedLogger.getLogger(logger).error("jetcache publish error", e);
            return new CacheResult(e);
        }
    }
}
