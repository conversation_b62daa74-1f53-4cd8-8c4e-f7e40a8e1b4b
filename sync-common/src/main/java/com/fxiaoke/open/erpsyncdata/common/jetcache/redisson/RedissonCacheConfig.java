package com.fxiaoke.open.erpsyncdata.common.jetcache.redisson;

import com.alicp.jetcache.external.ExternalCacheConfig;
import org.redisson.api.RedissonClient;

/**
 * 从官方代码copy后修改
 * copy from <a href="https://github.com/alibaba/jetcache/blob/v2.7.3/jetcache-support/jetcache-redisson/src/main/java/com/alicp/jetcache/redisson/RedissonCacheConfig.java">RedissonCacheConfig.java</a>
 *
 * <AUTHOR> (^_−)☆
 * @date 2023/4/23
 */
public class RedissonCacheConfig<K, V> extends ExternalCacheConfig<K, V> {
    private RedissonClient redissonClient;

    public RedissonClient getRedissonClient() {
        return redissonClient;
    }

    public void setRedissonClient(final RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }
}
