package com.fxiaoke.open.erpsyncdata.common.monitor;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/8/19
 */
public class MemoryMonitor {

    @Getter
    private final MemoryInfoRecord memoryInfoRecord;
    private boolean enableSampling = true;
    private boolean inSampling = false;

    private static class LazyHolder{
        private static final MemoryMonitor INSTANCE = new MemoryMonitor();
    }
    public static MemoryMonitor getInstance(){
        return LazyHolder.INSTANCE;
    }
    private MemoryMonitor() {
        memoryInfoRecord = new MemoryInfoRecord();
        String address = "unknown";
        try {
            address = InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException ignore) {
        }
        memoryInfoRecord.setAddress(address);
    }

    @AllArgsConstructor
    @ToString
    @Getter
    public static class MemoryInfo {
        Long free;
        Long total;
        Long max;
        Long used;
        Long maxFree;
        Long samplingTime;
    }

    @Setter
    @Getter
    @ToString
    public static class MemoryInfoRecord {
        private String address;
        private List<MemoryInfo> memoryInfos = new ArrayList<>(64);
    }

    public MemoryInfo getMemoryInfo() {
        Runtime runtime = Runtime.getRuntime();
        long freeMemory = runtime.freeMemory();
        long totalMemory = runtime.totalMemory();
        long maxMemory = runtime.maxMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxFree = maxMemory - usedMemory;
        long samplingTime = System.currentTimeMillis();
        return new MemoryInfo(freeMemory, totalMemory, maxMemory, usedMemory, maxFree, samplingTime);
    }

    public void sampling(Long intervalSecond, Integer samplingTimes) throws InterruptedException {
        if (inSampling) {
            return;
        }
        inSampling = true;
        enableSampling = true;
        for (int i = 0; i < samplingTimes && enableSampling; i++) {
            memoryInfoRecord.getMemoryInfos().add(getMemoryInfo());
            if (i != samplingTimes - 1) {
                TimeUnit.SECONDS.sleep(intervalSecond);
            }
        }
        inSampling = false;
    }

    public void stopSampling(){
        this.enableSampling = false;
        this.memoryInfoRecord.getMemoryInfos().clear();
    }

    public String getRecordFormat(){
        StringBuilder format = new StringBuilder();
        format.append("address:").append(this.memoryInfoRecord.address).append("\n");
        for (MemoryInfo memoryInfo : this.memoryInfoRecord.getMemoryInfos()) {
            format.append("maxFree:").append(formatSize(memoryInfo.maxFree)).append("; ")
                    .append("used:").append(formatSize(memoryInfo.used)).append("; ")
                    .append("max:").append(formatSize(memoryInfo.max)).append("; ")
                    .append("free:").append(formatSize(memoryInfo.free)).append("; ")
                    .append("total:").append(formatSize(memoryInfo.total)).append("; ")
                    .append("\n");
        }
        return format.toString();
    }

    public static String formatSize(Long s) {
        if (s == null) {
            return "NULL";
        }
        String sizeStr;
        DecimalFormat df = new DecimalFormat("#.00");
        if (s < 1024) {
            sizeStr = df.format((double) s) + "B";
        } else if (s < 1048576) {
            sizeStr = df.format((double) s / 1024) + "KB";
        } else if (s < 1073741824) {
            sizeStr = df.format((double) s / 1048576) + "MB";
        } else {
            sizeStr = df.format((double) s / 1073741824) + "GB";
        }
        return sizeStr;
    }
}
