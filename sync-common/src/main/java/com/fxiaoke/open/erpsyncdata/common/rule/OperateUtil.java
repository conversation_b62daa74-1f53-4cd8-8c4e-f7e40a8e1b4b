package com.fxiaoke.open.erpsyncdata.common.rule;

import com.fxiaoke.open.erpsyncdata.common.constant.ConditionExceptionCodeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.FieldType;
import com.fxiaoke.open.erpsyncdata.common.constant.Operate;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Updator lilei
 */
public class OperateUtil {
    private static Set<String> stringDefaultOperate = new HashSet<>();

    static {
        //等于 == ,不等于 != | 包含 continues ,不包含 !continues |开始startwiths|结束endWiths|为空 null ,不为空 != null |属于 in,不属于 notIn
        stringDefaultOperate.add(Operate.EQUALS);
        stringDefaultOperate.add(Operate.NOT_EQUALS);
        stringDefaultOperate.add(Operate.CONTAINS);
        stringDefaultOperate.add(Operate.NOT_CONTAINS);
        stringDefaultOperate.add(Operate.START_WITH);
        stringDefaultOperate.add(Operate.END_WITH);
        stringDefaultOperate.add(Operate.NOT_START_WITH);
        stringDefaultOperate.add(Operate.NOT_END_WITH);
        stringDefaultOperate.add(Operate.IN);
        stringDefaultOperate.add(Operate.NOT_IN);
        stringDefaultOperate.add(Operate.IS);
        stringDefaultOperate.add(Operate.IS_NOT);
        stringDefaultOperate.add(Operate.EQ);
        stringDefaultOperate.add(Operate.N);
        stringDefaultOperate.add(Operate.LIKE);
        stringDefaultOperate.add(Operate.NOT_LIKE);
    }

    private static Set<String> numberDefaultOperate = new HashSet<>();

    static {
        //等于,不等于|大于,小于|大于等于,小于等于|为空,不为空|介于
        numberDefaultOperate.add(Operate.EQ);
        numberDefaultOperate.add(Operate.N);
        numberDefaultOperate.add(Operate.GT);
        numberDefaultOperate.add(Operate.LT);
        numberDefaultOperate.add(Operate.GTE);
        numberDefaultOperate.add(Operate.LTE);
        numberDefaultOperate.add(Operate.BETWEEN);
        numberDefaultOperate.add(Operate.NOT_BETWEEN);
        numberDefaultOperate.add(Operate.IN);
        numberDefaultOperate.add(Operate.NOT_IN);
        numberDefaultOperate.add(Operate.IS);
        numberDefaultOperate.add(Operate.IS_NOT);
    }

    private static Set<String> boolDefaultOperate = new HashSet<>();

    static {
        //等于,不等于|大于,小于|大于等于,小于等于|为空,不为空|介于
        boolDefaultOperate.add(Operate.EQ);
        boolDefaultOperate.add(Operate.N);
        boolDefaultOperate.add(Operate.EQUALS);
        boolDefaultOperate.add(Operate.NOT_EQUALS);
        boolDefaultOperate.add(Operate.IS);
        boolDefaultOperate.add(Operate.IS_NOT);
    }

    private static Set<String> selectManyOperate = new HashSet<>();

    static {
        //等于,不等于|大于,小于|大于等于,小于等于|为空,不为空|介于
        selectManyOperate.add(Operate.EQ);
        selectManyOperate.add(Operate.N);
        selectManyOperate.add(Operate.IN);
        selectManyOperate.add(Operate.LIKE);
        selectManyOperate.add(Operate.NOT_LIKE);
        selectManyOperate.add(Operate.NOT_IN);
        selectManyOperate.add(Operate.IS);
        selectManyOperate.add(Operate.IS_NOT);
    }

    public static String operateExpressionMap(final String fieldName, final String fieldType, final String operate, final List<String> values) {
        if (StringUtils.isBlank(fieldType)) {
            throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_TYPE_UNSUPPORT, "fieldType is null");
        }
        switch (fieldType) {
            case FieldType.STRING:
            case FieldType.TEXT:
            case FieldType.RECORD_TYPE:
            case FieldType.PHONE_NUMBER:
            case FieldType.EMAIL:
            case FieldType.USE_RANGE:
            case FieldType.OBJECT_REFERENCE:
            case FieldType.ID:
            case FieldType.CATEGORY:
                return stringRuleToExpression(values, operate, fieldName);
            case FieldType.NUMBER:
                return numberRuleToExpression(values, operate, fieldName);
            case FieldType.DATE:
            case FieldType.DATE_TIME:
            case FieldType.TIME:
                return dateRuleToExpression(values, operate, fieldName);
            case FieldType.BOOL:
                return booleanRuleToExpression(values, operate, fieldName);
            case FieldType.LONG_TEXT:
                return stringRuleToExpression(values, operate, fieldName);
            case FieldType.SELECT_ONE:
            case FieldType.PROVINCE:
            case FieldType.COUNTRY:
            case FieldType.DISTRICT:
            case FieldType.CITY:
                return stringRuleToExpression(values, operate, fieldName);
            case FieldType.SELECT_MANY:
            case FieldType.EMPLOYEE:
            case FieldType.DEPARTMENT:
                return selectManyRuleToExpression(values, operate, fieldName);
            case FieldType.PERCENTILE:
                return numberRuleToExpression(values, operate, fieldName);
            case FieldType.CURRENCY:
            case FieldType.COUNT:
                return numberRuleToExpression(values, operate, fieldName);
            case FieldType.OBJECT_REFERENCE_MANY:
                return objectReferenceManyRuleToExpression(values, operate, fieldName);
            default:
                throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_TYPE_UNSUPPORT, fieldType);
        }
    }

    private static String booleanRuleToExpression(final List values, final String operate, final String fieldName) {
        if (StringUtils.isBlank(operate)) {
            throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_OPERATE_UNSUPPORT, false);
        }

        StringBuilder expression = new StringBuilder();
        Boolean bool = null;
        if (values.size() > 0) {
            bool = Boolean.valueOf((String) values.get(0));
        } else {
            if (!operate.equals(Operate.IS) && !operate.equals(Operate.IS_NOT)) {
                throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_VALUES_EMPTY, false);
            }
        }

        switch (operate) {
            case Operate.IS:
                expression.append(fieldName).append(" == nil");
                break;
            case Operate.IS_NOT:
                expression.append(fieldName).append(" != nil");
                break;
            case Operate.EQUALS:
                expression.append(fieldName).append(" == ").append(bool);
                break;
            case Operate.NOT_EQUALS:
                expression.append(fieldName).append(" != ").append(bool);
                break;
            case Operate.EQ:
                expression.append(fieldName).append(" == ").append(bool);
                break;
            case Operate.N:
                expression.append(fieldName).append(" != ").append(bool);
                break;
            default:
                throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_OPERATE_UNSUPPORT, operate);
        }
        return expression.toString();
    }

    private static String numberRuleToExpression(final List<String> values, final String operate, final String fieldName) {
        if (StringUtils.isBlank(operate)) {
            throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_OPERATE_UNSUPPORT, false);
        }
        StringBuilder expression = new StringBuilder();
        int len = 0;
        int lenFlag = 0;
        List<Double> numberList = new ArrayList<>();
        if (values != null) {
            len = values.size();
            values.forEach(v -> {
                if (StringUtils.isNotEmpty(v)) {
                    numberList.add(Double.valueOf(v));
                }
            });
        }
        switch (operate) {
            case Operate.EQ:
                expression.append(fieldName).append(" == ").append(numberList.get(0)).append("M");
                break;
            case Operate.N:
                expression.append(fieldName).append(" != ").append(numberList.get(0)).append("M");
                break;
            case Operate.GT:
                expression.append(fieldName).append(" > ").append(numberList.get(0)).append("M");
                break;
            case Operate.GTE:
                expression.append(fieldName).append(" >= ").append(numberList.get(0)).append("M");
                break;
            case Operate.LT:
                expression.append(fieldName).append(" < ").append(numberList.get(0)).append("M");
                break;
            case Operate.LTE:
                expression.append(fieldName).append(" <= ").append(numberList.get(0)).append("M");
                break;
            case Operate.IS:
                expression.append(fieldName).append(" == nil ");
                break;
            case Operate.IS_NOT:
                expression.append(fieldName).append(" != nil ");
                break;
            case Operate.BETWEEN_CAPITAL:
                if (numberList.size() < 2) {
                    throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_VALUE_NOT_VALID_FOR_BETWEEN, "between size <2");
                }
                expression.append(" ( ").append(fieldName).append(" >= ").append(numberList.get(0)).append(" && ").append(fieldName).append(" <= ").append(numberList.get(1))
                        .append(')');
                break;
            case Operate.BETWEEN:
                if (numberList.size() < 2) {
                    throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_VALUE_NOT_VALID_FOR_BETWEEN, "between size <2");
                }
                expression.append(" ( ").append(fieldName).append(" >= ").append(numberList.get(0)).append("M").append(" && ").append(fieldName).append(" <= ").append(numberList.get(1)).append("M")
                    .append(')');
                break;
            case Operate.NOT_BETWEEN:
                if (numberList.size() < 2) {
                    throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_VALUE_NOT_VALID_FOR_BETWEEN, "not between size <2");
                }
                expression.append(" !( ").append(fieldName).append(" >= ").append(numberList.get(0)).append("M").append(" && ").append(fieldName).append(" <= ").append(numberList.get(1)).append("M")
                    .append(')');
                break;
            case Operate.IN:
                expression.append(" ( ");
                numberIN(fieldName, expression, len, lenFlag, numberList);
                expression.append(" ) ");
                break;
            case Operate.NOT_IN:
                expression.append(" !( ");
                numberIN(fieldName, expression, len, lenFlag, numberList);
                expression.append(" ) ");
                break;
            default:
                throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_OPERATE_UNSUPPORT, operate);
        }
        return expression.toString();
    }

    private static String dateRuleToExpression(final List<String> values, final String operate, final String fieldName) {
        if (StringUtils.isBlank(operate)) {
            throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_OPERATE_UNSUPPORT, false);
        }
        StringBuilder expression = new StringBuilder();
        int len = 0;
        int lenFlag = 0;
        List<Long> numberList = new ArrayList<>();
        if (values != null) {
            len = values.size();
            values.forEach(v -> {
                if (StringUtils.isNotEmpty(v)) {
                    numberList.add(Long.valueOf(v));
                }
            });
        }
        switch (operate) {
            case Operate.EQ:
                expression.append(fieldName).append(" == ").append(numberList.get(0));
                break;
            case Operate.N:
                expression.append(fieldName).append(" != ").append(numberList.get(0));
                break;
            case Operate.GT:
                expression.append(fieldName).append(" > ").append(numberList.get(0));
                break;
            case Operate.GTE:
                expression.append(fieldName).append(" >= ").append(numberList.get(0));
                break;
            case Operate.LT:
                expression.append(fieldName).append(" < ").append(numberList.get(0));
                break;
            case Operate.LTE:
                expression.append(fieldName).append(" <= ").append(numberList.get(0));
                break;
            case Operate.IS:
                expression.append(fieldName).append(" == nil ");
                break;
            case Operate.IS_NOT:
                expression.append(fieldName).append(" != nil ");
                break;
            case Operate.BETWEEN:
                if (numberList.size() < 2) {
                    throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_VALUE_NOT_VALID_FOR_BETWEEN, "between size <2");
                }
                expression.append(" ( ").append(fieldName).append(" >= ").append(numberList.get(0)).append("M").append(" && ").append(fieldName).append(" <= ").append(numberList.get(1)).append("M")
                        .append(')');
                break;
            case Operate.BETWEEN_CAPITAL:
                if (numberList.size() < 2) {
                    throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_VALUE_NOT_VALID_FOR_BETWEEN, "between size <2");
                }
                expression.append(" ( ").append(fieldName).append(" >= ").append(numberList.get(0)).append(" && ").append(fieldName).append(" <= ").append(numberList.get(1))
                    .append(')');
                break;
            case Operate.NOT_BETWEEN:
                if (numberList.size() < 2) {
                    throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_VALUE_NOT_VALID_FOR_BETWEEN, "not between size <2");
                }
                expression.append(" !( ").append(fieldName).append(" >= ").append(numberList.get(0)).append("M").append(" && ").append(fieldName).append(" <= ").append(numberList.get(1)).append("M")
                    .append(')');
                break;
            case Operate.IN:
                expression.append(" ( ");
                LongIN(fieldName, expression, len, lenFlag, numberList);
                expression.append(" ) ");
                break;
            case Operate.NOT_IN:
                expression.append(" !( ");
                LongIN(fieldName, expression, len, lenFlag, numberList);
                expression.append(" ) ");
                break;
            default:
                throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_OPERATE_UNSUPPORT, operate);
        }
        return expression.toString();
    }

    private static void numberIN(String fieldName, StringBuilder expression, int len, int lenFlag, List<Double> numberList) {
        for (Double value : numberList) {
            expression.append(fieldName).append(" == ").append(value).append("M");
            if (lenFlag + 1 < len) {
                expression.append(" || ");
            }
            lenFlag++;
        }
    }

    private static void LongIN(String fieldName, StringBuilder expression, int len, int lenFlag, List<Long> numberList) {
        for (Long value : numberList) {
            expression.append(fieldName).append(" == ").append(value);
            if (lenFlag + 1 < len) {
                expression.append(" || ");
            }
            lenFlag++;
        }
    }
    private static String selectManyRuleToExpression(final List<String> values, final String operate, final String fieldName) {
        if (StringUtils.isBlank(operate)) {
            throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_OPERATE_UNSUPPORT, "operate is null");
        }
        int len = 0;
        int lenFlag = 0;
        if (values != null) {
            len = values.size();
        }
        StringBuilder expression = new StringBuilder();
        switch (operate) {
            case Operate.EQ:
                expression.append(" ( ");
                expression.append("count(").append(fieldName).append(") == ").append(len);
                if (len > 0) {
                    expression.append(" && ");
                }
                seqInclude(values, fieldName, len, lenFlag, expression);
                expression.append(" ) ");
                break;
            case Operate.N:
                expression.append(" !( ");
                expression.append("count(").append(fieldName).append(") == ").append(len);
                if (len > 0) {
                    expression.append(" && ");
                }
                seqInclude(values, fieldName, len, lenFlag, expression);
                expression.append(" ) ");
                break;
            case Operate.IS:
                expression.append(fieldName).append(" == nil ");
                break;
            case Operate.IS_NOT:
                expression.append(fieldName).append(" != nil ");
                break;
            case Operate.IN:
                expression.append(" ( ");
                seqIncludeForMany(values, fieldName, len, lenFlag, expression);
                expression.append(" ) ");
                break;
            case Operate.NOT_IN:
                expression.append(" !( ");
                seqIncludeForMany(values, fieldName, len, lenFlag, expression);
                expression.append(" ) ");
                break;
            case Operate.LIKE:
                expression.append(" ( ");
                seqInclude(values, fieldName, len, lenFlag, expression);
                expression.append(" ) ");
                break;
            case Operate.NOT_LIKE:
                expression.append(" !( ");
                seqInclude(values, fieldName, len, lenFlag, expression);
                expression.append(" ) ");
                break;
            default:
                throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_OPERATE_UNSUPPORT, operate);
        }
        return expression.toString();
    }
    private static String objectReferenceManyRuleToExpression(final List<String> values, final String operate, final String fieldName) {
        if (StringUtils.isBlank(operate)) {
            throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_OPERATE_UNSUPPORT, "operate is null");
        }
        int len = 0;
        int lenFlag = 0;
        if (values != null) {
            len = values.size();
        }
        StringBuilder expression = new StringBuilder();
        switch (operate) {
            case Operate.EQ:
                expression.append(" ( ");
                expression.append("count(").append(fieldName).append(") == ").append(len);
                if (len > 0) {
                    expression.append(" && ");
                }
                seqInclude(values, fieldName, len, lenFlag, expression);
                expression.append(" ) ");
                break;
            case Operate.N:
                expression.append(" !( ");
                expression.append("count(").append(fieldName).append(") == ").append(len);
                if (len > 0) {
                    expression.append(" && ");
                }
                seqInclude(values, fieldName, len, lenFlag, expression);
                expression.append(" ) ");
                break;
            case Operate.IS:
                expression.append(fieldName).append(" == nil ").append(" || count(").append(fieldName).append(") ==0 ");
                break;
            case Operate.IS_NOT:
                expression.append(fieldName).append(" != nil ").append(" && count(").append(fieldName).append(") >0 ");
                break;
            case Operate.IN:
                expression.append(" ( ");
                seqIncludeForMany(values, fieldName, len, lenFlag, expression);
                expression.append(" ) ");
                break;
            case Operate.NOT_IN:
                expression.append(" !( ");
                seqIncludeForMany(values, fieldName, len, lenFlag, expression);
                expression.append(" ) ");
                break;
            default:
                throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_OPERATE_UNSUPPORT, operate);
        }
        return expression.toString();
    }

    private static void seqInclude(List<String> values, String fieldName, int len, int lenFlag, StringBuilder expression) {
        for (String value : values) {
            expression.append("  include(").append(fieldName).append(",\"").append(value).append("\")");
            if (lenFlag + 1 < len) {
                expression.append(" && ");
            }
            lenFlag++;
        }
    }

    private static void seqExclude(List<String> values, String fieldName, int len, int lenFlag, StringBuilder expression) {
        for (String value : values) {
            expression.append("  !include(").append(fieldName).append(",\"").append(value).append("\")");
            if (lenFlag + 1 < len) {
                expression.append(" && ");
            }
            lenFlag++;
        }
    }

    private static void seqIncludeForMany(List<String> values, String fieldName, int len, int lenFlag, StringBuilder expression) {
        if(values.size()==1&&values.get(0).contains(";")){//旧版本数据范围组件，输入字符串；分隔没有被分隔成list
            List<String> strings = Splitter.on(";").splitToList(values.get(0));
            len=strings.size();
            for (String value : strings) {
                expression.append("  include(").append(fieldName).append(",\"").append(value).append("\")");
                try{
                    Long.valueOf(value);//为了兼容fieldName值可能是整形数字
                    expression.append(" || ").append("  include(").append(fieldName).append(",").append(value).append(")");
                }catch (Exception e){

                }
                if (lenFlag + 1 < len) {
                    expression.append(" || ");
                }
                lenFlag++;
            }
        }else{
            for (String value : values) {
                expression.append("  include(").append(fieldName).append(",\"").append(value).append("\")");
                try{
                    Long.valueOf(value);//为了兼容fieldName值可能是整形数字
                    expression.append(" || ").append("  include(").append(fieldName).append(",").append(value).append(")");
                }catch (Exception e){

                }
                if (lenFlag + 1 < len) {
                    expression.append(" || ");
                }
                lenFlag++;
            }
        }
    }


    private static String stringRuleToExpression(final List<String> values, final String operate, final String fieldName) {
        if (StringUtils.isBlank(operate)) {
            throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_OPERATE_UNSUPPORT, "operate is null");
        }
        StringBuilder expression = new StringBuilder();
        int len = 0;
        int lenFlag = 0;
        List<String> v = new ArrayList<>();
        if (values != null) {
            len = values.size();
            v = Lists.newArrayList(values);
        }
        switch (operate) {
            case Operate.CONTAINS:
                expression.append("string.contains(").append(fieldName).append(",\"").append(v.get(0)).append("\")");
                break;
            case Operate.NOT_CONTAINS:
                expression.append("!string.contains(").append(fieldName).append(",\"").append(v.get(0)).append("\")");
                break;
            case Operate.LIKE:
                expression.append("string.contains(").append(fieldName).append(",\"").append(v.get(0)).append("\")");
                break;
            case Operate.NOT_LIKE:
                expression.append("!string.contains(").append(fieldName).append(",\"").append(v.get(0)).append("\")");
                break;
            case Operate.START_WITH:
            case Operate.START_WITH_CAPITAL:
                expression.append("string.startsWith(").append(fieldName).append(",\"").append(v.get(0)).append("\")");
                break;
            case Operate.NOT_START_WITH:
                expression.append("!string.startsWith(").append(fieldName).append(",\"").append(v.get(0)).append("\")");
                break;
            case Operate.END_WITH:
            case Operate.END_WITH_CAPITAL:
                expression.append("string.endsWith(").append(fieldName).append(",\"").append(v.get(0)).append("\")");
                break;
            case Operate.NOT_END_WITH:
                expression.append("!string.endsWith(").append(fieldName).append(",\"").append(v.get(0)).append("\")");
                break;
            case Operate.IS:
                expression.append(" ( ");
                expression.append(fieldName).append(" == nil ").append(" || ").append(fieldName).append(" == ''");
                expression.append(" ) ");
                break;
            case Operate.IS_NOT:
                expression.append(fieldName).append(" != nil ").append(" && ").append(fieldName).append(" != ''");
                break;
            case Operate.EQUALS:
                expression.append(fieldName).append(" == \"").append(v.get(0)).append("\"");
                break;
            case Operate.NOT_EQUALS:
                expression.append(fieldName).append(" != \"").append(v.get(0)).append("\"");
                break;
            case Operate.EQ:
                expression.append(fieldName).append(" == \"").append(v.get(0)).append("\"");
                break;
            case Operate.N:
                expression.append(fieldName).append(" != \"").append(v.get(0)).append("\"");
                break;
            case Operate.IN:
                expression.append(" ( ");
                stringIN(fieldName, expression, len, lenFlag, v);
                expression.append(" ) ");
                break;
            case Operate.NOT_IN:
                expression.append(" !( ");
                stringIN(fieldName, expression, len, lenFlag, v);
                expression.append(" ) ");
                break;
            default:
                throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_OPERATE_UNSUPPORT, operate);
        }
        return expression.toString();
    }

    private static void stringIN(String fieldName, StringBuilder expression, int len, int lenFlag, List<String> v) {
        if(v.size()==1&&v.get(0).contains(";")){//旧版本数据范围组件，输入字符串；分隔没有被分隔成list
            List<String> strings = Splitter.on(";").splitToList(v.get(0));
            len=strings.size();
            for (String value : strings) {
                expression.append(fieldName).append(" == \"").append(value).append("\"");
                if (lenFlag + 1 < len) {
                    expression.append(" || ");
                }
                lenFlag++;
            }
        }else{
            for (String value : v) {
                expression.append(fieldName).append(" == \"").append(value).append("\"");
                if (lenFlag + 1 < len) {
                    expression.append(" || ");
                }
                lenFlag++;
            }
        }
    }

    public static void checkOperate(final String fieldType, final String operateStr, final List<String> value) {
        if (StringUtils.isBlank(fieldType)) {
            throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_TYPE_UNSUPPORT, false);
        }
        if (StringUtils.isBlank(operateStr)) {
            throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_OPERATE_UNSUPPORT, false);
        }

        if (FieldType.STRING.equals(fieldType) || FieldType.TEXT.equals(fieldType) || FieldType.LONG_TEXT.equals(fieldType) || FieldType.SELECT_ONE.equals(fieldType) || FieldType.RECORD_TYPE
            .equals(fieldType) || FieldType.PHONE_NUMBER.equals(fieldType) || FieldType.EMAIL.equals(fieldType) || FieldType.DEPARTMENT.equals(fieldType)) {
            stringOperate(operateStr, value);
        } else if (FieldType.NUMBER.equals(fieldType) || FieldType.CURRENCY.equals(fieldType) || FieldType.PERCENTILE.equals(fieldType) || FieldType.DATE.equals(fieldType) || FieldType.DATE_TIME
            .equals(fieldType) || FieldType.TIME.equals(fieldType) || FieldType.COUNT.equals(fieldType)) {
            numberOperate(operateStr, value);
        } else if (FieldType.BOOL.equals(fieldType)) {
            booleanOperate(operateStr, value);
        } else if (FieldType.SELECT_MANY.equals(fieldType) || FieldType.EMPLOYEE.equals(fieldType) || FieldType.DEPARTMENT.equals(fieldType)) {
            selectManyOperate(operateStr, value);
        } else {
            throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_TYPE_UNSUPPORT, fieldType);
        }
    }

    private static void selectManyOperate(final String operateStr, final List<String> values) {
        if (!selectManyOperate.contains(operateStr)) {
            throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_OPERATE_UNSUPPORT, false);
        }
        if (Operate.IS.equals(operateStr) || Operate.IS_NOT.equals(operateStr)) {
            return;
        }
        if (CollectionUtils.isEmpty(values)) {
            throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_VALUES_EMPTY, "values is null");
        }
        values.forEach(value -> {
            if (value == null) {
                throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_VALUES_IS_NULL, "value is null");
            }
        });
    }

    private static void stringOperate(final String operateStr, final List<String> values) {
        if (!stringDefaultOperate.contains(operateStr)) {
            throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_OPERATE_UNSUPPORT, false);
        }
        if (Operate.IS.equals(operateStr) || Operate.IS_NOT.equals(operateStr)) {
            return;
        }
        if (CollectionUtils.isEmpty(values)) {
            throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_VALUES_EMPTY, false);
        }
        values.forEach(value -> {
            if (value == null) {
                throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_VALUES_IS_NULL, false);
            }
        });
    }

    private static void numberOperate(final String operateStr, final List<String> values) {
        if (!numberDefaultOperate.contains(operateStr)) {
            throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_OPERATE_UNSUPPORT, false);
        }
        if (Operate.IS.equals(operateStr) || Operate.IS_NOT.equals(operateStr)) {
            return;
        }
        if (CollectionUtils.isEmpty(values)) {
            throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_VALUES_EMPTY, false);
        }
        values.forEach(value -> {
            if (value == null) {
                throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_TYPE_NUMBER_ERROR, false);
            }
            try {
                Double.valueOf(value);
            } catch (Exception e) {
                throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_TYPE_NUMBER_ERROR, false);
            }
        });
    }

    private static void booleanOperate(final String operateStr, final List<String> values) {
        if (!boolDefaultOperate.contains(operateStr)) {
            throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_OPERATE_UNSUPPORT, false);
        }
        if (Operate.IS.equals(operateStr) || Operate.IS_NOT.equals(operateStr)) {
            return;
        }
        if (CollectionUtils.isEmpty(values)) {
            throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_VALUES_EMPTY, false);
        }
        String str = values.get(0);
        if (!("true".equals(str) || "false".equals(str))) {
            throw new ConditionServiceException(ConditionExceptionCodeEnum.RULE_FIELD_VALUES_BOOLEAN_ERROR, false);
        }
    }

    private static List<Double> stringToDouble(List<String> values) {
        List<Double> doublesValues = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(values)) {
            values.forEach(value -> {
                doublesValues.add(Double.valueOf(value));
            });
        }
        return doublesValues;
    }
}
