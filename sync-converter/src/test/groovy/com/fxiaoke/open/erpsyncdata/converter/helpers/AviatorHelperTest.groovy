package com.fxiaoke.open.erpsyncdata.converter.helpers


import com.fxiaoke.open.erpsyncdata.common.data.ObjectData
import com.fxiaoke.open.erpsyncdata.common.rule.ConditionUtil
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil
import spock.lang.Specification
import spock.lang.Subject

/**
 *
 * <AUTHOR> (^_−)☆
 */
class AviatorHelperTest extends Specification {

    @Subject
    AviatorHelper aviatorHelper

    void setup() {
        aviatorHelper = new AviatorHelper()
    }

    def "test Execute"() {
        String expr = "((field_product_seven_level_classification_code__c == \"NEW04MGRFIN030104\") && (serialNumber__c != nil  && serialNumber__c != '')) || ((field_product_seven_level_classification_code__c == \"NEW04MGRFIN030107\") && (serialNumber__c != nil  && serialNumber__c != ''))"
        ObjectData sourceData = JacksonUtil.fromJson("""
{"lock_rule":null,"salesOrderCode__c":"MGI_Salesorder_22100313343","pickCode__c":null,"field_x24p9__c":"1000020832","SetProductNameEN__c":null,"topItemSerialNumber__c":null,"mc_exchange_rate":"1.000000","life_status_before_invalid":null,"owner_department_id":"1352","field_query_oms_primary_key_success__c":null,"searchAfterId":["1673927006711","821481435027046400"],"topItemNumber__c":null,"omsCode__c":null,"field_J1XsY__c":"417281","remainingReturnableQuantity__c":"16.00","topSerialNumber__c":null,"lineNumber__c":"1.1","businessEntity__c__relation_ids":"820305260883869697","version":"3","stockTxId__c":null,"pickLineSeq__c":null,"productValue__c":null,"Release_time__c":null,"lineId__c":"821183556865556480","tenant_id":"746745","itemNumber__c":null,"businessEntity__c":"820305260883869697","itemTotalAmount__c":"0.00","orderNumber__c":"6339b8c12853a20001870b0e","origin_source":null,"cumulativeReturnableQty__c":null,"crmTrialLine__c":null,"serialQuantity__c":"1.00","orgName__c":null,"mc_functional_currency":"CNY","orderLineQuantity__c":"16.00","field_6pnAb__c":null,"lotExpirationDate__c":1634918400000,"shipFromOrgName__c":"MGI Tech Co., Ltd.","last_modified_time":1673927006711,"lotNumber__c":"A0202","life_status":"normal","field_2jITn__c":null,"out_tenant_id":null,"field_lvZ1z__c":null,"SetProductNameCN__c":null,"order_by":null,"shipSet__c":"417281-1","DueDate__c":null,"OMSlineId__c":"279768","orderStatusName__c":null,"shipFromOrg__c":"105","field_x72J8__c":null,"MaterialNameCN__c":"\\u9AD8\\u901A\\u91CF\\u53CC\\u672B\\u7AEF\\u6D4B\\u5E8F\\u5F15\\u7269\\u8BD5\\u5242\\u76D2\\uFF08App-A\\uFF09","itemQuantity__c":"0.00","lotQuantity__c":"16.00","primoryUomCode__c":"820301282200649728","setExchangeOrNot__c":null,"checkoutType__c":"order","owner_department":"160302-\\u7F8E\\u6D32\\u533A\\u8FD0\\u8425","field_erp_order_number_calculated__c":"417281","lock_status":"0","package":"CRM","create_time":1664890945909,"field_b119T__c":null,"amount__c":null,"cumulativeReturnedQuantity__c":"0.00","MaterialNameEN__c":"High-Throughput Pair-End Sequencing Primer Kit (App-A)","pickLineId__c":null,"created_by":["-10000"],"relevant_team":null,"serialNumber__c":"S2011071474","data_own_department":["1156"],"itemCode__c":"63371788f26b320001876e36","field_Z5d3c__c":"MGI18000R2019121802_24","name":"ExWarehouse-2022-10-63295","Miscellaneous__c":null,"_id":"821481435027046400","topFlag__c":"Y","field_L2lp6__c":null,"field_xQt41__c":null,"field_EndUser__c":null,"field_vBBu1__c":null,"CREATEdevice__c":null,"order_record_type__c":"record_NwGlr__c","forecast_reagent_count__c":null,"customerNumber__c":"6337e9079d595800016151fe","lock_user":null,"is_deleted":false,"crmLine__c":null,"object_describe_api_name":"orderExWarehouse__c","Release_days__c":null,"out_owner":null,"itemCode__c__relation_ids":"63371788f26b320001876e36","deviceOutboundType__c":null,"field_product_seven_level_classification_code__c":"SEQ18MGRFIN010206","owner":["1011"],"itemAmount__c":null,"transactionDate__c":1606394239000,"omsorderId__c":null,"last_modified_by":["-10000"],"mc_currency":"CNY","shipmentNumber__c":"160372","orderNumber__c__relation_ids":"6339b8c12853a20001870b0e","record_type":"default__c","topLineId__c":null,"lineId__c__relation_ids":"821183556865556480","customerNumber__c__relation_ids":"6337e9079d595800016151fe","pickId__c":null,"mc_exchange_rate_version":"1663608690174","Miscellaneousline__c":null,"primoryUomCode__c__relation_ids":"820301282200649728"}
""", ObjectData.class)
        def result = aviatorHelper.execute(expr, sourceData, "1", "1", "1", "1", false)
        expect:
        !result
    }

    def "testParseExpression"() {
        when:
        FilterData rule = new FilterData();
        rule.setFieldApiName("ff");
        rule.setFieldType(fieldType)
        rule.setOperate(operate)
        rule.setFieldValue(filterValue)
        String expression = ConditionUtil.parseExpression(rule);
        ObjectData sourceData = JacksonUtil.fromJson("{}", ObjectData.class)
        sourceData.put("ff", fieldValue);
        def result1 = aviatorHelper.execute(expression, sourceData, "1", "1", "1", "1", false)

        then:
        result1 == result

        where:
        name           || filterValue                    | fieldType     | fieldValue      | operate     | result
        "文本IN-1"     || ["1", "23"]                    | "text"        | null            | "IN"        | false
        "文本IN-2"     || ["1", "23"]                    | "text"        | "2"             | "IN"        | false
        "文本IN-3"     || ["1", "23"]                    | "text"        | "1"             | "IN"        | true
        "文本NIN-1"    || ["1", "23"]                    | "text"        | null            | "NIN"       | true
        "文本NIN-2"    || ["1", "23"]                    | "text"        | "2"             | "NIN"       | true
        "文本NIN-3"    || ["1", "23"]                    | "text"        | "1"             | "NIN"       | false
        "多选IN-1"     || ["1", "23"]                    | "select_many" | null            | "IN"        | false
        "多选IN-2"     || ["1", "23"]                    | "select_many" | ["2"]           | "IN"        | false
        "多选IN-3"     || ["1", "23"]                    | "select_many" | ["1"]           | "IN"        | true
        "多选IN-4"     || ["1", "23"]                    | "select_many" | ["1", "23"]     | "IN"        | true
        "多选NIN-1"    || ["1", "23"]                    | "select_many" | null            | "NIN"       | true
        "多选NIN-2"    || ["1", "23"]                    | "select_many" | ["2"]           | "NIN"       | true
        "多选NIN-3"    || ["1", "23"]                    | "select_many" | ["1"]           | "NIN"       | false
        "文本开始于-1" || ["23"]                         | "text"        | null            | "STARTWITH" | false
        "文本开始于-2" || ["23"]                         | "text"        | "21113h33323"   | "STARTWITH" | false
        "文本开始于-3" || ["23"]                         | "text"        | "231133h3133"   | "STARTWITH" | true
        "文本结束于-1" || ["23"]                         | "text"        | null            | "ENDWITH"   | false
        "文本结束于-2" || ["23"]                         | "text"        | "1111h123"      | "ENDWITH"   | true
        "文本结束于-3" || ["23"]                         | "text"        | "11112h3231111" | "ENDWITH"   | false
        "数字在之间-1" || ["1", "23"]                    | "number"      | null            | "BETWEEN"   | false
        "数字在之间-2" || ["1", "23"]                    | "number"      | 3               | "BETWEEN"   | true
        "数字在之间-3" || ["1", "23"]                    | "number"      | 0               | "BETWEEN"   | false
        "时间在之间-1" || [1730044800000, 1730217599000] | "date_time"   | null            | "BETWEEN"   | false
        "时间在之间-2" || [1730044800000, 1730217599000] | "date_time"   | 1730217589080   | "BETWEEN"   | true
        "时间在之间-3" || [1730044800000, 1730217599000] | "date_time"   | 1730217599009   | "BETWEEN"   | false

    }

}
