package com.fxiaoke.open.erpsyncdata.converter.manager

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.TypeReference
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData
import com.fxiaoke.open.erpsyncdata.preprocess.data.DuringSyncDataResult
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR> 
 * @date 2023/4/14 10:07:17
 */
class NodeCompleteDataProcessManagerTest extends Specification {

    @Unroll
    def "改从对象-#id"() {
        when:
        NodeCompleteDataProcessManager manager = new NodeCompleteDataProcessManager()
        def DestDataMap = JSON.parseObject(JSON.toJSONString(destMap), new TypeReference<LinkedHashMap<String, ObjectData>>() {
        })
        SyncDataContextEvent data = ["destDetailSyncDataIdAndDestDataMap": DestDataMap] as SyncDataContextEvent
        def object = JSON.parseObject(JSON.toJSONString(funcDetails), new TypeReference<Map<String, List<DuringSyncDataResult.DuringFuncObjectData>>>() {
        })

        manager.processDetails(data, object, cover)

        def check = JSON.parseObject(JSON.toJSONString(result), new TypeReference<LinkedHashMap<String, ObjectData>>() {
        })

        then:
        success == data.isSuccess()
        check == data.getDestDetailSyncDataIdAndDestDataMap()

        where:
        id                    | destMap                                                                                                            | funcDetails                                                                                                                     | cover || result                                                                                                             | success
        "无修改"              | [:]                                                                                                                | [:]                                                                                                                             | true  || destMap                                                                                                            | true
        "无修改"              | ["123": [:]]                                                                                                       | [:]                                                                                                                             | true  || destMap                                                                                                            | true
        "无修改"              | [:]                                                                                                                | ["test1": [[:]]]                                                                                                                | true  || destMap                                                                                                            | true


        "删字段1"             | ["mapping1": ["_id": "1", "a": "23"], "mapping2": ["_id": "2"]]                                                    | ["a": [["_id": "1", "erpExtendData": ["removeFields": ["a"]]]]]                                                                 | false || ["mapping1": ["_id": "1"], "mapping2": ["_id": "2"]]                                                               | true
        "删字段2"             | ["mapping1": ["_id": "1", "a": "23"], "mapping2": ["_id": "2", "a": "3"]]                                          | ["a": [["_id": "1", "erpExtendData": ["removeFields": ["a"]]]]]                                                                 | false || ["mapping1": ["_id": "1"], "mapping2": ["_id": "2", "a": "3"]]                                                     | true
        "删字段3"             | ["mapping1": ["_id": "1", "a": "23"], "mapping2": ["_id": "2"]]                                                    | ["a": [["_id": "1", "erpExtendData": ["removeFields": ["a"]]], ["_id": "2", "erpExtendData": ["removeFields": ["a"]]]]]         | false || ["mapping1": ["_id": "1"], "mapping2": ["_id": "2"]]                                                               | true

        "新增字段失败"        | ["mapping1": ["_id": "1"], "mapping2": ["_id": "2"]]                                                               | ["a": [["_id": "1", "erpExtendData": ["removeFields": ["a"]]], ["_id": "2", "aa": "23"]]]                                       | false || ["mapping1": ["_id": "1"], "mapping2": ["_id": "2"]]                                                               | true

        "删企业字段失败"      | ["mapping1": ["_id": "1", "tenant_id": "23"], "mapping2": ["_id": "2"]]                                            | ["a": [["_id": "1", "erpExtendData": ["removeFields": ["a"]]], ["_id": "2", "erpExtendData": ["removeFields": ["tenant_id"]]]]] | false || ["mapping1": ["_id": "1", "tenant_id": "23"], "mapping2": ["_id": "2"]]                                            | true

        "加字段1"             | ["mapping1": ["_id": "1"], "mapping2": ["_id": "2"]]                                                               | ["a": [["_id": "1", "erpExtendData": ["addData": ["a": 1]]]]]                                                                   | false || ["mapping1": ["_id": "1", "a": 1], "mapping2": ["_id": "2"]]                                                       | true
        "加字段2"             | ["mapping1": ["_id": "1"], "mapping2": ["_id": "2"]]                                                               | ["a": [["_id": "2", "erpExtendData": ["addData": ["a": 1]]]]]                                                                   | false || ["mapping1": ["_id": "1"], "mapping2": ["_id": "2", "a": 1]]                                                       | true
        "新增旧字段失败"      | ["mapping1": ["_id": "1"], "mapping2": ["_id": "2", "a": 2]]                                                       | ["a": [["_id": "2", "erpExtendData": ["addData": ["a": 1]]]]]                                                                   | false || ["mapping1": ["_id": "1"], "mapping2": ["_id": "2", "a": 2]]                                                       | true


        "改对象名失效"        | ["mapping1": ["_id": "1"], "mapping2": ["_id": "2", "object_describe_api_name": "api2"]]                           | ["a": [["_id": "2", "object_describe_api_name": "api1"]]]                                                                       | false || ["mapping1": ["_id": "1"], "mapping2": ["_id": "2", "object_describe_api_name": "api2"]]                           | true
        "改企业失效"          | ["mapping1": ["_id": "1"], "mapping2": ["_id": "2", "tenant_id": "api2"]]                                          | ["a": [["_id": "2", "tenant_id": "api1"]]]                                                                                      | false || ["mapping1": ["_id": "1"], "mapping2": ["_id": "2", "tenant_id": "api2"]]                                          | true


        "改顺序1"             | ["mapping1": ["_id": "1"], "mapping2": ["_id": "2"], "mapping3": ["_id": "3"]]                                     | ["a": [["_id": "3"], ["_id": "1"]], "b": [["_id": "2"]]]                                                                        | true  || ["mapping3": ["_id": "3"], "mapping1": ["_id": "1"], "mapping2": ["_id": "2"]]                                     | true
        "改顺序2"             | ["mapping1": ["_id": "1"], "mapping2": ["_id": "2"], "mapping3": ["_id": "3"]]                                     | ["a": [["_id": "3"]], "c": [["_id": "1"]], "b": [["_id": "2"]]]                                                                 | true  || ["mapping3": ["_id": "3"], "mapping1": ["_id": "1"], "mapping2": ["_id": "2"]]                                     | true

        "改顺序-少对象1"      | ["mapping1": ["_id": "1"], "mapping2": ["_id": "2"], "mapping3": ["_id": "3"]]                                     | ["a": [["_id": "3"], ["_id": "1"]]]                                                                                             | true  || destMap                                                                                                            | false
        "改顺序-少对象2"      | ["mapping1": ["_id": "1"], "mapping2": ["_id": "2"], "mapping3": ["_id": "3"]]                                     | ["a": [["_id": "3"]], "c": [["_id": "1"]]]                                                                                      | true  || destMap                                                                                                            | false

        "改顺序-多对象"       | ["mapping1": ["_id": "1"], "mapping2": ["_id": "2"], "mapping3": ["_id": "3"]]                                     | ["a": [["_id": "3"]], "c": [["_id": "1"]], "b": [["_id": "2"]], "d": [["_id": "4"]]]                                            | true  || destMap                                                                                                            | false

        "改顺序-id不一致1"    | ["mapping1": ["_id": "1"], "mapping2": ["_id": "2"], "mapping3": ["_id": "3"]]                                     | ["a": [["_id": "3"]], "c": [["_id": "1"]], "b": [["_id": "4"]]]                                                                 | true  || destMap                                                                                                            | false
        "改顺序-id不一致2"    | ["mapping1": ["_id": "1"], "mapping2": ["_id": "2"], "mapping3": ["_id": "3"]]                                     | ["a": [["_id": "3"], ["_id": "4"]], "b": [["_id": "2"]]]                                                                        | true  || destMap                                                                                                            | false

        "改顺序-改对象名失效" | ["mapping1": ["_id": "1", "object_describe_api_name": "api1"], "mapping2": ["_id": "2"], "mapping3": ["_id": "3"]] | ["a": [["_id": "3"], ["_id": "1", "object_describe_api_name": "api2"]], "b": [["_id": "2"]]]                                    | true  || ["mapping3": ["_id": "3"], "mapping1": ["_id": "1", "object_describe_api_name": "api1"], "mapping2": ["_id": "2"]] | true
        "改顺序-改企业失效"   | ["mapping1": ["_id": "1", "tenant_id": "api1"], "mapping2": ["_id": "2"], "mapping3": ["_id": "3"]]                | ["a": [["_id": "3"], ["_id": "1", "tenant_id": "api2"]], "b": [["_id": "2"]]]                                                   | true  || ["mapping3": ["_id": "3"], "mapping1": ["_id": "1", "tenant_id": "api1"], "mapping2": ["_id": "2"]]                | true
        "改顺序-没给企业"     | ["mapping1": ["_id": "1", "tenant_id": "api1"], "mapping2": ["_id": "2"], "mapping3": ["_id": "3"]]                | ["a": [["_id": "3"], ["_id": "1"]], "b": [["_id": "2"]]]                                                                        | true  || ["mapping3": ["_id": "3"], "mapping1": ["_id": "1", "tenant_id": "api1"], "mapping2": ["_id": "2"]]                | true

        "改顺序-去除扩展值"   | ["mapping1": ["_id": "1", "erpExtendData": "api1"], "mapping2": ["_id": "2"], "mapping3": ["_id": "3"]]            | ["a": [["_id": "3"], ["_id": "1"]], "b": [["_id": "2"]]]                                                                        | true  || ["mapping3": ["_id": "3"], "mapping1": ["_id": "1"], "mapping2": ["_id": "2"]]                                     | true

    }
}
