package com.fxiaoke.open.erpsyncdata.main.aop;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.fxiaoke.open.erpsyncdata.common.annotation.CompareSyncField;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncCompareConstant;
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.CompareResultDoc;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.factory.CompareSyncDataMongoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncPloyDetailSnapshotData2;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncPloyDetailSnapshotService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Map;

//@Component
//@Aspect
@Slf4j
//@Order(10)
public class CompareSyncResultInterceptor {

    // 2. PointCut表示这是一个切点，@annotation表示这个切点切到一个注解上，后面带该注解的全类名
    // 切面最主要的就是切点，所有的故事都围绕切点发生
    // logPointCut()代表切点名称
//    @Pointcut("@annotation(com.fxiaoke.open.erpsyncdata.common.annotation.CompareSyncField)")
//    @Pointcut("@annotation(com.fxiaoke.open.erpsyncdata.common.annotation.CompareSyncField)")
    public void logPointCut(){};
     @Autowired
     private SyncPloyDetailSnapshotService syncPloyDetailSnapshotService;
     @Autowired
     private CompareSyncDataMongoDao compareSyncDataMongoDao;
    // 3. 环绕通知
//    @Around("execution(* com.fxiaoke.open.erpsyncdata.preprocess.service.*(..))")
    private static final TransmittableThreadLocal<CompareResultDoc> BASE_LOG = new TransmittableThreadLocal<>();
    @Around("@annotation(compareSyncField)")
    public Object logAround(ProceedingJoinPoint joinPoint, CompareSyncField compareSyncField){
        // 获取方法名称
        Object proceed = null;
        String methodName = joinPoint.getSignature().getName();
        CompareResultDoc compareResultDoc=getBaseLog();
        try {
            // 获取入参
            Object[] param = joinPoint.getArgs();

            try {
                for(Object object : param){
                    if(ObjectUtils.isNotEmpty(object)&&object instanceof SyncDataContextEvent){
                        //com.fxiaoke.open.erpsyncdata.main.service.SyncDataMappingServiceImpl$$EnhancerBySpringCGLIB$$79f6b156 CGLIB代理
                        SyncDataContextEvent inputContext=(SyncDataContextEvent) object;
                        if(ObjectUtils.isEmpty(compareResultDoc.getSourceDataId())){
                            compareResultDoc.setSyncLogId(inputContext.getSyncLogId());
                            compareResultDoc.setTenantId(inputContext.getSourceData().getTenantId());
                            compareResultDoc.setSourceObjApiName(inputContext.getSourceData().getApiName());
                            String dataId=ObjectUtils.isNotEmpty(inputContext.getSourceData().getName())?inputContext.getSourceData().getName():inputContext.getSourceData().getId();
                            compareResultDoc.setSourceDataId(dataId);
                            resetBaseLog(compareResultDoc);
                        }
                        compareResultDoc.setSyncType(compareSyncField.syncType());
                        compareResultDoc.setInputParamsMap(JSONObject.toJSONString(object));
                        Result2<SyncPloyDetailSnapshotData2> syncPloyDetailSnapshotBySnapshotId = syncPloyDetailSnapshotService.getSyncPloyDetailSnapshotBySnapshotId(inputContext.getSourceData().getTenantId(),inputContext.getPloyDetailSnapshotId());
                        if(syncPloyDetailSnapshotBySnapshotId.isSuccess()&&ObjectUtils.isNotEmpty(syncPloyDetailSnapshotBySnapshotId.getData())){
                            compareResultDoc.setSourceObjApiName(syncPloyDetailSnapshotBySnapshotId.getData().getSourceObjectApiName());
                            compareResultDoc.setDestObjectApiName(syncPloyDetailSnapshotBySnapshotId.getData().getDestObjectApiName());
                            if(syncPloyDetailSnapshotBySnapshotId.getData().getSyncPloyDetailData().getSourceTenantType().equals(TenantTypeEnum.CRM.getType())){
                                //crm-erp.取name
                                compareResultDoc.setSourceDataId(inputContext.getSourceData().getName());
                                resetBaseLog(compareResultDoc);
                            }else {
                                compareResultDoc.setSourceDataId(inputContext.getSourceData().getId());
                                resetBaseLog(compareResultDoc);
                            }
                        }else {
                            log.info("getSyncploy detail error:{}",syncPloyDetailSnapshotBySnapshotId);
                        }
                    }
                    if(compareSyncField.syncType().equals(SyncCompareConstant.SYNC_DATA_MAIN)){
                        //设置ThreadLocal
                        BASE_LOG.set(compareResultDoc);
                    }
                }
            } catch (Exception e) {
                log.info("batch insert compareSyncDataMongoDao:logId:{},:msg:{}", TraceUtil.get(),e.getMessage());
            }
            // 继续执行方法
            proceed=joinPoint.proceed();
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }finally {
            // 获取方法签名
            Signature signature = joinPoint.getSignature();
            if (signature instanceof MethodSignature) {
                MethodSignature methodSignature = (MethodSignature) signature;

                // 获取返回类型
                Class<?> returnType = methodSignature.getReturnType();
                if(compareSyncField.syncType().equals(SyncCompareConstant.SYNC_DATA_MAIN)){
                    clear();//
                }
                // 根据返回类型进行处理
                if (returnType.equals(SyncDataContextEvent.class)) {
                    SyncDataContextEvent syncDataContextEvent = (SyncDataContextEvent) proceed;
                    // 对特定类型的返回结果进行处理
                    compareResultDoc.setOutputParamsMap(JSONObject.toJSONString(syncDataContextEvent));
                    if(compareResultDoc.getTenantId().equals("88466")||compareResultDoc.equals("89180")){
                        compareSyncDataMongoDao.batchInsert(compareResultDoc.getTenantId(), Lists.newArrayList(compareResultDoc));
                        log.info("batch insert compareSyncDataMongoDao:logId:{},:dataId:{}",compareResultDoc.getSyncLogId(),compareResultDoc.getSourceDataId());
                    }
                }
            }
        }
        return proceed;
    }
    private static CompareResultDoc getBaseLog() {
        CompareResultDoc baseLog = BASE_LOG.get();
        if (baseLog == null|| ObjectUtils.isEmpty(baseLog.getSourceDataId())) {
            baseLog = new CompareResultDoc();
            BASE_LOG.set(baseLog);
        }
        return baseLog;
    }

    public static void resetBaseLog(CompareResultDoc compareResultDoc) {
        BASE_LOG.set(compareResultDoc);

    }

    /**
     * 在线程结束前调用防止内存泄露
     */
    public static void clear() {
        BASE_LOG.remove();
    }
}
