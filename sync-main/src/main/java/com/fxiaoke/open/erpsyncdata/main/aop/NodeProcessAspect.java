package com.fxiaoke.open.erpsyncdata.main.aop;

import com.fxiaoke.open.erpsyncdata.common.exception.SyncStepException;
import com.fxiaoke.open.erpsyncdata.dbproxy.node.AbsNodeProcessor;
import com.fxiaoke.open.erpsyncdata.dbproxy.node.NodeHelper;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ErrorUtils;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.model.node.NodeContext;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ExceptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.aop.support.AopUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/12/29
 */
@Slf4j
@Aspect
@Order(-10)
@Component
public class NodeProcessAspect {


    // 定义一个切入点
    @Pointcut("execution(* com.fxiaoke.open.erpsyncdata.dbproxy.node.AbsNodeProcessor+.processMessage(..))")
    public void processMethod() {
    }

    // 定义一个切入点，匹配BaseClass的所有子类的targetMethod方法
    @Around("processMethod() && target(processor)")
    public Object aroundProcess(ProceedingJoinPoint joinPoint, AbsNodeProcessor<?> processor) throws Throwable {
        Object[] args = joinPoint.getArgs(); // 获取方法参数
        NodeContext<?> nodeContext = null;
        if (args.length > 0 && args[0] instanceof NodeContext<?>) {
            nodeContext = (NodeContext<?>) args[0];
        }
        //按道理不应该出现空的情况
        if (nodeContext == null) {
            return joinPoint.proceed();
        }
        NodeContext<?> oldContext = NodeHelper.setContext(nodeContext);
        before(processor, nodeContext);
        try {
            Object result = joinPoint.proceed();
            after(processor, nodeContext);
            return result;
        } catch (Throwable throwable) {
            //类似pg超时等异常，需要重试的数据。
            if (ErrorUtils.judgeException(throwable)) {
                LogIdUtil.setNeedRetry(true);
            }
            String className = AopUtils.getTargetClass(joinPoint.getTarget()).getSimpleName();
            //先打成warn日志观察下，防止error日志 过多。
            log.warn("sync step throw throwable,stepName:{}", className, throwable);
            //当步骤异常时，将终止同步
            throw new SyncStepException(String.format("%s step throw exception,%s", className, ExceptionUtil.getMessage(throwable)));
        } finally {
            //必定移除线程变量,,,并且将旧的，set进去。防止嵌套node，里层node remove导致外层的无法获取
            NodeHelper.removeContext();
            if (oldContext != null) {
                NodeHelper.setContext(oldContext);
            }
        }
    }

    private static void before(AbsNodeProcessor<?> processor, NodeContext<?> nodeContext) {
        //前处理
        try {
            //设置开始时间
            nodeContext.setStartTime(System.currentTimeMillis());
            processor.preProcess(nodeContext);
        } catch (Exception ex) {
            log.error("Exception caught in preProcess method: ", ex);
        }
    }

    private void after(AbsNodeProcessor<?> processor, NodeContext<?> nodeContext) {
        //非异常情况下才执行后动作
        try {
            processor.postProcess(nodeContext);
            if (nodeContext instanceof SyncDataContextEvent) {
                SyncDataContextEvent context = (SyncDataContextEvent) nodeContext;
                DataNodeNameEnum currentDataNodeName = context.getCurrentDataNodeName();
                if (currentDataNodeName != null && currentDataNodeName.isNeedStatCount() && nodeContext.getStartTime() != null) {
                    long cost = System.currentTimeMillis() - nodeContext.getStartTime();
                    //记录耗时, 每次数量都按1计算，代表一次执行，不代表数据量
                    NodeHelper.addCostRecord(currentDataNodeName, cost);
                }
            }
        } catch (Exception e) {
            log.error("Exception caught in postProcess method:", e);
        }
    }
}
