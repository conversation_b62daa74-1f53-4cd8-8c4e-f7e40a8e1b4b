package com.fxiaoke.open.erpsyncdata.main.crmevent;

import com.fxiaoke.open.erpsyncdata.common.constant.MqTagContants;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.monitor.TimePointRecorderStatic;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.data.DoWriteMqData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.data.TriggerPollingData;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.MessageQueueSelector;
import org.apache.rocketmq.common.message.Message;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class DataCenterMqProducer  {
    @Resource(name = "syncDataMqProducer")
    private AutoConfMQProducer producer;


    public String sendDoDispatcher(SyncDataContextEvent eventData) {//发送聚合mq
        ObjectData sourceData = eventData.getSourceData();
        TimePointRecorderStatic.asyncRecord(sourceData.getTenantId(), sourceData.getApiName(), sourceData.getId(), "sendDoDispatcher");
        return this.sendMq(MqTagContants.DO_DISPATCHER, eventData, sourceData.getId());
    }

    private MessageQueueSelector selector = (list, message, obj) -> {
        String key = (String) obj;
        Integer hashCode = key.hashCode();
        return list.get(Math.abs(hashCode) % list.size());
    };

    private String sendMq(String tags, Object data, String key) {
        try {
            byte[] body = JacksonUtil.toJson(data).getBytes("utf-8");
            Message message = new Message(producer.getDefaultTopic(), tags, body);
            if (StringUtils.isEmpty(key)) {
                return producer.send(message).getMsgId();
            }
            return producer.send(message, selector, key).getMsgId();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public String sendMqWithoutSelector(String topic, String tags, String keys, Object data) {
        try {
            byte[] body = JacksonUtil.toJson(data).getBytes("utf-8");
            Message message = new Message(topic, tags, keys, body);
            return producer.send(message).getMsgId();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
