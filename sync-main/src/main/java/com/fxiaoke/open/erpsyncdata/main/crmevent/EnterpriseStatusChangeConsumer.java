package com.fxiaoke.open.erpsyncdata.main.crmevent;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.common.mq.AbstractMqConsumer;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyManager;
import com.fxiaoke.open.erpsyncdata.main.crmevent.data.EnterpriseRunStatusEvent;
import com.fxiaoke.open.erpsyncdata.main.manager.SandboxEventManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * 这里的com.alibaba.rocketmq 要改为org.apache.rocketmq
 * */

@Slf4j
public class EnterpriseStatusChangeConsumer extends AbstractMqConsumer<EnterpriseRunStatusEvent> {
//erp-sync-listen-enterprise
    @Autowired
    private SandboxEventManager sandboxEventManager;
    @Autowired
    private SyncPloyManager syncPloyManager;
    @Autowired
    private I18NStringManager i18NStringManager;

    public EnterpriseStatusChangeConsumer(String rocketMQConsumerConfigName, String nameserver, String consumerGroup, String topic) {
        super(rocketMQConsumerConfigName, nameserver, consumerGroup, topic);
    }

    @Override
    public void processMessage(EnterpriseRunStatusEvent message) throws Throwable {
        if (message == null) {
            return;
        }
        if(message.getRunStatus()==5){
            log.info("enterpriseStatusChangeListener delete enterprise:{}",message);
            //运行状态：1：待开通； 2：已开通； 3、已作废；4：已停用；5：已删除；
            sandboxEventManager.deleteErpSettingNotJudgeSandbox(String.valueOf(message.getEnterpriseId()));
        }else if(message.getRunStatus()==4){
            log.info("enterpriseStatusChangeListener stop enterprise message:{}",message);
            syncPloyManager.disableAllStream(String.valueOf(message.getEnterpriseId()),
                    i18NStringManager.getByEi(I18NStringEnum.s972,message.getEnterpriseId()+""));
        }
    }

    /**
     * 转换为bean
     *
     * @param body 消息体
     * @return UnbindWeChat
     */
    private EnterpriseRunStatusEvent byte2Bean(byte[] body) {
        EnterpriseRunStatusEvent appAdminItemVo = new EnterpriseRunStatusEvent();
        try {
            appAdminItemVo.fromProto(body);
            return appAdminItemVo;
        } catch (Exception e) {
            log.error("pluginMessageListener convert sendPluginEvent bean failed.", e);
        }
        return null;
    }

}
