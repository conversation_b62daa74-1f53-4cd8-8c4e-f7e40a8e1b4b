package com.fxiaoke.open.erpsyncdata.main.crmevent;

import com.facishare.sandbox.listener.SandboxEvent;
import com.facishare.sandbox.module.CreateEvent;
import com.facishare.sandbox.module.DestroyEvent;
import com.facishare.sandbox.module.Enterprise;
import com.facishare.sandbox.module.Module;
import com.facishare.uc.api.model.enterprise.arg.GetSimpleEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetSimpleEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantEnvManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.main.manager.SandboxEventManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendAdminNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpSyncDataBackStageEnvironmentEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;

@Slf4j
public class SandboxEventTask extends SandboxEvent {
    @Autowired
    private SandboxEventManager sandboxEventManager;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private TenantEnvManager tenantEnvManager;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;

    @Override
    public void afterPropertiesSet() throws Exception {
        if (!ConfigCenter.ENABLE_CONSUME_SANDBOX_EVENT) {
            log.info("this env not consume sandbox event");
            return;
        }
        super.afterPropertiesSet();
    }

    //具体参考 http://wiki.firstshare.cn/pages/viewpage.action?pageId=75268819 配置定义
    public SandboxEventTask(String consumer) {
        super(consumer, Module.ErpSyncData);
        log.info("create sandbox event task,consumer:{}", consumer);
    }


    @Override
    public void destroy() throws Exception {
        if (!ConfigCenter.ENABLE_CONSUME_SANDBOX_EVENT) {
            log.info("this env not consume sandbox event");
            return;
        }
        super.destroy();
    }

    @Override
    protected boolean onSandboxCreated(String module, CreateEvent event) {
        if (!ConfigCenter.ENABLE_CONSUME_SANDBOX_EVENT) {
            log.info("copy erp sync config, disable consume,event{}", event);
            return false;
        }
        if ("All".equals(event.getType())) {
            // 全量复制
            Enterprise from = event.getFrom();
            Enterprise to = event.getTo();
            String fromTenantId = from.getEnterpriseId().toString();
            String toTenantId = to.getEnterpriseId().toString();
            try {
                sandboxEventManager.copyErpSyncConfig(fromTenantId, toTenantId);
                changeTenantEnv(to.getEnterpriseId());
            } catch (Exception e) {
                log.error("copy erp sync config exception,from:{},to{},", from, to, e);
                sendErrorSuperAdminNotice(to, fromTenantId, e.getMessage(), event.getType());
            }
            return true;
        } else if ("Data".equals(event.getType())) {
            try {
                // 只复制数据
                sandboxEventManager.copyErpSyncData(event.getFrom().getEnterpriseId().toString(), event.getTo().getEnterpriseId().toString());
                changeTenantEnv(event.getTo().getEnterpriseId());
            } catch (Exception e) {
                final Enterprise to = event.getTo();
                final String fromTenantId = event.getFrom().getEnterpriseId().toString();
                log.error("copy erp sync data exception,from:{},to{},", fromTenantId, to.getEnterpriseId().toString(), e);
                sendErrorSuperAdminNotice(to, fromTenantId, e.getMessage(), event.getType());
            }
            return true;
        }
        return true;
    }

    private void changeTenantEnv(Integer toTenantId) {
//        是foneshare环境才挪到gray
        GetSimpleEnterpriseDataResult result = enterpriseEditionService.getSimpleEnterpriseData(new GetSimpleEnterpriseDataArg(toTenantId, null));

        Optional.ofNullable(result)
                .map(GetSimpleEnterpriseDataResult::getEnterpriseData)
                .filter(data -> {
                    final Integer env = data.getEnv();
                    return env == null || env == 1 || env == 0;
                })
                //默认gray环境
                .ifPresent(env -> tenantEnvManager.changeTenantEnv(toTenantId.toString(), ErpSyncDataBackStageEnvironmentEnum.GRAY));
    }

    private void sendErrorSuperAdminNotice(final Enterprise to, final String fromTenantId, final String message, final String type) {
        notificationService.sendSuperAdminNotice(SendAdminNoticeArg.builder()
                .tenantId(fromTenantId)
                .msgTitle(i18NStringManager.getByEi(I18NStringEnum.s978,to.getEnterpriseId()+"") + type)
                .msg(i18NStringManager.getByEi(I18NStringEnum.s979,to.getEnterpriseId()+"") + to)
                .msg(i18NStringManager.getByEi(I18NStringEnum.s980,to.getEnterpriseId()+"") + message)
                .build());
    }

    @Override
    protected boolean onSandboxDestroy(String module, DestroyEvent event) {
        if (!ConfigCenter.ENABLE_CONSUME_SANDBOX_EVENT) {
            log.info("copy erp sync config, disable consume,event{}", event);
            return true;
        }
        String tenantId = event.getEnterpriseId().toString();
        if ("All".equals(event.getType())) {
            try {
                //全部销毁，一般用户体验帐号
                sandboxEventManager.deleteErpSyncConfig(tenantId);
            } catch (Exception e) {
                log.error("delete erp sync config exception,event{},", event, e);
                notificationService.sendSuperAdminNotice(SendAdminNoticeArg.builder()
                        .tenantId(tenantId)
                        .msgTitle(i18NStringManager.getByEi(I18NStringEnum.s981,tenantId))
                        .msg(i18NStringManager.getByEi(I18NStringEnum.s982,tenantId) + tenantId)
                        .build());
            }
            return true;
        } else if ("Data".equals(event.getType())) {
            //只销毁数据
            return true;
        }
        return true;
    }
}
